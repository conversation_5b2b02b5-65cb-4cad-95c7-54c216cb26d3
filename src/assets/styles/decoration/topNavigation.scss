// 店铺导航

@import "../mixins/mixins";
@import "../mixins/utils.scss";

@include b(topNavigation-item) {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 10px 0;
  align-items: center;



  &>li {
    flex: none;
    // width: 25%;
    text-align: center;

    margin: 0 15px;

    &.active {
      position: relative;
    }

    &.active::before {
      content: "";
      position: absolute;
      width: var(--underline-width, 50px); /* 默认 50px，可被父组件覆盖 */
      height: var(--underline-height, 3px);
      background-color: var(--underline-color, red);
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
    }
  }



}

@include b(topNavigation-item-form) {
  position: relative;
  border: 1px solid #eeeeee;
  padding: 10px;
  margin-bottom: 10px;

  @include b(avatar) {
    width: 48px;
    height: 48px;
    display: block;
  }

  @include b(avatar-uploader) {
    @include e(icon) {
      font-size: 28px;
      color: #8c939d;
      width: 48px;
      height: 48px;
      line-height: 48px;
      text-align: center;
    }

    @include b(el-upload) {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: #409eff;
      }
    }
  }
}