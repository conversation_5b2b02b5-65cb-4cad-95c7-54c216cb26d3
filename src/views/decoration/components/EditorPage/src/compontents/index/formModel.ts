/*
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-24 10:13:53
 * 2025-7-17
 */
import HomeSwiper from "../HomeSwiper/HomeSwiper";
import GoodSwiper from "../GoodSwiper/GoodSwiper";
import Goods from "../Goods/Goods";
import Search from "../Search/Search";
import AddressSet from "../AddressSet/AddressSet";
import TitleBar from "../TitleBar/TitleBar";
import BlankPaceholder from "../BlankPaceholder/BlankPaceholder";
import Separator from "../Separator/Separator";
import StoreNavigation from "../StoreNavigation/StoreNavigation";
import NavBar from "../NavBar/NavBar";
import CubeBox from "../CubeBox/CubeBox";
import RichText from "../RichText/RichText";
import BusinessSuper from "../../../../BusinessSuper/BusinessSuper";
import VideoCom from "../VideoCom/VideoCom";
import ImageCom from "../ImageCom/ImageCom";
import StoreInformationCom from "../StoreInformationCom/StoreInformationCom";
import StaffDisplay from "../StaffDisplay/StoreNavigation";
import TopNavigation from "../TopNavigation/TopNavigation";
import OperationCenterTop from "../OperationCenterTop/OperationCenterTop";
import PopupView from "../PopupView/PopupView";
import BuoyView from "../BuoyView/BuoyView";

class EdtiorFormData {
  HomeSwiper = HomeSwiper;
  
  Goods = Goods;

  Search = Search;
  AddressSet = AddressSet;

  TitleBar = TitleBar;

  BlankPaceholder = BlankPaceholder;

  Separator = Separator;

  StoreNavigation = StoreNavigation;

  NavBar = NavBar;

  CubeBox = CubeBox;

  RichText = RichText;

  BusinessSuper = BusinessSuper;

  ImageCom = ImageCom;

  VideoCom = VideoCom;

  GoodSwiper = GoodSwiper;

  StoreInformationCom = StoreInformationCom;

  StaffDisplay = StaffDisplay;
  
  TopNavigation = TopNavigation;

  OperationCenterTop = OperationCenterTop;

  PopupView = PopupView;
  
  BuoyView = BuoyView;

  [x: string]: any;
}
export default EdtiorFormData;
