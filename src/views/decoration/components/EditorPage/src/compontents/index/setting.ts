/*
 * @description: 抽离开源版本
 * @Author: chuy<PERSON><PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-23 16:01:22
 * 2025-7-17
 */
import HomeSwiper from "../HomeSwiper/Setting.vue";
import GoodSwiper from "../GoodSwiper/Setting.vue";
import Goods from "../Goods/Setting.vue";
import Search from "../Search/Setting.vue";
import AddressSet from "../AddressSet/Setting.vue";
import TitleBar from "../TitleBar/Setting.vue";
import BlankPaceholder from "../BlankPaceholder/Setting.vue";
import Separator from "../Separator/Setting.vue";
import StoreNavigation from "../StoreNavigation/Setting.vue";
import NavBar from "../NavBar/Setting.vue";
import CubeBox from "../CubeBox/Setting.vue";
import RichText from "../RichText/Setting.vue";
import BusinessSuper from "../../../../BusinessSuper/Setting.vue";
import ImageCom from "../ImageCom/Setting.vue";
import VideoCom from "../VideoCom/Setting.vue";
import StoreInformationCom from "../StoreInformationCom/Setting.vue";
import StaffDisplay from "../StaffDisplay/Setting.vue";
import TopNavigation from "../TopNavigation/Setting.vue";
import OperationCenterTop from "../OperationCenterTop/Setting.vue";
import PopupView from "../PopupView/Setting.vue";
import BuoyView from "../BuoyView/Setting.vue";
export default {
  HomeSwiper,
  Goods,
  Search,
  AddressSet,
  TitleBar,
  BlankPaceholder,
  Separator,
  StoreNavigation,
  NavBar,
  CubeBox,
  RichText,
  BusinessSuper,
  ImageCom,
  VideoCom,
  GoodSwiper,
  StoreInformationCom,
  StaffDisplay,
  TopNavigation,
  OperationCenterTop,
  PopupView,
  BuoyView,
};
