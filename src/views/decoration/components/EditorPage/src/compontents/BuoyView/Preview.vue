<!--
 * @description: 
 * @Author: chuyinlong
 * @Date: 2021-04-30 14:05:47
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-01 15:39:19
 * 123
-->
<template>
  <div class="BuoyView">
    <div  class="box" :style="{right: upLoadImg.right + 'px',}">
      <img :src="upLoadImg.img" alt=""  :style="{width: upLoadImg.width + 'px', height: upLoadImg.height + 'px', borderRadius: upLoadImg.borderRadius + '%'}"/>
      <div class="icons">
        <i class="el-icon-close"></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, PropSync, Watch } from "vue-property-decorator";
import ImageCom from "./BuoyView";
import VueDragResize from "vue-drag-resize";

@Component({
  components: {
    VueDragResize,
  },
})
export default class ImageComPreview extends Vue {
  @PropSync("formData", {
    type: Object,
    default() {
      return null;
    },
  })
  upLoadImg!: ImageCom;




}
</script>

<style lang="scss" scoped>
.BuoyView{
  display: flex;
  justify-content: flex-end;

  .box{
    position: relative;

    .icons{
      position: absolute;
      top: 0;
      left: 0;
				// background-color: rgba(255, 255, 255, 1.0);
      
    }
  }
}
</style>
