<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-23 18:04:16
-->
<template>
  <!-- 店铺导航 -->
  <div :style="'padding-top:' + formData.pagePadding + 'px;padding-bottom:' + formData.pagePadding + 'px;padding-left:' 
  + formData.pagePadding2 + 'px;padding-right:' + formData.pagePadding2 + 'px'"  class="">
    <ul class="topNavigation-item">
      <li v-for="(storeNavigationItem, index) in formData.storeNavigations" :key="index"
        :class="index == activeIndex ? 'active' : ''" :style="{
          '--underline-width': formData.width + 'px',
          '--underline-height': formData.height + 'px',
          '--underline-color': formData.color,
        }">
        <div :style="{ color: index == activeIndex ? formData.fontColorActive : storeNavigationItem.fontColor }"
          @click="setIndex(index)">
          {{ storeNavigationItem.navName }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import StoreNavigation from "./TopNavigation";

@Component
export default class StoreNavigationPreview extends Vue {
  @Prop()
  formData!: StoreNavigation;

  activeIndex = 0;

  underlineStyle = {
    width: "50px",
    height: "3px",
    color: "red",
  }

  setIndex(i) {
    this.activeIndex = i;
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/styles/decoration/topNavigation";


  /* 针对 Webkit 浏览器（Chrome, Safari, Edge） */
 .topNavigation-item::-webkit-scrollbar {
  height: 6px; /* 设置滚动条高度（水平滚动条的宽度） */
}

.topNavigation-item::-webkit-scrollbar-thumb {
  background: #8b8b8b; /* 滚动条滑块颜色 */
  border-radius: 6px; /* 滑块圆角 */
}
</style>
