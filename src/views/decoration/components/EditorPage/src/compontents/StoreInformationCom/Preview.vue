<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-23 13:29:08
 2025-7-18
-->
<template>
	<!-- 商品 -->
	<div class="goods__ponent-page">
		<div class="boxs" :style="pageStyle" style="overflow: hidden;">
			<div class="flex-center-center grid__name" :style="pageStyle__one">
				<div class="imgDiv"></div>
				<div class="grid__name__center">
					<div class="name">
						店铺名称
					</div>
					<div class="right">
						店铺分类 >
					</div>
				</div>

			</div>
			<div class="grid__border">
				<div class="time" :style="{ 'margin-left': formData.Padding + 'px' }">
					<div class="span">营业中</div> 周一至周日 12:00-24:00
				</div>
			</div>
			<div class=" navigation" :style="pageStyle__two">
				<div class="navigation__left">
					<div class="top">
						金龙路2号
					</div>
					<!-- <div class="bottom">
            距离总部基地地铁站200米
          </div> -->
				</div>
				<div class="navigation__right flex-between-center">
					<div class="top">
						<img src="./jtou.png" mode="">
						<div class="">
							导航
						</div>
					</div>
					<div class="top">
						<img src="./dhua.png" mode="">
						<div class="">
							电话
						</div>
					</div>
					<div class="top">
						<img src="./reservation2.png" mode="" >
						<div class="">
							预约
						</div>
					</div>
				</div>
			</div>

		</div>
	</div>
</template>

<script lang="ts">
	import { Vue, Component, Prop, Watch } from "vue-property-decorator";
	import Goods, { ICategoryItem, ISubFormGoods } from "./StoreInformationCom";
	import AddCart from '../../../../BusinessSuper/components/AddCart.vue';

	@Component({
		components: {
			AddCart
		},
	})
	export default class GoodsPreview extends Vue {
		@Prop()
		formData! : Goods;

		goodsList : number | ISubFormGoods[] = [];

		get pageStyle() {
			return {
				borderRadius: `${this.formData.checked1 ? this.formData.roundedPixels : '0'}px ${this.formData.checked2 ? this.formData.roundedPixels : '0'}px ${this.formData.checked3 ? this.formData.roundedPixels : '0'}px ${this.formData.checked4 ? this.formData.roundedPixels : '0'}px`,
				width: `${this.formData.width}px`,
				height: `${this.formData.height}px`,
				minHeight: "20px"
			};
		}
		get pageStyle__one() {
			return {
				paddingTop: `${this.formData.Padding}px`,
				paddingLeft: `${this.formData.Padding}px`,
				paddingRight: `${this.formData.Padding}px`,
				paddingBottom: '0px'
			};
		}
		get pageStyle__two() {
			return {
				paddingBottom: `${this.formData.Padding}px`,
				paddingLeft: `${this.formData.Padding}px`,
				paddingRight: `${this.formData.Padding}px`,
				paddingTop: '0px'
			};
		}
		// @Watch("formData", { deep: true })
		// handleFormDataChange() {
		//   this.getGoodsList();
		// }
	}
</script>

<style scoped lang="scss">
	.goods__ponent-page {
		display: flex;
		justify-content: center;
		background: #eae9ee;

		.boxs {
			background: #ffffff;

			.grid__name {
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.imgDiv {
					width: 45px;
					height: 45px;
					border-radius: 10px;
					background: #098Bec;
					margin-right: 10px;
				}
			}

			.grid__name__center {


				.name {
					color: #2b2b2b;
					font-size: 18px;
					font-weight: 700;
					text-align: left;
				}

				.right {
					font-size: 14px;
					text-align: left;
					margin-top: 5px;
				}
			}


			// }

			.grid__border {
				border-top: 5px solid #bbbbbb;
				border-bottom: 1px solid #bbbbbb;
				margin-top: 10px;

				.time {
					height: 35px;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.span {
						color: rgba(54, 207, 0, 1);
						margin-right: 32px;
						display: inline-block;
					}

				}

			}

			.navigation {
				display: flex;
				justify-content: space-between;

				.navigation__left {
					width: 230px;
					// margin-left: 30px;

					.top {
						margin-top: 10px;

					}

					.bottom {
						margin-top: 20px;
					}
				}

				.navigation__right {
					margin-top: 15px;
					width: 200px;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.top {
						display: flex;
						justify-content: flex-start;
						align-items: center;


						img {
							width: 15px;
							height: 15px;
						}
					}
				}
			}


		}
	}
</style>