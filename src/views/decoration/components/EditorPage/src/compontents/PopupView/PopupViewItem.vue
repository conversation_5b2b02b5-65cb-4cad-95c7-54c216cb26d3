<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-07 13:13:17
-->
<template>
  <!-- 店铺导航 -->
  <div class="storeNavigation-item-form">
    <el-form :model="formData" label-width="100px" style="marginTop:15px">

      <el-form-item label="会员权限设置" prop="name"></el-form-item>
      <el-form-item label="会员类型" prop="name">
        <el-select v-model="formData.memberTypeId" @change="changeMemberType" clearable placeholder="请选择">
          <el-option v-for="item in memberTypeList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="会员等级" prop="name">
        <el-select v-model="formData.memberLevelId" clearable placeholder="请选择">
          <el-option v-for="item in memberLevelList" :key="item.id" :label="item.memberLevel" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>

      <el-divider></el-divider>
      <el-form-item label="名称">
        <el-input v-model="formData.navName" :maxlength="6" placeholder="请输入名称" style="width: 220px;"></el-input>
      </el-form-item>

      <!-- <el-form-item label="字体颜色">
        <el-color-picker v-model="formData.fontColor"></el-color-picker>
      </el-form-item> -->
      <el-form-item label="图片">
        <upload-file :img-url.sync="formData.navIcon"></upload-file>
        <div class="el-upload__tip">
          尺寸建议57*57的PNG图片
        </div>
      </el-form-item>
      <el-form-item label="优惠券">
        <el-button type="primary" plain size="small" 
        @click="selectCoupon()" >+添加优惠券</el-button>
        <div style="margin-top: 10px;" v-if="formData.couponIdList.length">
        <!-- <div style="margin-top: 10px;" > -->
          <el-table :data="formData.couponIdList" ref="couponIdList" border>
            <el-table-column prop="option" label="操作" width="60">
              <template slot-scope="scope">
                <el-button type="text" style="color: red;" @click="deletePackageProducts(scope.$index)" >删除</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="couponName" label="优惠券名称" show-overflow-tooltip>
            </el-table-column>
          </el-table>
        </div>
      </el-form-item>

      <el-form-item label="跳转路径">
        <LinkSelect :link="link" :inner="true"></LinkSelect>
        <div v-if="link.type !== 6">
          <span style="color: #9797A1">{{ formData.linkName }}</span>
        </div>
      </el-form-item>
    </el-form>
    <img @click="delect(formData, itemIndex)"
      style="width: 35px;height: 35px;cursor: pointer;position: absolute;top: -5px;right: -5px;"
      class="bar_item_del_icon" src="@/assets/images/del.png" />

    <!-- 优惠券选择对话框 -->
    <ChooseCoupon ref="chooseCoupon" @handleCoupon="handleCouponSelect" />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import StoreNavigationItemTs from "./PopupViewTs";
import UploadFile from "@/views/decoration/components/NavBar/UploadFile.vue";
import LinkSelect from "@/components/LinkSelect";
import LinkSelectItem from "@/components/LinkSelect/src/components/LinkSelectItem";
import ChooseCoupon from "./ChooseCoupon.vue";
import {
  getMemberType,
  selectMemberLevelList,
} from "@/api/sign/index";
@Component({
  components: {
    UploadFile,
    LinkSelect,
    ChooseCoupon
  },
})
export default class StoreNavigationItemForm extends Vue {
  @Prop()
  formData!: StoreNavigationItemTs;

  @Prop({ default: 0 })
  itemIndex!: number;

  memberTypeList = []
  memberLevelList = []

  link: LinkSelectItem = {
    id: "",
    type: 0,
    name: "",
    url: "",
    append: "",
  };

  handleChange(file: { raw: any }) {
    this.formData.navIcon = URL.createObjectURL(file.raw);
  }

  @Watch("link", { deep: true })
  /** 点击链接选择 */
  selectLinkHandle(linkDataItem: LinkSelectItem) {
    /** 链接地址 */
    this.formData.linkUrl = linkDataItem.url;
    /** 链接名称 */
    this.formData.linkName = linkDataItem.name;
    /** 首页 链接append */
    this.formData.append = linkDataItem.append || "";
    /** 类型 type5为自定义页面 */
    this.formData.type = linkDataItem.type;
    this.formData.id = linkDataItem.id;
  }

  /**
   * @LastEditors: chuyinlong
   * @description: 删除店铺导航tab
   * @param {StoreNavigationItemTs} formData
   * @param {number} itemIndex
   */

  delect(formData: StoreNavigationItemTs, itemIndex: number) {
    this.$emit("onDelect", itemIndex);
  }

  mounted() {
    const formData = this.formData;
    this.link = {
      id: formData.id,
      type: Number(formData.type),
      name: formData.linkName,
      url: formData.linkUrl,
      append: formData.append,
    };

    this.getMemberType()

    if (this.formData.memberTypeId != null &&
      this.formData.memberTypeId != undefined &&
      this.formData.memberTypeId != ''
    ) {
      this.selectMemberLevelList(this.formData.memberTypeId)
    }
  }
  getMemberType() {
    getMemberType({ status: 1 }).then(res => {
      this.memberTypeList = res.data
    }).catch(err => {
      this.$message.error(err);
    })
  }
  changeMemberType() {
    this.formData.memberLevelId = ""
  }
  @Watch("formData.memberTypeId", { deep: true })
  changeFormData() {
    console.log("formData.memberTypeId", this.formData.memberTypeId);
    this.selectMemberLevelList(this.formData.memberTypeId)
  }
  selectMemberLevelList(memberTypeId: any) {
    selectMemberLevelList({ memberTypeId }).then(res => {
      this.memberLevelList = res.data

    }).catch(err => {
      this.$message.error(err)
    })
  }

  // 处理优惠券选择结果
  handleCouponSelect(coupons: any[]) {
    // this.formData.couponIdList = [];
    // this.formData.couponIdList.push(...coupons);
    this.$set(this.formData, 'couponIdList', coupons);

    console.log("coupons=", coupons)
    console.log("this.formData.couponIdList=", this.formData.couponIdList)
  }

  // 选择优惠券
  selectCoupon() {
    (this.$refs.chooseCoupon as any).openDialog();
  }

  deletePackageProducts(index) {
    console.log("tableData=", this.tableData)
    this.formData.couponIdList.splice(index, 1)

  }
}
</script>
