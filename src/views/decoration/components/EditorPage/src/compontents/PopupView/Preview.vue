<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-23 18:04:16
-->
<template>
  <!-- 店铺导航 -->
  <div>
    <div style="text-align: center;position: relative;">
      <img src="https://qiniu-app.qtshe.com/u391.png" alt="" style="width: 100%; height: 180px;" />
      <span style="position: absolute;top: 50%;left: 0;right: 0;font-size: 16px;">
        弹窗组件
      </span>
    </div>
    <el-dialog title="弹窗设置" :visible.sync="formData.dialogVisible" width="60%" :before-close="handleClose">

      <div class="box">
        <div class="left">
          <div class="left-box">
            <div class="top">
              <i class="el-icon-circle-close"></i>
            </div>
            <div class="conter">
              <el-carousel trigger="click" :autoplay="false" :height="formData.height + 'px'">
                <el-carousel-item v-for="(item, i) in formData.storeNavigations" :key="i">
                  <div class="img-box">
                    <img :src="item.navIcon" alt=""
                      :style="{ width: formData.width + '%', height: formData.height + 'px' ,borderRadius: formData.borderRadius + '%' }" />
                    <div class="youhui">
                      <youhuiCertificate v-for="(items, i) in item.couponIdList" :key="i" :color='color'
                         :list="items" class="youhuiCertificate">
                      </youhuiCertificate>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
            <div class="checkbox-box">
              <el-checkbox v-model="checked">不再显示</el-checkbox>
            </div>
          </div>
        </div>
        <div class="right">
          <SettingSet :formData="formData"></SettingSet>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="formData.dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="formData.dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import StoreNavigation from "./PopupView";
import SettingSet from "./SettingSet.vue";
import youhuiCertificate from "./youhuiCertificate.vue";
@Component({
  components: {
    SettingSet,
    youhuiCertificate
  }
})
export default class StoreNavigationPreview extends Vue {
  @Prop()
  formData!: StoreNavigation;

  checked = false;

  color = '#d63040';

  handleClose(done) {
    this.$confirm('确认关闭？')
      .then(_ => {
        done();
      })
      .catch(_ => { });
  }

  certificateImgs(backColor: string) {
    // this.formModel.backColor = backColor
    console.log('66666666666');
  }

}
</script>
<style lang="scss" scoped>
/deep/ .el-checkbox__inner {
  border-radius: 50% !important;
}

/deep/ .box .left-box .conter .el-carousel__item {
  background-color: rgba(0, 0, 0, 0) !important;
}

.box {
  display: flex;
  justify-content: space-around;

  .left {
    width: 390px;
    height: 800px;
    background-color: #ddd;
    border-radius: 10px;
    display: flex;
    justify-content: space-around;
    align-items: center;

    &-box {
      width: 100%;

      .top {
        display: flex;
        justify-content: flex-end;
        margin-right: 30px;
      }

      .conter {
        width: 100%;
        margin: 20px 0;

        .img-box {
          text-align: center;
          position: relative;
          background-color: rgba(0, 0, 0, 0);

          .youhui {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }

        img {
          height: 500px;
          width: 100%;
          margin: 0 auto;
        }

        .el-carousel__item:nth-child(2n) {
          background-color: #99a9bf;
        }

        .el-carousel__item:nth-child(2n+1) {
          background-color: #d3dce6;
        }
      }
    }

    .checkbox-box {
      text-align: center;
    }
  }

  .right {}
}
</style>
