<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-23 13:29:08
 2025-7-16
-->
<template>
  <!-- 运营中心 -->
  <div class="operationCenterTop" :style="{ padding: `${formData.pagePadding2}px ` + `${formData.pagePadding1}px`, }">
    <div class="backgourndTop" :style="{ backgroundImage: `url(${formData.backgroundImage})` }">
      <div class="" style="display: flex;">
        <img class="user--layout__image" :src="formData.img" />
        <div class="text-list" :style="{color: formData.fontColor }">
          <div style="margin-bottom: 10px;">
            <span>
              xxx运营中心
            </span>
            <span style="margin-left: 10px;">
              待发货数量：158
            </span>
          </div>
          <div style="margin-bottom: 10px;">加入时间：2025-05-18</div>
          <div style="" v-if="formData.showDate">到期时间：2026-05-19</div>
        </div>
      </div>
      <div>
        <div class="btn" :style="{color: formData.btnFontColor,  backgroundColor: formData.btnColor, }">
          认证></div>
        <div class="btn" :style="{color: formData.btnFontColor,  backgroundColor: formData.btnColor, }">
          升级></div>
        <div class="btn" :style="{color: formData.btnFontColor,  backgroundColor: formData.btnColor, }">
          发货></div>
        <div class="btn" :style="{color: formData.btnFontColor,  backgroundColor: formData.btnColor, }">
          分布></div>
      </div>
    </div>

  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import OperationCenterTop from "./OperationCenterTop";
import FontSize from "wangeditor/dist/menus/font-size";
@Component({
  components: {
  },
})
export default class OperationCenterTopPrediv extends Vue {
  @Prop()
  formData!: OperationCenterTop;


  mounted() {
  }
}
</script>

<style scoped lang="scss">
.operationCenterTop {

  .user--layout__image {
    flex: 0 0 58px;
    width: 58px;
    height: 58px;
    border-radius: 50%;
    box-shadow: 0 0 0 2px white;
  }

  .backgourndTop {

    background-size: 100% 100%;
    border-radius: 10px;
    color: #272643;
    background-repeat: no-repeat;
    display: flex;
    // align-items: center;
    justify-content: space-between;
    padding: 10px;

  }

  .text-list {
    margin-left: 10px;
    color: white;
    font-size: 12px;
  }

  .btn {
    color: white;
    background-color: #409EFF;
    border-radius: 36px;
    padding: 6px 14px;
    margin-bottom: 7px;
    line-height: 12px;
    font-size: 12px;

    &:last-child {
      margin-bottom: 0px;

    }
  }
}
</style>
