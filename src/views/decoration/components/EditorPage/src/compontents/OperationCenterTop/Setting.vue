<!--
 * @description: 抽离开源版本
 * @Author: chuy<PERSON><PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: vikingShip
 * @LastEditTime: 2021-08-25 16:03:04
 2025-7-15
-->
<template>
  <!-- 运营中心 -->
  <el-form :model="subForm" label-width="100px" class="goods__page--set">

    <el-form-item label="页面边距左右">
      <el-slider v-model="subForm.pagePadding1" :show-tooltip="false" :show-input="true" :max="30"></el-slider>
    </el-form-item>
    <el-form-item label="页面边距上下">
      <el-slider v-model="subForm.pagePadding2" :show-tooltip="false" :show-input="true" :max="30"></el-slider>
    </el-form-item>
    <el-form-item label="显示到期时间">
      <el-checkbox v-model="subForm.showDate"></el-checkbox>
    </el-form-item>
    <el-form-item label="按钮颜色">
      <el-color-picker v-model="subForm.btnColor"></el-color-picker>
    </el-form-item>
    <el-form-item label="按钮字体颜色">
      <el-color-picker v-model="subForm.btnFontColor"></el-color-picker>
    </el-form-item>
    <el-form-item label="字体颜色">
      <el-color-picker v-model="subForm.fontColor"></el-color-picker>
    </el-form-item>
    <el-form-item label="背景图片">
      <upload-file :img-url.sync="subForm.backgroundImage"></upload-file>
      <div class="el-upload__tip">
        <!-- 尺寸建议57*57的PNG图片 -->
      </div>
    </el-form-item>
  </el-form>
</template>

<script lang="ts">
import OperationCenterTop from "./OperationCenterTop";
import { Vue, Component, Ref, Watch, PropSync } from "vue-property-decorator";
import UploadFile from "@/views/decoration/components/NavBar/UploadFile.vue";

@Component({
  components: {
    UploadFile
  },
})
export default class OperationCenterTopForm extends Vue {
  @PropSync("formData", {
    type: Object,
    default() {
      return {};
    },
  })
  subForm!: OperationCenterTop;



  @Watch("subForm", { deep: true })
  handleFormDataChange() {
    if (!this.subForm.firstCatList) this.$set(this.subForm, "firstCatList", []);
    if (!this.subForm.currentCategoryId === undefined)
      this.$set(this.subForm, "currentCategoryId", "-1");
  }


}
</script>
<style lang="scss"></style>
