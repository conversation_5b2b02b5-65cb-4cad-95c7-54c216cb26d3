<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-07 13:13:17
-->
<template>
  <!-- 职员导航 -->
  <div class="storeNavigation-item-form">
    <el-form :model="formData" label-width="100px" style="marginTop:15px">
      <el-form-item label="会员权限设置" prop="name"></el-form-item>
      <el-form-item label="会员类型" prop="name">
        <el-select v-model="formData.memberTypeId" @change="changeMemberType" clearable placeholder="请选择">
          <el-option v-for="item in memberTypeList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="会员等级" prop="name">
        <el-select v-model="formData.memberLevelId" clearable placeholder="请选择">
          <el-option v-for="item in memberLevelList" :key="item.id" :label="item.memberLevel" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>

      <el-divider></el-divider>
      <el-form-item label="职员">
        <el-select v-model="formData.empFullId" placeholder="请选择" @change="changeSelect">
          <el-option v-for="item in options" :key="item.id" :label="item.empFullName" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="字体颜色">
        <el-color-picker v-model="formData.fontColor"></el-color-picker>
      </el-form-item>
      <!-- <el-form-item label="图标">
        <upload-file :img-url.sync="formData.navIcon"></upload-file>
        <div class="el-upload__tip">
          尺寸建议57*57的PNG图片
        </div>
      </el-form-item> -->

      <el-form-item label="跳转路径">
        <LinkSelect :link="link"></LinkSelect>
        <div v-if="link.type !== 6">
          <span style="color: #9797A1">{{ formData.linkName }}</span>
        </div>
      </el-form-item>
    </el-form>
    <img @click="delect(formData, itemIndex)"
      style="width: 35px;height: 35px;cursor: pointer;position: absolute;top: -5px;right: -5px;"
      class="bar_item_del_icon" src="@/assets/images/del.png" />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import StoreNavigationItemTs from "./StoreNavigationItemTs";
import UploadFile from "@/views/decoration/components/NavBar/UploadFile.vue";
import LinkSelect from "@/components/LinkSelect";
import LinkSelectItem from "@/components/LinkSelect/src/components/LinkSelectItem";
import { pageEmployeeList } from "@/api/employee/employee.ts";
import {
  getMemberType,
  selectMemberLevelList,
} from "@/api/sign/index";
@Component({
  components: {
    UploadFile,
    LinkSelect,
  },
})
export default class StoreNavigationItemForm extends Vue {
  @Prop()
  formData!: StoreNavigationItemTs;

  @Prop({ default: 0 })
  itemIndex!: number;

  link: LinkSelectItem = {
    id: "",
    type: 0,
    name: "",
    url: "",
    append: "",
  };

  options = []
  memberTypeList = []
  memberLevelList = []

  value = ''

  handleChange(file: { raw: any }) {
    this.formData.navIcon = URL.createObjectURL(file.raw);
  }

  changeSelect(val) {
    console.log(val);
    let option = this.options.filter(item => item.id == val)[0];
    console.log("option=", option);


    this.formData.navName = option.empFullName
    this.formData.navIcon = option.employeeImageUrl
    if (option.positionList && option.positionList.length) {
      this.formData.clerkName = option.positionList[0].positionName
    }


  }


  pageEmployeeList() {
    const param = {
      current: 1,
      size: 9999,
    }
    pageEmployeeList(param).then(res => {
      this.options = res.data.list
    }).catch(err => {
      this.$message.error(err)
    })
  }

  @Watch("link", { deep: true })
  /** 点击链接选择 */
  selectLinkHandle(linkDataItem: LinkSelectItem) {
    /** 链接地址 */
    this.formData.linkUrl = linkDataItem.url;
    /** 链接名称 */
    this.formData.linkName = linkDataItem.name;
    /** 首页 链接append */
    this.formData.append = linkDataItem.append || "";
    /** 类型 type5为自定义页面 */
    this.formData.type = linkDataItem.type;
    this.formData.id = linkDataItem.id;
  }

  /**
   * @LastEditors: chuyinlong
   * @description: 删除店铺导航tab
   * @param {StoreNavigationItemTs} formData
   * @param {number} itemIndex
   */

  delect(formData: StoreNavigationItemTs, itemIndex: number) {
    this.$emit("onDelect", itemIndex);
  }

  mounted() {
    const formData = this.formData;
    this.link = {
      id: formData.id,
      type: Number(formData.type),
      name: formData.linkName,
      url: formData.linkUrl,
      append: formData.append,
    };
    this.pageEmployeeList();

    this.getMemberType()

    if (this.formData.memberTypeId != null &&
      this.formData.memberTypeId != undefined &&
      this.formData.memberTypeId != ''
    ) {
      this.selectMemberLevelList(this.formData.memberTypeId)
    }
  }
  getMemberType() {
    getMemberType({ status: 1 }).then(res => {
      this.memberTypeList = res.data
    }).catch(err => {
      this.$message.error(err);
    })
  }
  changeMemberType() {
    this.formData.memberLevelId = ""
  }
  @Watch("formData.memberTypeId", { deep: true })
  changeFormData() {
    console.log("formData.memberTypeId", this.formData.memberTypeId);
    this.selectMemberLevelList(this.formData.memberTypeId)
  }
  selectMemberLevelList(memberTypeId: any) {
    selectMemberLevelList({ memberTypeId }).then(res => {
      this.memberLevelList = res.data

    }).catch(err => {
      this.$message.error(err)
    })
  }
}
</script>
