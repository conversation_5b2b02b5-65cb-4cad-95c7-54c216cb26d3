<template>
  <div class="multi-upload-container">
    <!-- 多图上传核心区域 -->
    <el-upload action="" list-type="picture-card" :multiple="true" :file-list="fileList" :limit="limit"
      :on-exceed="handleExceed" :before-upload="beforeUpload" :on-remove="handleRemove" :http-request="uploadImage"
      :disabled="disabled"
      :class="{ 'hide-upload': hideUploadButton }"
      >
      <i class="el-icon-plus"></i>
      <div slot="tip" class="el-upload__tip">
        <slot name="tip"></slot>
      </div>
    </el-upload>

    <!-- 恢复默认按钮（可选） -->
    <el-button v-if="defaultImgs.length" @click="resetToDefault" size="small">
      恢复默认
    </el-button>
  </div>
</template>

<script>
import { upLoad } from "@/api"; // 替换为你的上传API

export default {
  name: "MultiUploadFile",
  props: {
    value: { type: Array, default: () => [] }, // v-model 绑定的图片URL数组
    limit: { type: Number, default: 5 },      // 最大上传数量
    size: { type: Number, default: 1 },       // 单文件大小限制（MB）
    disabled: { type: Boolean, default: false }, // 是否禁用
    defaultImgs: { type: Array, default: () => [] } // 默认图片数组
  },
  data() {
    return {
      fileList: [] // 内部维护的文件列表
    };
  },
  watch: {
    // 监听外部数据变化，同步到 fileList
    value: {
      immediate: true,
      handler(urls) {
        this.fileList = urls.map(url => ({ url }));
        console.log("watch=",urls)
      }
    }
  },
  computed: {
    // 新增：是否隐藏上传按钮
    hideUploadButton() {
      return this.fileList.length >= this.limit;
    }
  },
  methods: {
    // 上传文件
    async uploadImage({ file }) {
      console.log("file=",file);
      
      try {
        const res = await upLoad({file});
        this.fileList.push({ url: res.data });
        this.emitUpdate();
      } catch (err) {
        this.$message.error("上传失败");
      }
    },

    // 文件上传前的校验
    beforeUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLtSize = file.size / 1024 / 1024 < this.size;

      if (!isImage) this.$message.error("只能上传图片");
      if (!isLtSize) this.$message.error(`图片大小不能超过 ${this.size}MB`);

      return isImage && isLtSize;
    },

    // 移除文件
    handleRemove(_, fileList) {
      this.fileList = fileList;
      this.emitUpdate();
    },

    // 超出限制提示
    handleExceed() {
      this.$message.warning(`最多只能上传 ${this.limit} 张图片`);
    },

    // 恢复默认图片
    resetToDefault() {
      this.fileList = this.defaultImgs.map(url => ({ url }));
      this.emitUpdate();
    },

    // 向父组件更新数据
    emitUpdate() {
      this.$emit("input", this.fileList.map(item => item.url));
      console.log("input",this.fileList.map(item => item.url))
    }
  }
};
</script>

<style scoped>
.multi-upload-container {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

/* 调整上传卡片样式 */
::v-deep .el-upload--picture-card {
  width: 100px;
  height: 100px;
  line-height: 110px;
}
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
  line-height: 110px;
}
::v-deep .el-upload-list--picture-card .el-upload-list__item .el-icon-check, .el-upload-list--picture-card .el-upload-list__item .el-icon-circle-check {
  position: absolute;
  top: 1px;
  left: 14px;
}
/* 动态控制上传按钮隐藏 */
.hide-upload ::v-deep .el-upload--picture-card {
  display: none;
}
</style>