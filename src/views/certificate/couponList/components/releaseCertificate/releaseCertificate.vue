<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-01 15:47:16
-->
<template>
	<div class="goodForm">

		<el-form :model="formModel" :rules="formRules" ref="formModel" label-width="100px" :disabled="disabled">

			<div class="baseMsg">
				<el-form-item label="优惠券名称" prop="couponName">
					<el-input v-model="formModel.couponName" :maxlength="128" style="width: 550px"
						placeholder="请输入优惠券名称"></el-input>
				</el-form-item>
				<el-form-item label="优惠券面额" prop="promotion">
					<el-input v-model="formModel.promotion" style="width: 550px" maxlength="30"
						placeholder="请输入优惠券面额"></el-input>
				</el-form-item>
				<el-form-item label="显示时间" prop="time">
					<div style="display: flex;align-items: center;justify-content: flex-start;">
						<el-date-picker v-model="formModel.displayStartTime" type="datetime" style="width:200px;"
							prefix-icon="el-icon-alarm-clock" placeholder="请选择开始时间" @change="chooseTime">
						</el-date-picker>
						<i style="margin: 0 10px;font-size: 30px;color: rgb(187, 187, 187);" class="el-icon-minus"></i>
						<el-date-picker v-model="formModel.displayEndTime" type="datetime" style="width:200px;"
							prefix-icon="el-icon-alarm-clock" placeholder="请选择开始时间" @change="chooseTimeEad">
						</el-date-picker>
					</div>
				</el-form-item>
				<el-form-item label="订单满" prop="fullAmount">
					<el-input v-model="formModel.fullAmount" style="width: 150px" maxlength="30"
						placeholder="请输入金额"></el-input>
					<span class="useableTimes__ci">元可使用</span>
				</el-form-item>
				<!-- 到期类型:100->指定时间;101->购买之日起计算; -->
				<el-form-item label="有效期" prop="times">
					<div style="display: flex;align-items: center;justify-content: flex-start;">
						<el-date-picker v-model="formModel.startTime" type="datetime" style="width:200px;"
							prefix-icon="el-icon-alarm-clock" placeholder="请选择日期" @change="chooseStartTime">
						</el-date-picker>
						<i style="margin: 0 10px;font-size: 30px;color: rgb(187, 187, 187);" class="el-icon-minus"></i>
						<el-date-picker v-model="formModel.endTime" type="datetime" style="width:200px;"
							prefix-icon="el-icon-alarm-clock" placeholder="请选择日期" @change="chooseEndTime">
						</el-date-picker>
					</div>
				</el-form-item>
				<el-form-item label="领取次数" prop="receiveTimes">
					<el-input v-model="formModel.receiveTimes" style="width: 550px" maxlength="30"
						placeholder="请输入领取次数"></el-input>
				</el-form-item>
				<el-form-item label="优惠券类型" prop="couponType">
					<el-select v-model="formModel.couponType" placeholder="优惠券类型" @change="changeCouponType">
						<el-option v-for="item in couponTypeList" :key="item.id" :label="item.label"
							:value="item.id"></el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="优惠商品" prop="products" v-if="showGoods">
					<el-button type="primary" plain size="small" @click="addTemPackage">+添加商品</el-button>
					<div class="valueName">
						<el-table :data="formModel.products" ref="inventorySumTable">
							<el-table-column prop="option" label="操作" width="140">
								<template slot-scope="scope">
									<el-button type="text" style="color: red;"
										@click="deletePackageProducts(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
							<el-table-column prop="productName" label="商品名称" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="specs" label="商品规格" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="price" label="实售价" width="180" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</div>

				</el-form-item>

				<el-form-item label="优惠品类" prop="categorys" v-if="showCategorys">
					<el-button type="primary" plain size="small" @click="addCategorys">+添加品类</el-button>
					<div class="valueName">
						<el-table :data="formModel.categorys" ref="inventorySumTable">
							<el-table-column prop="option" label="操作" width="140">
								<template slot-scope="scope">
									<el-button type="text" style="color: red;"
										@click="deleteCategorys(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
							<el-table-column prop="modeName" label="商品专区" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="categoryParentName" label="商品一级分类" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="categoryName" label="商品二级分类" width="180" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</div>
				</el-form-item>

				<el-form-item label="指定发券对象" prop="grantType">
					<el-select v-model="formModel.grantType" placeholder="请选择指定发券对象" @change="changeGrantType">
						<el-option v-for="item in this.grantTypeList" :key="item.id" :label="item.label"
							:value="item.id"></el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="标签客户" prop="accounts" v-if="showTag">
					<el-button type="primary" plain size="small" @click="addTags">+添加标签</el-button>
					<div class="valueName">
						<el-table :data="formModel.accounts" ref="inventorySumTable">
							<el-table-column prop="option" label="操作" width="140">
								<template slot-scope="scope">
									<el-button type="text" style="color: red;"
										@click="deleteAccount(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
							<el-table-column prop="tagName" label="标签名称" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="accountNum" label="客户数量" width="180" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</div>
				</el-form-item>
				<el-form-item label="会员等级" prop="accounts" v-if="showMember">
					<el-select multiple v-model="formModel.memberLevels" placeholder="会员等级">
						<el-option v-for="item in this.memberLevelList" :key="item.id" :label="item.memberLevel"
							:value="item.id"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="指定客户" prop="accounts" v-if="showAccount">
					<el-button type="primary" plain size="small" @click="addAccounts">+添加客户</el-button>
					<div class="valueName">
						<el-table :data="formModel.accounts" ref="inventorySumTable">
							<el-table-column prop="option" label="操作" width="120">
								<template slot-scope="scope">
									<el-button type="text" style="color: red;"
										@click="deleteAccount(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
							<el-table-column prop="nikeName" label="客户昵称" width="100" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="phone" label="客户电话号码" width="100" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="memberLevel" label="客户会员等级" width="100" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="tagName" label="标签名称" width="100" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</div>
				</el-form-item>
				<ChooseGoods ref="chooseGoods" @handleGoods="handleGoods">
				</ChooseGoods>
				<ChooseCategory ref="chooseCategory" @handleCategorys="handleCategorys">
				</ChooseCategory>
				<ChooseTag ref="chooseTag" @handleTag="handleTag">
				</ChooseTag>
				<ChooseAccount ref="chooseAccount" @handleAccount="handleAccount">
				</ChooseAccount>
				<el-form-item label="备注" prop="remark">
					<el-input v-model="formModel.remark" style="width: 550px" placeholder="填写您的备注内容"></el-input>
				</el-form-item>
				<el-form-item label="弹窗提醒" prop="isAlert">
					<el-select v-model="formModel.isAlert" placeholder="请选择">
						<el-option :value="true" label="是"></el-option>
						<el-option :value="false" label="否"></el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="样板展示" prop="times">
					<div style="display: flex;align-items: center;justify-content: flex-start;">
						<youhuiCertificate :widePicImg='widePic' :color='color1' @certificateImg="certificateImgs">
						</youhuiCertificate>
						<div class="upload">
							{{ color1 }}
							<el-color-picker v-model="color1"></el-color-picker>
						</div>
					</div>
				</el-form-item>
				<!-- <el-form-item label="参与商户" prop="remark" >
					<el-radio v-model="formModel.shopFlag" :label="0"><el-button size="mini">所有商户</el-button></el-radio>
				</el-form-item>
				<el-form-item label="" prop="remark">
					<el-radio v-model="formModel.shopFlag" :label="1">
						<el-button size="mini" @click="dialogShow"
							style="background:#07CAD5 ;color: #ffffff ;">自选商户</el-button></el-radio>
				</el-form-item> -->
				<el-form-item label="参与商户" prop="remark" v-if="mainFlag">
					<el-radio v-model="formModel.shopFlag" :label="0"><el-button size="mini">所有商户</el-button></el-radio>
				</el-form-item>
				<el-form-item label="" prop="remark" v-if="mainFlag">
					<el-radio v-model="formModel.shopFlag" :label="1">
						<el-button size="mini" @click="dialogShow"
							style="background:#07CAD5 ;color: #ffffff ;">自选商户</el-button></el-radio>
				</el-form-item>
			</div>
		</el-form>
		<div class="primary__bottom">
			<el-button @click="editSecUnit" v-if="!disabled">取 消</el-button>
			<el-button type="primary" @click="submitForm()" v-if="!disabled">提交</el-button>
			<el-button @click="newly" v-if="disabled">返 回</el-button>
		</div>
		<el-dialog :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
			<el-form ref="form" :model="searchType" label-width="90px">
				<el-row>
					<el-col :span="12">
						<el-form-item label="商家名称">
							<el-input v-model="searchType.name" placeholder="请输入商家名称" style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系人电话">
							<el-input v-model="searchType.phone" placeholder="请输入联系人电话"
								style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="商家类型">
							<el-cascader v-model="name" :props="props" :options="options"
								@change="handleChange"></el-cascader>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系人名称">
							<el-input v-model="searchType.contacts" placeholder="请输入联系人名称"
								style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-button @click="getShopList" type="primary" size="mini" round>搜索</el-button>
					</el-col>
				</el-row>
				<div style="border: 1px solid #ccc;width: 100%;margin: 20px 0;"></div>
				<el-row>
					<el-col :span="24">
						<el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
							:row-key="getRowKeys" @selection-change="handleSelectionChange">
							<el-table-column type="selection" :reserve-selection="true">
							</el-table-column>
							<el-table-column label="序号" type="index">
							</el-table-column>
							<el-table-column width="55">
							</el-table-column>
							<el-table-column label="商家名称" width="160">
								<template slot-scope="scope">{{ scope.row.name }}</template>
							</el-table-column>
							<el-table-column prop="contacts" label="联系人名称" width="120">
							</el-table-column>
							<el-table-column prop="phone" label="联系人电话" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="categoryName" label="商家类型" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</el-col>
				</el-row>
			</el-form>
			<!-- 设置分类 -->
			<div class="listBottom">
				<PageManage :pageSize="searchType.size" :pageNum="searchType.current" :total="total"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"
					style="margin-top: 0px">
				</PageManage>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="primaryButton">确 定</el-button>
			</span>
		</el-dialog>

	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { AddSubmitFormType, partnerListType, formModelType } from "./newGoodType";
import { readFile, advanceGetImage } from "@/store/modules/voidImg";
import { ElUploadInternalFileDetail } from "element-ui/types/upload";
import PageManage from "@/components/PageManage.vue";
import { upLoad } from "@/api/index";
import DateUtil from "@/store/modules/date";
import ChooseGoods from "./ChooseGoods";
import ChooseCategory from "./ChooseCategory";
import ChooseTag from "./ChooseTag";
import ChooseAccount from "./ChooseAccount";
import { SearchState, SearchKeyType } from "../searchType";
import { getIdCoupon, addCoupon, editCoupon, getAllShops, getShopList, getMainFlag } from "@/api/certificateApi/certificateApi"
import youhuiCertificate from "./youhuiCertificate";
import {
	getMemberLevelList
} from "@/api/customer/customer";
@Component({
	components: {
		PageManage,
		youhuiCertificate,
		ChooseGoods,
		ChooseCategory,
		ChooseTag,
		ChooseAccount
	}
})
export default class NewGoodFormOpints extends Vue {
	@Prop({})
	from!: string;
	color1 = ''
	/////////////////////
	// 存放数据的对象
	formModel = { shopFlag: 0, products: [], categorys: [], accounts: [], memberLevels: [], isAlert: true } as formModelType;
	/** 基本信息验证 */
	formRules = {
		name: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
		goodsCode: [{ required: true, message: "请输入商品编码", trigger: "blur" }],
		points: [{ required: true, message: "需要用多少积分兑换本商品", trigger: "blur" }, { required: true, message: "需要用多少现金兑换本商品", trigger: "blur" }],
		amount: [{ required: true, message: "请输入总数量", trigger: "blur" }],
		showCategoryIds: [
			{ required: true, message: "请选择分类", trigger: "blur" }
		]
	};
	tableData = []
	selectionList = []
	commodityCode = "";
	createTime = ''
	showRegion = false;
	shopTicketList = []
	widePic = ''
	dialogVisible = false;
	showGoods = false
	showTag = false
	showMember = false
	showCategorys = false
	showAccount = false
	/** 搜索类型 */
	couponTypeList = [
		{
			id: 0,
			label: "普通券",
		},
		{
			id: 1,
			label: "新人券",
		},
		{
			id: 2,
			label: "商品优惠券",
		},
		{
			id: 3,
			label: "品类优惠券",
		},
		{
			id: 4,
			label: "满返券",
		},
	];
	grantTypeList = [
		{
			id: 0,
			label: '全部客户',
		},
		{
			id: 1,
			label: '标签客户',
		},
		{
			id: 2,
			label: '会员等级',
		},
		{
			id: 3,
			label: '指定客户',
		},
	]
	// searchType : SearchKeyType = {};
	searchType = {
		current: 1,
		size: 10
	} as SearchKeyType;

	name = [];
	options = []
	props = {
		expandTrigger: 'hover',
		value: 'shopsCategoryId',
		label: 'name',
		children: 'shopsCategoryVos'
	}
	total = 0;
	disabled = false;
	partnerList: Array<partnerListType> = []
	multipleSelection = []
	memberLevelList = []

	mainFlag = 0

	mounted() {
		this.getIdCoupon();
		this.getMemberLevelList();
		this.getMainFlag();
		getAllShops({}).then((res) => {
			this.options = res.data
		}).catch((err) => {
			this.$message.error(err)
		})
	}

	getMainFlag() {
		getMainFlag(null).then((res) => {
			this.mainFlag = res.data
			console.log("getMainFlag=",res);
		}).catch((err) => {
			this.$message.error(err)
		})
	}

	getMemberLevelList() {
		getMemberLevelList({}).then((res) => {
			this.memberLevelList = res.data
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	getRowKeys(row) {
		return row.id //唯一性
	}
	handleClose(done: any) {
		done();
	}
	/**选择商家信息 */
	handleSelectionChange(val) {
		console.log(val);
		this.selectionList = val;
	}
	changeCouponType(val) {
		if (val == 2) {
			this.showGoods = true
		} else {
			this.showGoods = false
			this.formModel.products = []
		}
		if (val == 3) {
			this.showCategorys = true
		} else {
			this.showCategorys = false
			this.formModel.categorys = []
		}

	}

	changeGrantType(val) {
		this.formModel.accounts = []
		this.formModel.memberLevels = []
		this.showTag = false
		this.showMember = false
		this.showAccount = false
		if (val == 1) {
			this.showTag = true
		}
		if (val == 2) {
			this.showMember = true
		}
		if (val == 3) {
			this.showAccount = true
		}
	}
	/**点击自选商户的确定按钮 */
	primaryButton() {
		let dataList = this.selectionList;
		let ids = [];
		dataList.forEach(data => {
			let arr = {}
			arr.shopsPartnerId = data.id
			ids.push(arr);
		})
		this.formModel.partnerList = ids
		this.formModel.shopFlag = 1
		this.dialogVisible = false
		//重置复选选项
		this.selectionList = []
		this.$refs.multipleTable.clearSelection();
	}
	handleGoods(val) {
		this.formModel.products = val
	}
	handleCategorys(val) {
		this.formModel.categorys = val
	}
	handleTag(val) {
		if (this.formModel.grantType == 1) {
			val.forEach(element => {
				if (element.tagId != null && element.tagId != undefined && element.tagId != '') {
					element.sourceId = element.tagId
				}
			});
		}
		this.formModel.accounts = val
	}
	handleAccount(val) {
		if (this.formModel.grantType == 3) {
			val.forEach(element => {
				if (element.userId != null && element.userId != undefined && element.userId != '') {
					element.sourceId = element.userId
				}
			});
		}
		this.formModel.accounts = val
	}
	dialogShow() {
		this.getShopList()
		this.dialogVisible = true;
		let partnerList = this.formModel.partnerList

		this.$nextTick(function () {
			if (partnerList != null && partnerList != undefined && partnerList.length > 0) {
				partnerList.forEach(data => {
					let row = {};
					row.id = data.shopsPartnerId
					this.$refs.multipleTable.toggleRowSelection(row, true);
				})
			}
		})

	}



	/**
	 * 上传图片
	 */
	async addUploadProductImg(file: ElUploadInternalFileDetail) {
		const whiteList = ["image/jpeg", "image/jpg", "image/png"];

		const isLt1M = file.size < 1 * 1024 * 1024;
		if (!whiteList.includes(file.raw.type)) {
			this.$message.error("上传文件只能是 JPG或PNG 格式!");
			return;
		}
		if (!isLt1M) {
			this.$message.error("上传文件大小不能超过 1MB!");
			return;
		}

		const res = await upLoad({
			file: file.raw
		});
		this.widePic = res.data;
		this.formModel.backPic = res.data;
		console.log('eeeeeeee上传大图片', this.widePic);
		// this.productImgList.push({
		//   img: res.data
		// });
	}
	certificateImgs(backColor: string) {
		this.formModel.backColor = backColor
		console.log('66666666666', this.widePic, this.formModel);
	}

	handleChange(value: any) {
		this.searchType.shopsCategoryId = value[1]
	}
	/**
	 * 显示开始时间
	 * @param data
	 */
	chooseTime(data: any) {
		this.formModel.displayStartTime = data ? this.dateConversion(data) : "";
	}
	/**
	 * 显示结束时间
	 * @param data
	 */
	chooseTimeEad(data: any) {
		this.formModel.displayEndTime = data ? this.dateConversion(data) : "";
		if (this.formModel.displayStartTime > this.formModel.displayEndTime) {
			this.$message.error("结束时间不能小于开始时间");
			this.formModel.displayEndTime = "";
		}
	}
	addTemPackage() {
		this.$refs.chooseGoods.searchType = {
			productName: "",
			showCategoryId: "",
			status: "1",
		}
		console.log("this.formModel.products", this.formModel.products);
		this.$refs.chooseGoods.products = this.formModel.products
		this.$refs.chooseGoods.searchGoods();
	}
	addCategorys() {
		this.$refs.chooseCategory.searchType = {
			modeName: "",
			categoryParentName: "",
			categoryName: "",
		}
		this.$refs.chooseCategory.categorys = this.formModel.categorys
		this.$refs.chooseCategory.searchCategory();

	}
	addTags() {
		this.$refs.chooseTag.searchType = {
			tagName: "",
		}
		this.$refs.chooseTag.tag = this.formModel.accounts
		this.$refs.chooseTag.searchTag();
	}
	addAccounts() {
		this.$refs.chooseAccount.searchType = {
			nikeName: "",
			phone: "",
			memberLevel: "",
			tagName: "",
		}
		this.$refs.chooseAccount.account = this.formModel.accounts
		this.$refs.chooseAccount.searchAccount();
	}
	deletePackageProducts(index) {
		this.formModel.products.splice(index, 1);
	}
	deleteCategorys(index) {
		this.formModel.categorys.splice(index, 1);
	}
	deleteAccount(index) {
		this.formModel.accounts.splice(index, 1);
	}
	/**
	 * 有效期开始时间
	 * @param data
	 */
	chooseStartTime(data: any) {
		this.formModel.startTime = data ? this.dateConversion(data) : "";
	}
	/**
	 * 有效期结束时间
	 * @param data
	*/
	chooseEndTime(data: any) {
		this.formModel.endTime = data ? this.dateConversion(data) : "";
		if (this.formModel.startTime > this.formModel.endTime) {
			this.$message.error("有效期结束时间不能小于开始时间");
			this.formModel.endTime = "";
		}
	}
	dateConversion(value: Date) {
		const date = new DateUtil("").getYMDHMSs(value);
		return date;
	}
	getShopList() {
		getShopList(this.searchType).then((res) => {
			this.tableData = res.data.list
			this.total = res.data.total
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	// 提交信息表单
	submitForm() {

		if (this.formModel.couponName == null || this.formModel.couponName == undefined || this.formModel.couponName == "") {
			this.$message.error('优惠券名称不能为空')
			return
		}
		if (this.formModel.promotion == null || this.formModel.promotion == undefined) {
			this.$message.error('优惠券面额不能为空')
			return
		}
		if (!this.formModel.displayStartTime || !this.formModel.displayEndTime) {
			this.$message.error('显示时间不能为空')
			return
		}
		if (this.formModel.fullAmount == null || this.formModel.fullAmount == undefined) {
			this.$message.error('订单满xx元可使用不能为空')
			return
		}
		if (!this.formModel.startTime || !this.formModel.endTime) {
			this.$message.error('有效期时间不能为空')
			return
		}
		if (this.formModel.receiveTimes == null || this.formModel.receiveTimes == undefined) {
			this.$message.error('领取次数不能为空')
			return
		}
		if (this.formModel.couponType == null || this.formModel.couponType == undefined) {
			this.$message.error('优惠券类型不能为空')
			return
		}
		if (!this.formModel.backColor) {
			this.formModel.backColor = "#d63040"
		}

		if (this.formModel.couponType == 2) {
			if (this.formModel.products == null || this.formModel.products == undefined || this.formModel.products.length == 0) {
				this.$message.error('商品优惠券需要选择商品！')
				return
			}
		}
		if (this.formModel.couponType == 3) {
			if (this.formModel.categorys == null || this.formModel.categorys == undefined || this.formModel.categorys.length == 0) {
				this.$message.error('品类优惠券需要选择品类！')
				return
			}
		}

		if (this.formModel.grantType == 1) {
			if (this.formModel.accounts == null || this.formModel.accounts == undefined || this.formModel.accounts.length == 0) {
				this.$message.error('请先选择客户标签！')
				return
			}
		}

		if (this.formModel.grantType == 2) {
			if (this.formModel.memberLevels == null || this.formModel.memberLevels == undefined || this.formModel.memberLevels.length == 0) {
				this.$message.error('请先选择会员等级！')
				return
			}
			const accounts = [];
			this.formModel.memberLevels.forEach(element => {
				var account = {}
				account.sourceId = element
				accounts.push(account);
			});
			this.formModel.accounts = accounts
		}

		if (this.formModel.grantType == 3) {
			if (this.formModel.accounts == null || this.formModel.accounts == undefined || this.formModel.accounts.length == 0) {
				this.$message.error('请先选择指定客户！')
				return
			}
		}

		if (this.$route.params.id || this.$route.query.id) {
			this.setEditCoupon()
		} else {
			this.setAddCoupon()
		}
	}
	/**
* 确定退出发布积分商品页面
*/

	editSecUnit() {
		this.$confirm(
			`确定退出${this.$route.query.id ? '编辑' : '发布'}优惠券页面?退出后，未保存的信息将不会保留!`,
			"提示",
			{
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			},
		).then(() => {
			this.newly()
		});
	}

	newly() {
        this.$router.push({ name: "couponList" });
    }
	/**
	 * 根据id查看通行票活动
	 */
	async getIdCoupon() {
		const goodId = this.$route.query.id || this.$route.params.id;

		const type = this.$route.query.type || this.$route.params.type;

		console.log("type", type);

		if (type != null && type == "detail") {
			this.disabled = true
		}

		if (!goodId) {
			return;
		}
		const { data } = await getIdCoupon({ 'id': goodId });
		if (data.shopFlag) {
			data.shopFlag = 1
		} else {
			data.shopFlag = 0
		}

		if (data.couponType == 2) {
			this.showGoods = true
		} else {
			this.showGoods = false
		}
		if (data.couponType == 3) {
			this.showCategorys = true
		} else {
			this.showCategorys = false
		}

		this.showTag = false
		this.showMember = false
		this.showAccount = false
		if (data.grantType == 1) {
			this.showTag = true
		}
		if (data.grantType == 2) {
			this.showMember = true
		}
		if (data.grantType == 3) {
			this.showAccount = true
		}

		this.color1 = data.backColor
		this.widePic = data.backPic
		this.formModel = data
		console.log(this.formModel);
	}
	/**
	添加优惠证
	*/
	setAddCoupon() {
		addCoupon(this.formModel).then((res) => {
			this.$message({
				type: 'success',
				message: '创建成功!'
			});
			this.$router.go(-1)
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/** 编辑通行票活动*/
	setEditCoupon() {

		editCoupon(this.formModel).then((res) => {
			// console.log('reee12,', res);
			this.$message({
				type: 'success',
				message: '成功!'
			});
			this.$router.go(-1)
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.searchType.size = val;
		this.getShopList()
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.searchType.current = val;
		console.log('当前页', val);
		this.getShopList()
		// this.getPageTicket();
	}
}
</script>

<style lang="scss">
@import "@/assets/styles/goods/index.scss";

.upload {
	display: flex;
	align-items: center;
	flex-direction: column;
	margin-left: 10px;

	.upload-demo {
		margin-top: 10px;
	}
}

.useableTimes__ci {
	margin-left: 10px;
}

.goodForm::-webkit-scrollbar {
	display: none;
}

.w-e-text-container {
	height: 532px !important;
	/*!important是重点，因为原div是行内样式设置的高度300px*/
}

.valueName {
	margin-top: 10px;
	width: 620px;
	padding: 20px 20px;
	border: 1px solid #d7d7d7;
}

.primary__bottom {
	margin-top: 30px;
	text-align: center;

	.el-button {
		width: 100px;
		font-size: 16px;
		font-weight: 700;
	}
}
</style>
