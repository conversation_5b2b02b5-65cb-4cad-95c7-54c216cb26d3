<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:52:07
-->
<template>
  <m-card class="form" :needToggle="true">
    <el-form ref="form" :model="searchType" label-width="110px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户姓名">
            <el-input v-model="searchType.nickName" placeholder="请输入用户姓名"></el-input>
          </el-form-item>
        </el-col>
		<el-col :span="12">
		  <el-form-item label="用户手机号">
		    <el-input v-model="searchType.userMobile" placeholder="请输入用户手机号"></el-input>
		  </el-form-item>
		</el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="商家名称">
            <el-input v-model="searchType.shopName" placeholder="请输入商家名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经手人">
            <el-input v-model="searchType.verifyNickName" placeholder="请输入经手人"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>

         <el-col :span="12">
			 <el-form-item label="核销时间">
			   <el-date-picker
			         v-model="value0"
			         type="daterange"
			         range-separator="-"
			         start-placeholder="开始日期"
			         end-placeholder="结束日期"
					 style="width: 220px;"
					 @change="chooseTime"
					 >
			       </el-date-picker>
			 </el-form-item>
         
          </el-col>
        <el-col :span="12">
          <el-form-item label="优惠券名称">
            <el-input v-model="searchType.couponName" placeholder="请输入优惠券名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
	  <el-row>
	  
	 <el-col :span="12">
	   <el-form-item label="核销号">
	     <el-input v-model="searchType.verifyCode" placeholder="请输入核销号"></el-input>
	   </el-form-item>
	 </el-col>
   <el-col :span="12">
	   <el-form-item label="核销人手机号">
	     <el-input v-model="searchType.verifyUserMobile" placeholder="请输入核销人手机号"></el-input>
	   </el-form-item>
	 </el-col>
	</el-row>
    
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
      </el-form-item>
    </el-form>
  </m-card>
  <!-- </div> -->
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
// import { getAllCategory } from "@/api/good/goods";
import { SearchState, SearchKeyType } from "./searchType";
import DateUtil from "@/store/modules/date";
import { DatePickerOptions } from "element-ui/types/date-picker";
// import { watch } from "vue";

@Component
export default class Search extends Vue implements SearchState {
  name = "Search";

  @Prop({})
  status!: string;

  // @Prop({
  //   type: Object,
  //   default() {
  //     return {
  //       name: "",
  //       showCategoryId: "",
  //       status: "",
  //       createBeginTime: "",
  //       createEndTime: ""
  //     };
  //   }
  // })
  @Prop({})
  searchTypeProp!: SearchKeyType;

  @Watch("status")
  getSaleMode() {
    this.saleMode = this.status;
  }

  // @Watch("searchTypeProp")
  // getSearchType() {
  //   this.searchType = this.searchTypeProp;
  //   console.log('\\\\\\\\',this.searchType);
    
  // }
  // pickerOptions: {
  //         disabledDate(time) {
  //           return time.getTime() > Date.now();
  //         },
  pickerOptions: DatePickerOptions = {
    shortcuts: [{
      text: '今天',
      onClick(picker) {
        picker.$emit('pick', new Date());
      }
    }, {
      text: '昨天',
      onClick(picker) {
        const date = new Date();
        date.setTime(date.getTime() - 3600 * 1000 * 24);
        picker.$emit('pick', date);
      }
    }, {
      text: '一周前',
      onClick(picker) {
        const date = new Date();
        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit('pick', date);
      }
    },
    {
      text: "一个月前",
      onClick(picker) {
        const date = new Date();
        date.setTime(date.getTime() - 3600 * 1000 * 24 * 30);
        picker.$emit("pick", date);
      }
    },
    {
      text: "三个月前",
      onClick(picker) {
        const date = new Date();
        date.setTime(date.getTime() - 3600 * 1000 * 24 * 90);
        picker.$emit("pick", date);
      }
    }]
  };
    options=[{
          value: '1',
          label: '全部'
        }, {
          value: '100',
          label: '未审'
        }, {
          value: '200',
          label: '已审'
        }];
  showCateList = [];
  goodsType = [];

  searchType = {  } as SearchKeyType;


  saleMode = "";

  createTime = "";
  value0 = ''
  value1 = ''
created(){
	var cache = JSON.parse(
		localStorage.getItem("cache_couponWriteOffList_search_form") || "{}"
	);
	this.searchType = Object.assign(this.searchType, cache) as SearchKeyType;
	// console.log('ffffffffff',this.searchType,cache);
}
  // mounted() {
	 
  // }



  // chooseTime(data: Array<Date>) {
  chooseTime(data:any) {
    this.searchType.startTime = data ? this.dateConversion(data[0]) : "";
	this.searchType.endTime = data ? this.dateConversion(data[1]) : "";
	console.log('时间', this.searchType);
  }


  dateConversion(value: Date) {
    const date = new DateUtil("").getYMDs(value);
    return date;
  }
  exprotData() {
    this.$emit("exprotBy", this.searchType);
  }
  search() {
	  console.log('eeeeeee',this.searchType);
    this.$emit("searchBy", this.searchType);
  }
}
</script>

<style lang="scss" scoped>
.el-form-item .el-input{
  width: 224px;
}
.el-form-item .el-button{
  width:90px;
}
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  &.show {
    height: 300px;
    margin-bottom: 20px;
  }

  &.hide {
    margin-bottom: 20px;
    height: 50px;

    .form__btn {
      width: 940px;
      height: 50px;
      background: #f9f9f9;
      line-height: 50px;
      // margin-top: 20px
    }
  }

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}

.page {
  // height: 270px;
  background-color: #f9f9f9;
  margin-bottom: 20px;
}

@include b(search) {
  display: flex;
  flex-wrap: wrap;

  @include e(item) {
    padding: 20px 40px 10px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include m(text) {
      width: 60px;
    }
  }

  @include e(icon) {
    width: 40px;
    text-align: center;
    border-left: 1px solid #dcdfe6;
    cursor: pointer;
    vertical-align: middle;
  }
}

@include b(searchButton) {
  margin: 20px 30px;
}
</style>
