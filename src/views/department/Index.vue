<!--
 * @description: 平台用户管理
-->
<template>
	<div class="customer">
		<el-tabs>
			<div class="memberList">
				<div class="line"></div>
				<m-card class="form" :needToggle="true">
					<el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="部门编号"><el-input v-model="dataForm.deptCode" clearable
										placeholder="请输入部门编号" /></el-form-item>
							</el-col>
							<el-col :span="10">
								<el-form-item label="部门全称"><el-input v-model="dataForm.deptFullName" clearable
										placeholder="请输入部门全称" /></el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="部门简称"><el-input v-model="dataForm.departmentName" clearable
										placeholder="请输入部门简称" /></el-form-item>
							</el-col>
							<el-col :span="10">
								<el-form-item label="状  态">
									<el-select v-model="dataForm.stopOpenState" placeholder="请选择状态" style="width: 256px"
										clearable>
										<el-option label="全部" :value="''" />
										<el-option v-for="tag in statusList" :key="tag.value" :label="tag.key"
											:value="tag.value" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
						<el-button type="primary" style="margin-left:100px" @click="searchQuery(1)">搜索</el-button>
					</el-form>
				</m-card>
				<!-- 部门列表 -->
				<template>
					<el-table :data="departmentList" style="width: 100%" border max-height="100%">
						<el-table-column type="selection" width="60">
						</el-table-column>
						<el-table-column prop="deptCode" label="部门编号">
						</el-table-column>
						<el-table-column prop="deptFullName" label="部门全称">
						</el-table-column>
						<el-table-column prop="employeeFullName" label="绑定用户">
						</el-table-column>
						<el-table-column prop="departmentName" label="部门简称">
						</el-table-column>
						<el-table-column prop="defaultFlag" label="是否默认部门">
							<template slot-scope="scope">
								<span v-if="scope.row.defaultFlag == '0'">否</span>
								<span v-if="scope.row.defaultFlag == '1'">是</span>
							</template>
						</el-table-column>
						<el-table-column prop="stopOpenState" label="状态">
							<template slot-scope="scope">
								<span v-if="scope.row.stopOpenState == '0'">启用</span>
								<span v-if="scope.row.stopOpenState == '-1'">停用</span>
							</template>
						</el-table-column>
						<el-table-column label="操作">
							<template slot-scope="scope">
								<el-tooltip content="删除" v-if="isSupper||deleteButton">
									<!-- 删除-->
									<el-button type="text" size="medium" icon="el-icon-delete-solid"
										@click="delWareBtn(scope.row)"></el-button>
								</el-tooltip>
							</template>
						</el-table-column>
					</el-table>
				</template>
			
				<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
			</div>
		</el-tabs>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import { ElForm } from 'element-ui/types/form';
import { pageDepartmentList, delDepartment, setDefault } from "@/api/department/department";

@Component({
	components: {
		PageManage,
		
	}
})
export default class Index extends Vue {
	@Ref()
	readonly dataFormRef!: ElForm;

	// 数据表单  组成数据用于向后端提交数据
	dataForm = {
		deptCode: '',
		deptFullName: '',
		departmentName: '',
		stopOpenState: ''
	};

	// 传给子组件的id
	departmentId = "";

	departmentCode = "";

	/** 分页条数 */
	pageSize = 10;

	/** 分页页码 */
	pageNum = 1;

	/** 数据长度 */
	total = 0;
	statusList = [
		{ key: '启用', value: '0' },
		{ key: '停用', value: '-1' },
	];

	// 存放全部仓库数据
	departmentList = [];

	menuName = "部门管理";
	buttonList = []
	isSupper = 0;
    deleteButtonCode = "department.delete";
    deleteButton = false;

	mounted() {
		this.searchQuery(1);
		this.buttonAuth();
	}

	buttonAuth() {
		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)
		let buttonList = [];
		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});
		this.buttonList = buttonList

		var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);

		if (deleteButtonData != null && deleteButtonData != undefined) {
			this.deleteButton = true;
		}

	}

	//获取用户列表（数据）
	searchQuery(pageNum: number) {
		// console.log('执行了');
		const form = this.dataForm;
		const param = {
			current: pageNum,
			size: this.pageSize,
			...form
		}

		pageDepartmentList(param).then(res => {
			// 仓库数据
			this.departmentList = res.data.list
			// 显示数据条数
			this.pageSize = res.data.size;
			// 第几页
			this.pageNum = res.data.current;

			this.total = res.data.total;
		})
	}

	//删除按钮功能模块
	delWareBtn(scope) {
		this.$confirm('此操作将永久删除该部门, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const delParam = {
				id: scope.id,
			}
			delDepartment(delParam).then(res => {
				this.$message({
					type: 'success',
					message: res.data
				});
				this.searchQuery(1);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消删除'
			});
			this.searchQuery(1);
		});
	}


	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.pageSize = val;
		this.searchQuery(1);
	}
	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.pageNum = val;
		this.searchQuery(val);
	}
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';
</style>
