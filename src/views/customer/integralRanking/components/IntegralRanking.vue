<template>
    <div style="margin-top: 20px">
        <el-table :data="goodList" style="width: 100%">
            <el-table-column label="序号" type="index" width="60">
            </el-table-column>
            <el-table-column prop="nikeName" label="客户昵称" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="phone" label="客户号码" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="allIntegral" label="客户总积分"  show-overflow-tooltip>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { searchIntegralRanking, exportIntegralRanking } from '@/api/customer/customer';
import { SearchKeyType } from "./searchType";

@Component({
    components: {

    }
})
export default class IntegralRanking extends Vue {
    name = "IntegralRanking"
    goodList = [];
    mounted() {
        this.getIntegralRanking();
    }
    searchType = {
        current: 1,
        saleIntegralSort: 2,
        size: 10
    } as SearchKeyType;

    async getIntegralRanking() {
        const param = this.searchType;
        try {
            const res = await searchIntegralRanking(param);
            const goodList = res.data.list;
            this.total = res.data.total;
            this.goodList = goodList;
        } catch (error) {
            console.log(error);
        }
        this.$emit("getShowProList", this.goodList);
    }
    async exportIntegralRanking() {
        exportIntegralRanking(this.searchType).then((res) => {
            var blob = new Blob([res.data], {
                type: "application/x-msdownload;charset=UTF-8",
            });
            // 创建一个blob的对象链接
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            // 把获得的blob的对象链接赋值给新创建的这个 a 链接
            let now = new Date();
            let timestamp = now.getTime();
            link.setAttribute('download', '积分排行' + timestamp + '.xls'); // 设置下载文件名
            document.body.appendChild(link);
            // 使用js点击这个链接
            link.click();
        }).catch((err) => {
            this.$message.error(err)
        })
    }

}
</script>

<style lang="scss" scoped>
.goodList {
    width: 250px;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-right: 20px;
    overflow: hidden;
}

.center {
    display: flex;
    justify-content: center;
}

.digTitle {
    font-size: 17px;
    font-weight: bold;
}
</style>