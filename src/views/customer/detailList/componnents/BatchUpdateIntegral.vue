<template>
    <div class="customer">
        <el-tabs>
            <div class="memberList">
                <div class="line"></div>
                <div style="margin-bottom: 20px;">
                    <span>积分：</span>
                    <el-input-number v-model="integral" :controls="false" @change="changeIntegral"></el-input-number>
                </div>
                <template style="padding: auto;-top: 10px;">
                    <el-table :data="tableData" style="width: 100%" border ref="multipleSelection" max-height="100%">
                        <el-table-column type="index" width="50">
                        </el-table-column>
                        <el-table-column label="" width="80">
                            <template slot-scope="scope">
                                <el-button @click="handleClick(scope.$index, scope.row)" type="text" size="medium"
                                    icon="el-icon-plus"></el-button>
                                <el-button type="text" size="medium" @click.prevent="removeDomain(scope.$index)"
                                    icon="el-icon-minus"></el-button>
                            </template>
                        </el-table-column>
                        <el-table-column label="客户名称" width="200">
                            <template slot-scope="scope">
                                <el-select v-model="tableData[scope.row.arrKey].userId"
                                    @change="selectCustomer(tableData[scope.row.arrKey].userId, scope.$index, scope.row)"
                                    placeholder="请选择" filterable remote :remote-method="dataFilter">
                                    <el-option v-for="(item, key) in customerList" :key="key" :label="item.nikeName"
                                        :value="item.userId">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="allIntegral" label="总积分" width="180">
                        </el-table-column>
                        <el-table-column prop="usedIntegral" label="已用积分" width="180">
                        </el-table-column>
                        <el-table-column prop="currentIntegral" label="可用积分" width="180">
                        </el-table-column>
                        <el-table-column label="更改类型" width="180">
                            <template slot-scope="scope">
                                <el-select v-model="tableData[scope.row.arrKey].type" placeholder="请选择">
                                    <el-option v-for="(item, key) in changeType" :key="key" :label="item.name"
                                        :value="item.type">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="积分" width="180">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.integral" @input="calculationOne(scope.row)"
                                    placeholder="请输入积分"></el-input>
                            </template>
                        </el-table-column>

                    </el-table>
                </template>
                <el-button type="primary" @click="Submit" style="margin: 60px 40px 60px 800px;">提交</el-button>
            </div>
        </el-tabs>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import { searchMiniAccountDetail, batchUpdateIntegral } from "@/api/customer/customer";

@Component({
    components: {

    }
})
export default class BatchUpdateIntegral extends Vue {
    integral = 0;
    arrKey = 0;
    //新增必填项
    tableData = [{
        arrKey: 0,//dakai
        phone: '',
        nikeName: '',
        allIntegral: null,
        usedIntegral: null,
        currentIntegral: null,
        userId: "",
        integral: null,
        type: null,
    }];
    changeType = [
        {
            name: "增加积分",
            type: "2",
        },
        {
            name: "减少积分",
            type: "3",
        },
    ]
    /** 分页条数 */
    pageSize = 10;

    /** 分页页码 */
    pageNum = 1;

    /** 数据长度 */
    total = 0;

    customerList = [];
    mounted() {
        this.dataFilter("");
    }
    selectCustomer(userId, index, scope) {
        console.log("userId", index);
        let item = this.customerList.find(items => userId == items.userId)
        for (let i = 0; i < this.tableData.length; i++) {
            if (item.phone == this.tableData[i].phone) {
                this.tableData[scope.arrKey].nikeName = '';
                this.tableData[scope.arrKey].phone = '';
                this.tableData[scope.arrKey].userId = '';
                this.tableData[scope.arrKey].allIntegral = null
                this.tableData[scope.arrKey].usedIntegral = null
                this.tableData[scope.arrKey].currentIntegral = null
                this.tableData[scope.arrKey].integral = this.integral
                this.$message.error("已经存在该客户！");
                return
            }
        }
        this.tableData[scope.arrKey].phone = item.phone
        this.tableData[scope.arrKey].nikeName = item.nikeName
        this.tableData[scope.arrKey].allIntegral = item.allIntegral
        this.tableData[scope.arrKey].usedIntegral = item.usedIntegral
        this.tableData[scope.arrKey].currentIntegral = item.currentIntegral
        this.tableData[scope.arrKey].integral = this.integral

        if (this.tableData.length == index + 1) {
            this.arrKey++
            this.tableData.push({
                arrKey: this.arrKey,
                phone: '',
                nikeName: '',
                allIntegral: null,
                usedIntegral: null,
                currentIntegral: null,
                userId: "",
                integral: null,
                type: null,
            });
        }
    }
    //商品编码搜索框
    dataFilter(pageNum: string) {
        const param = {
            keyword: pageNum,
            current: "1",
            size: this.pageSize,
        }
        searchMiniAccountDetail(param).then(res => {
            this.customerList = res.data.list
        })
    }
    //动态增加 新增模态框的行
    handleClick(scope) {
        this.arrKey++
        this.tableData.push({
            arrKey: this.arrKey,
            phone: '',
            nikeName: '',
            allIntegral: null,
            usedIntegral: null,
            currentIntegral: null,
            userId: "",
            integral: null,
            type: null,
        });

    }
    //动态删除
    removeDomain(index) {
        //当只有一行数据的时候不允许删除
        if (this.tableData.length == 1) {
            return
        }
        this.arrKey--//你找找有没有方法，删除对应下标的元素不改变下标的方法 只能说百度了
        console.log("删除：" + index)
        for (var i = index + 1; i < this.tableData.length; i++) {
            this.tableData[i].arrKey--;
            console.log(i)
        }
        this.tableData.splice(index, 1)


    }
    Submit() {

        for (let i = 0; i < this.tableData.length; i++) {
            if (this.tableData[i].userId == undefined || this.tableData[i].userId == null || this.tableData[i].userId == '') {
                this.removeDomain(i)
            }
        }

        for (let i = 0; i < this.tableData.length; i++) {
            if (this.tableData[i].userId == undefined || this.tableData[i].userId == null || this.tableData[i].userId == '') {

                this.$message.error("还未选择客户！");
                return
            }
            if (this.tableData[i].type == null || this.tableData[i].type == undefined || this.tableData[i].type == '') {
                this.$message.error("更改类型不能为空！");
                return
            }
            if (this.tableData[i].integral == null || this.tableData[i].integral == undefined || this.tableData[i].integral <= 0) {
                this.$message.error("积分必须大于0！");
                return
            }
        }


        var param = {}
        param.list = this.tableData

        batchUpdateIntegral(param).then(res => {
            if (res.code == 200) {
                this.$message.success("修改成功");
                this.$router.push({
                    name: "miniAccountDetail",
                });
            } else {
                this.$message.error("修改失败")
            }
        }).catch(error => {
            this.$message.error("修改失败")
        })

    }
    changeIntegral(e) {
        console.log("changeIntegral", e);
        if (e > 0) {
            for (let i = 0; i < this.tableData.length; i++) {
                this.tableData[i].integral = e
            }
        }
    }
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';
</style>
