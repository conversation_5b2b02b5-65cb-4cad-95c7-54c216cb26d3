<template>
    <div class="customer">
        <el-tabs>
            <div class="memberList">
                <div class="line"></div>
                <m-card class="form" :needToggle="true">
                    <el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
                        <el-row :gutter="40">
                            <el-row :gutter="40">
                                <el-col :span="10">
                                    <el-form-item label="客户昵称"><el-input v-model="dataForm.nikeName" clearable
                                            placeholder="请输入客户昵称" /></el-form-item>
                                </el-col>
                                <el-col :span="10">
                                    <el-form-item label="客户手机"><el-input v-model="dataForm.phone" clearable
                                            placeholder="请输入客户手机" /></el-form-item>
                                </el-col>
                            </el-row>
                          <el-row :gutter="40">
                            <el-col :span="10">
                              <el-form-item label="会员类型">
                                <el-select style="width: 100%;" v-model="dataForm.memberTypeIds" multiple clearable placeholder="请选择会员类型"
                                            @remove-tag="handleRemoveTag">
                                  <el-option v-for="item in memberTypeList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                              </el-form-item>
                            </el-col>
                            <el-col :span="10">
                              <el-form-item label="会员等级">
                                <el-select style="width: 100%;" v-model="dataForm.memberLevelIds" multiple clearable
                                           :disabled="levelDisabled" placeholder="请选择会员等级" >
                                  <el-option v-for="item in memberLevelList" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                              </el-form-item>
                            </el-col>
                          </el-row>
                        </el-row>
                        <el-button type="primary" style="margin-left:100px" @click="searchQuery(1)">搜索</el-button>
                    </el-form>
                </m-card>

                <div class="topLine">
                    <div class="topLine__left" style="margin-left: 30px;">
                        <el-button type="primary" @click="exportData">导出列表</el-button>
                        <el-button type="primary" @click="batchUpdateIntegral">批量修改积分</el-button>
                    </div>
                </div>
                <template>
                    <el-table :data="detailList" style="width: 100%" border max-height="100%">
                        <el-table-column label="序号" type="index" width="58" />
                        <el-table-column prop="nikeName" label="客户昵称" width="200" />
                        <el-table-column prop="phone" label="客户手机" width="180" />
                        <el-table-column prop="recommendName" label="推荐人" />
                        <el-table-column prop="memberLevel" label="会员等级" width="180">
                            <template slot-scope="scope">
                                <div>
                                    <span
                                        v-for="(level, idx) in (scope.row.memberLevel ? scope.row.memberLevel.split(',') : [])"
                                        :key="idx">
                                        {{ level }}<br v-if="idx !== (scope.row.memberLevel.split(',').length - 1)" />
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="address" label="收件地址" width="400">
                            <template slot-scope="scope">
                                <span>{{ scope.row.address }}</span>&nbsp;
                                <el-popover placement="right" width="600" trigger="click"
                                    v-if="scope.row.address != null && scope.row.address != undefined && scope.row.address != ''">
                                    <el-table :data="scope.row.miniAccountAddressList">
                                        <el-table-column width="150" property="province" label="省"></el-table-column>
                                        <el-table-column width="150" property="city" label="市"></el-table-column>
                                        <el-table-column width="150" property="county" label="区"></el-table-column>
                                        <el-table-column width="300" property="detailInfo"
                                            label="详细收货地址信息"></el-table-column>
                                    </el-table>
                                    <el-button slot="reference" type="primary">查看所有</el-button>
                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column prop="cardNumber" label="会员卡号" width="180" />
                        <el-table-column prop="registerTime" label="注册时间" width="180" />
                      <el-table-column prop="registerDays" label="注册天数" width="130" />
                      <el-table-column prop="allIntegral" label="总积分" width="120" />
                        <el-table-column prop="usedIntegral" label="已用积分" width="120" />
                        <el-table-column prop="currentIntegral" label="可用积分" width="120" />
                        <el-table-column prop="directMemberCount" label="直推数" width="100">
                            <template slot-scope="scope">
                                <span 
                                    v-if="scope.row.directMemberCount > 0" 
                                    style="color: #409EFF; cursor: pointer;"
                                    @click="goToSubordinate(scope.row.shopUserId, 'direct')">
                                    {{ scope.row.directMemberCount }}
                                </span>
                                <span v-else>{{ scope.row.directMemberCount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="inDirectMemberCount" label="间推数" width="100">
                            <template slot-scope="scope">
                                <span 
                                    v-if="scope.row.inDirectMemberCount > 0" 
                                    style="color: #409EFF; cursor: pointer;"
                                    @click="goToSubordinate(scope.row.shopUserId, 'indirect')">
                                    {{ scope.row.inDirectMemberCount }}
                                </span>
                                <span v-else>{{ scope.row.inDirectMemberCount }}</span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column prop="directOperationCenterCount" label="直推运营中心数量" width="150" /> -->
                        <!-- <el-table-column prop="inDirectOperationCenterCount" label="间推运营中心数量" width="150" /> -->
                        <el-table-column prop="currentCommission" label="佣金余额" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row.currentCommission || '0.00' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="currentGolden" label="金豆余额" width="120">
                            <template slot-scope="scope">
                                <span>{{ scope.row.currentGolden || '0.00' }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="120">
                            <template slot-scope="scope">
                                <el-button v-if="isSupper || updateIntegralButton" type="text" size="small"
                                    @click="updateIntegral(scope.row)">修改积分</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
                <!-- 分页 -->
                <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
                    @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />

                <el-dialog title="修改积分" :visible.sync="dialogVisible" width="30.7%">
                    <div>
                        <p style="margin-bottom: -15px;">{{ updateData.nikeName }}</p>
                    </div>
                    <div style="margin-top: 50px;">
                        <div>
                            <p style="margin-bottom: -15px;">修改类型:</p>
                        </div>
                        <div style="margin-left: 100px;">
                            <el-radio v-model="updateData.type" label="2">增加积分</el-radio>
                            <el-radio v-model="updateData.type" label="3">减少积分</el-radio>
                        </div>
                    </div>
                    <div style="margin-top: 30px;">
                        <div>
                            <p style="margin-bottom: -15px;">积分:</p>
                        </div>
                        <div style="margin-left: 100px;">
                            <el-input-number size="medium" v-model="updateData.integral" :precision="2"
                                :step="0.1"></el-input-number>
                        </div>
                    </div>

                    <span slot="footer" class="dialog-footer">
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="submitData()">确 定</el-button>
                    </span>
                </el-dialog>
            </div>
        </el-tabs>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Ref, Watch } from "vue-property-decorator";
import PageManage from '@/components/PageManage.vue';
import { ElForm } from 'element-ui/types/form';
import { searchMiniAccountDetail, updateIntegral, exportCustomerDetail } from "@/api/customer/customer";
import BatchUpdateIntegral from './componnents/BatchUpdateIntegral.vue';
import { getMemberType, selectMemberLevelList } from "@/api/sign/index";

@Component({
  components: {
    PageManage,
  }
})
export default class Index extends Vue {
    @Ref()
    readonly dataFormRef!: ElForm;

    dataForm = {
      nikeName: '',
      phone: '',
      memberTypeIds: [],
      memberLevelIds: []
    };

    updateData = {
      userId: '',
      type: '',
      integral: 0,
      nikeName: ''
    }

    /** 分页条数 */
    pageSize = 10;

    /** 分页页码 */
    pageNum = 1;

    /** 数据长度 */
    total = 0;

    // 客户明细列表
    detailList = [];

    dialogVisible = false;

    menuName = "客户明细";

    buttonList = []

    isSupper = 0;

    updateIntegralButtonCode = "detailList.updateIntegral";

    updateIntegralButton = false;

    mounted() {

      this.getMemberTypeList();
      this.handleRouteQuery()
      this.searchQuery(1);
      this.buttonAuth();
    }

    buttonAuth() {
      this.isSupper = this.$STORE.userStore.userInfo.isSupper
      let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)
      let buttonList = [];
      authMenuButtonVos.forEach(element => {
        buttonList.push(element.buttonCode);
      });
      this.buttonList = buttonList
      var updateIntegralButtonData = buttonList.find(e => e == this.updateIntegralButtonCode);

      if (updateIntegralButtonData != null && updateIntegralButtonData != undefined) {
        this.updateIntegralButton = true;
      }
    }

    handleRouteQuery() {
      // 获取路由参数中
      const routeQuery = this.$route.query;
      if (routeQuery) {
        this.dataForm = {...routeQuery}
        console.log("接收到参数",routeQuery);
      }
    }

    //获取用户列表（数据）
    searchQuery(pageNum: number) {
      const form = this.dataForm;
      const param = {
        current: pageNum,
        size: this.pageSize,
        ...form
      }
      searchMiniAccountDetail(param).then(res => {
        // 仓库数据
        this.detailList = res.data.list
        // 显示数据条数
        this.pageSize = res.data.size;
        // 第几页
        this.pageNum = res.data.current;
        this.total = res.data.total;
      })
    }

    updateIntegral(row) {
      this.updateData.userId = row.userId
      this.updateData.nikeName = row.nikeName
      this.updateData.type = ''
      this.updateData.integral = 0
      this.dialogVisible = true
    }

    /**
 * @method handleSizeChange
 * @description 每页 条
 */
    handleSizeChange(val: number) {
      this.pageSize = val;
      this.searchQuery(1);
    }

    /**
 * @method handleCurrentChange
 * @description 当前页
 */
    handleCurrentChange(val: number) {
      this.pageNum = val;
      this.searchQuery(val);
    }

  levelDisabled = true;

  memberTypeList: any[] = [];

  memberLevelList: any[] = [];


  async getMemberTypeList() {
    try {
      const res = await getMemberType({ status: 1 });
      if (res && res.data && Array.isArray(res.data) && res.data.length > 0) {
        this.memberTypeList = res.data.map((item: any) => ({ value: item.id, label: item.name }));
      } else {
        this.memberTypeList = [];
      }
    } catch (error) {
      this.memberTypeList = [];
    }
  }

  @Watch('dataForm.memberTypeIds')
  onMemberTypeChange(memberTypeId: string[]) {
    if (memberTypeId && memberTypeId.length >= 1 ) {
      this.getMemberLevelList(memberTypeId);
    }
  }

  async getMemberLevelList(memberTypeIds: any) {
    try {
      this.memberLevelList = [];
      memberTypeIds.forEach(async (id: string) => {
        const param = {
          'memberTypeId': id
        }
        const res = await selectMemberLevelList(param);
        if (res && res.data && Array.isArray(res.data) && res.data.length > 0) {
          let data = res.data.map((item: any) => ({ value: item.id, label: item.memberLevel, memberTypeId: item.memberTypeId }));
          this.memberLevelList.push(...data);
        }
      })
      this.levelDisabled = false;
    } catch (error) {
      this.memberLevelList = [];
    }
  }

  handleRemoveTag(removeId: string) {
    if(this.dataForm.memberTypeIds.length > 0) {
      // this.dataForm.memberLevelIds = this.memberLevelList.filter((item: any) => {
      //   return item.memberTypeId !== removeId;
      // }).map((item: any) => item.id);
      this.dataForm.memberLevelIds = [];
    }else{
      this.dataForm.memberLevelIds = [];
      this.memberLevelList = [];
      this.levelDisabled = true;
    }

  }

  /**
     * 导出客户明细
     */
  async exportData() {
    this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const params = {
        param: {
          nikeName: this.dataForm.nikeName,
          phone: this.dataForm.phone
        }
      };

      exportCustomerDetail(params).then((res) => {
        var blob = new Blob([res.data], {
          type: "application/x-msdownload;charset=UTF-8",
        });
        // 创建一个blob的对象链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        // 把获得的blob的对象链接赋值给新创建的这个 a 链接
        let now = new Date();
        let timestamp = now.getTime();
        link.setAttribute('download', '客户明细_' + timestamp + '.xls'); // 设置下载文件名
        document.body.appendChild(link);

        // 触发下载
        link.click();
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$message.success('导出成功');

      }).catch((err) => {
        this.$message.error("导出失败");
      });
    }).catch(() => {
      // 用户取消导出
    });
  }

  batchUpdateIntegral() {
    this.$router.push({
      name: "BatchUpdateIntegral",
    });
  }

  /**
   * 跳转到下级列表
   * @param id 用户ID
   * @param type 类型：direct-直推，indirect-间推
   */
  goToSubordinate(shopUserId: string, type: string) {
    this.$router.push({
      name: 'Subordinate',
      query: {
        id: shopUserId,
        tab: type
      }, 
      params: {
        id: shopUserId
      }
    });
  }

  submitData() {
    if (this.updateData.type == null || this.updateData.type == undefined || this.updateData.type == "") {
      this.$message.error("请选择修改类型");
      return;
    }
    if (this.updateData.integral == null || this.updateData.integral == undefined || this.updateData.integral <= 0) {
      this.$message.error("请输入积分");
      return;
    }
    updateIntegral(this.updateData).then(res => {
      // this.searchQuery(1);
      this.searchQuery(this.pageNum);
      this.dialogVisible = false
      if (res.code == 200) {
        this.$message.success("修改成功");
      } else {
        this.$message.error("修改失败")
      }
    }).catch(error=>{
      this.$message.error("修改失败")
    })
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.topLine {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    &__left {
        display: flex;
    }

    &__right {
        width: 450px;
        display: flex;
        justify-content: space-around;
    }
}
</style>