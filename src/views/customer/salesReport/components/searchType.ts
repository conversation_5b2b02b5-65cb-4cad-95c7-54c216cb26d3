/*
 * 销售汇总表搜索类型定义
 */

/**
 * @description: search组件state
 * @param memberTypeList 会员类型列表
 * @param memberLevelList 会员等级列表
 * @param searchType 搜索参数
 */
export interface SearchState {
  memberTypeList: MemberTypeItem[];
  memberLevelList: ComType[];
  searchType: SearchKeyType;
}

export type MemberTypeItem = {
  value: string;
  label: string;
};

type ComType = Record<"value" | "label", string>;

/**
 * @description: 搜索关键词
 * @param userName 会员名称
 * @param userPhone 会员电话
 * @param memberTypeId 会员类型ID（多选）
 * @param memberLevelId 会员等级ID（多选）
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param size 每页大小
 * @param current 当前页码
 */
export interface SearchKeyType {
  userName?: string;
  userPhone?: string;
  memberTypeId?: string[];
  memberLevelId?: string[];
  startTime?: string;
  endTime?: string;
  size?: number;
  current?: number;
} 