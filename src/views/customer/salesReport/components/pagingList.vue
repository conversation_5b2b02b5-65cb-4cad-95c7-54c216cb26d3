<!--
 * 销售汇总表分页列表
 -->
<template>
  <div>
    <el-table
      :data="data"
      style="width: 100%"
      :summary-method="getSummaries" show-summary>
      <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
      <el-table-column prop="memberTypeName" label="会员类型" min-width="120" align="center"></el-table-column>
      <el-table-column prop="memberLevelName" label="会员等级" min-width="120" align="center"></el-table-column>
      <el-table-column prop="userName" label="会员名称" min-width="120" align="center"></el-table-column>
      <el-table-column prop="userPhone" label="会员号码" min-width="120" align="center"></el-table-column>
      <el-table-column prop="repurchaseCount" label="复购次数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="repurchaseAmount" label="复购金额" min-width="100" align="center"></el-table-column>
      <el-table-column prop="repurchaseQuantity" label="复购数量" min-width="100" align="center"></el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button 
            type="text" 
            size="small" 
            @click="goToOrderDetail(scope.row)"
            style="color: #409EFF;">
            订单详情
          </el-button>
        </template>
      </el-table-column>


    </el-table>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";

@Component({
  components: { PageManage }
})
export default class PagingList extends Vue {
  @Prop({ type: Array, default: () => [] }) data!: any[];

  @Prop({ type: Object, default: () => ({}) }) query!: any;

  @Prop({ type: Boolean, default: false }) loading!: boolean;

  created() {

  }

  getSummaries(param: any) {
    const { columns, data } = param;
    const sums: any[] = [];
    columns.forEach((column: any, index: number) => {
      if (index === 0) {
        sums[index] = '合计';
        return;
      }
      if (column.property === 'repurchaseAmount' || column.property === 'repurchaseQuantity' || column.property === 'repurchaseCount') {
        const values = data.map((item: any) => Number(item[column.property]));
        const total = values.reduce((prev: any, curr: any) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          }
          return prev;
        }, 0);
        const totalStr = total.toFixed(2);
        sums[index] = totalStr;
      }
    });

    return sums;
  }

  goToOrderDetail(row: any) {
    this.$router.push({
      path: '/order/delivery',
      query: {
        userName: row.userName,
        phone: row.userPhone
      }
    });
  }
}


</script>

<style lang="scss" scoped>
.pagingList-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 