<!--
 * 销售汇总表
 -->
 <template>
  <div class="customer">
    <el-tabs>

         <Search ref="searchRef" :value="query" @search="getList"  />
        <div style="margin-bottom: 20px"></div>
        <pagingList
          :data="dataList"
          :query.sync="query"
          :loading="listLoading"
        />

        <PageManage
            :pageSize="query.size"
            :pageNum="query.current"
            :total="query.total"
            class="PageManage"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handlePageChange"
          />
      </el-tabs>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import Search from "./components/Search.vue";
import pagingList from "./components/pagingList.vue";
import PageManage from '@/components/PageManage.vue';

import { getMemberLevelReport }  from "@/api/customer/customer";

@Component({
  components: {
    PageManage,
    Search,
    pagingList,
  },
})
export default class MemberLevelReport extends Vue {
  // 统一以Search的form为唯一数据源
  query: any = {
    size: 10,
    current: 1,
    total: 0
  };


  dataList: any[] = [];

  listLoading = false;

  created() {
    // 获取路由参数中
    const routeQuery = this.$route.query;
    if (routeQuery) {
      routeQuery.dateRange = [new Date(routeQuery.startTime), new Date(routeQuery.endTime)]
      this.query = {...routeQuery}

      console.log("接受到参数",routeQuery);
    }
    this.getList();
  }

  // 获取Search组件的form
  getSearchForm() {
    const ref: any = this.$refs.searchRef;
    return ref && ref.form ? ref.form : { ...this.query };
  }

  // 获取DataList
  async getList() {
    this.listLoading = true;
    this.query = this.getSearchForm();
    try {
      const res = await getMemberLevelReport(this.query);
      if (res && res.data) {
        this.dataList = res.data.list || [];
        this.query.total = res.data.total || 0;
      }
    } catch (error) {
      this.dataList = [];
      this.query.total = 0;
    } finally {
      this.listLoading = false;
    }
  }

 
  // 分页相关
  handlePageChange(page: number) {
    this.query.current = page;
    this.getList();
  }

  handleSizeChange(size: number) {
    this.query.size = size;
    this.query.current = 1;
    this.getList();
  }

}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';
</style>