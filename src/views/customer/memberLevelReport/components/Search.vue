<!--
 * 销售汇总表搜索组件
 -->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="90px">
      <el-row>
        <el-col :span="10">
          <el-form-item label="会员名称">
            <el-input v-model="form.userName" placeholder="请输入会员名称" @input="handleFieldChange"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="会员号码">
            <el-input v-model="form.userPhone" placeholder="请输入会员号码" @input="handleFieldChange"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="10">
          <el-form-item label="会员类型">
            <el-select style="width: 100%;" v-model="form.memberTypeIds" multiple clearable placeholder="请选择会员类型" @change="handleFieldChange">
              <el-option v-for="item in memberTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="注册时间">
            <el-date-picker v-model="form.dateRange" type="daterange" range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间" style="width: 100%;" @change="chooseTimes" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="10">
          <el-form-item label="会员等级">
            <el-select v-model="form.memberLevelId" multiple clearable :disabled="levelDisabled" placeholder="请选择会员等级" @change="handleFieldChange">
              <el-option v-for="item in memberLevelList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </m-card>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import { getMemberType, selectMemberLevelList } from "@/api/sign/index";

@Component
export default class Search extends Vue {
  @Prop({ default: () => ({}) }) value!: any;

  form: any = {
    userName: "",
    userPhone: "",
    memberTypeIds: [],
    dateRange: [],
    startTime: "",
    endTime: "",
    size: 10,
    current: 1,
    total: 0,
  };

  memberTypeList: any[] = [];

  memberLevelList: any[] = [];

  levelDisabled = false;

  mounted() {
    this.syncFromValue();
    this.getMemberTypeList();
  }

  @Watch('value', { immediate: true, deep: true })
  onValueChange() {
    this.syncFromValue();
  }

  syncFromValue() {
    if (this.value) {
      Object.keys(this.form).forEach(key => {
        if (this.value[key] !== undefined) {
          this.form[key] = this.value[key];
        }
      });
    }
  }

  async getMemberTypeList() {
    try {
      const res = await getMemberType({ status: 1 });
      if (res && res.data && Array.isArray(res.data) && res.data.length > 0) {
        this.memberTypeList = res.data.map((item: any) => ({ value: item.id, label: item.name }));
      } else {
        this.memberTypeList = [];
      }
    } catch (error) {
      this.memberTypeList = [];
    }
  }



  chooseTimes(val: Date[]) {
    if (val && val.length === 2) {
      this.form.startTime = this.formatDate(val[0]);
      this.form.endTime = this.formatDate(val[1]);
    } else {
      this.form.startTime = "";
      this.form.endTime = "";
    }
  
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  handleFieldChange() {
    this.$emit("input", this.form);
  }
  
  handleSearch() {
    this.$emit("search", this.form);
  }

  handleReset() {
    this.form = {
      userName: "",
      userPhone: "",
      memberTypeIds: [],
      dateRange: [],
      startTime: "",
      endTime: "",
      size: 10,
      current: 1,
      total: 0,
    };
    this.$emit("search");
  }
}
</script>

<style lang="scss" scoped>
.form {
  margin-bottom: 10px;
}
</style> 