<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:09
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-12 13:17:51
 2025-7-17
-->
<template>
	<div class="customer">
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="会员列表" name="first">
				<div class="memberList">
					<div class="line"></div>
					<m-card class="form" :needToggle="true">
						<el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
							<el-row :gutter="40">
								<el-col :span="10">
									<el-form-item label="微信昵称"><el-input v-model="dataForm.nikeName" clearable
											placeholder="请输入会员昵称" /></el-form-item>
								</el-col>
								<el-col :span="10">
									<el-form-item label="手机号"><el-input v-model="dataForm.phone" clearable
											placeholder="请输入会员手机号" /></el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="40">
								<el-col :span="10">
									<el-form-item label="上次交易时间">
										<el-date-picker v-model="dataForm.orderSuccessTime"
											:default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss"
											style="width: 256px" type="daterange" range-separator="-"
											start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
									</el-form-item>
								</el-col>
								<el-col :span="10">
									<el-form-item label="实名状态">
										<el-select v-model="dataForm.cardAuthorization" placeholder="请选择实名状态"
											style="width: 256px" clearable>
											<el-option v-for="item in cardAuthorizationOptions" :key="item.value"
												:label="item.label" :value="item.value" />
										</el-select>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="40">
								<el-col :span="10">
									<el-form-item label="到期天数"><el-input v-model="dataForm.expireDays" clearable
											placeholder="请输入到期天数" /></el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="标  签">
										<el-select v-model="dataForm.tagId" placeholder="请选择标签" style="width: 256px"
											clearable>
											<el-option label="全部" :value="null" />
											<el-option v-for="tag in allTagList" :key="tag.tagId" :label="tag.tagName"
												:value="tag.tagId" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="12" v-if="isMainShop">
									<el-form-item label="商户">
										<el-select v-model="dataForm.shopIds" multiple placeholder="请选择商户"
											style="width: 256px" clearable>
											<el-option v-for="shop in shopsPartnerList" :key="shop.id"
												:label="shop.name" :value="shop.shopId" />
										</el-select>
									</el-form-item>
								</el-col>
							</el-row>
							<el-button type="primary" style="margin-left:100px" @click="getDataList(1)">搜索</el-button>
						</el-form>
					</m-card>

					<div class="topLine">
						<div class="topLine__left" style="margin-left: 30px;">
							<el-button type="primary" @click="exportData">导出列表</el-button>
						</div>
					</div>
					<el-row class="customer__filterForm" type="flex" justify="space-between">
						<el-col :span="4">
							<set-drop setName="设置标签" :dropdownList="couponDropList" v-if="isSupper || setLableButton"
								@setClick="batchCouponClick" @command="batchCouponCommand($event, row)" />
							<set-drop setName="设置标签" :dropdownList="couponDropList"
								v-else-if="isSupper || addBackListButton || modifySendStatusButton"
								@setClick="batchCouponClick" @command="batchCouponCommand($event, row)" />
						</el-col>
						<el-col :span="4">
							<el-select v-model="dataForm.sortType" placeholder="请选择排序方式"
								@change="handleCurrentChange(1)">
								<el-option v-for="item in sortOptions" :key="item.value" :label="item.label"
									:value="item.value"></el-option>
							</el-select>
						</el-col>
					</el-row>

					<!-- <m-table :data.sync="dataList" :selection="true" :checked-item.sync="multipleSelection" slot="content"
						needHoverBorder ref="customerListRef" class="customerList">
						<template v-slot:header="{ row }">
							<div style="width: 40%">
								<span>会员卡号:{{ row.cardNumber }}</span>
							</div>
							<div style="width: 30%">
								<span>会员等级:{{ row.memberLevelName }}</span>
							</div>
							<div style="width: 30%">
								对接状态：<span v-if="row.sendStatus === 1">已发送</span><span v-else>未发送</span>
							</div>
							<div style="width: 50%">
								<span>成为用户时间:{{ row.firstLoginTime }}</span>
							</div>
						</template>
<m-table-column prop="userName" label="客户信息" :showsSlection="true" width="300">
	<template v-slot="{ row }">
								<div class="info">
									<img class="info__img" :src="row.avatarUrl" alt />
									<div class="info__msg">
										<div class="info__msg--text">
											<div style="width: 350px">{{ row.nikeName }}</div>
											<div v-if="row.phone" style="width: 350px">({{ row.phone }})</div>
										</div>
										<div class="info__msg--tags" v-if="isSupper || setLableButton">
											<span v-if="row.userTagVos !== null && row.userTagVos.length > 0" @click="setLabel(row)"
												class="pointer">
												<el-button type="text">
													{{
														row.userTagVos
															.map(tag => tag.tagName)
															.slice(0, 2)
															.join(' ; ')
													}}
												</el-button>
												<span>等共{{ row.userTagVos.length }}个标签</span>
											</span>
											<el-button type="text" @click="setLabel(row)"
												v-if="row.userTagVos === null || (row.userTagVos !== null && row.userTagVos.length === 0)">
												请选择所属标签
											</el-button>
											<i class="el-icon-caret-bottom pointer" @click="setLabel(row)" />
										</div>
										<div class="info__msg--tags" v-else>
											<span v-if="row.userTagVos !== null && row.userTagVos.length > 0" class="pointer">
												<el-button type="text">
													{{
														row.userTagVos
															.map(tag => tag.tagName)
															.slice(0, 2)
															.join(' ; ')
													}}
												</el-button>
												<span>等共{{ row.userTagVos.length }}个标签</span>
											</span>
											<el-button type="text"
												v-if="row.userTagVos === null || (row.userTagVos !== null && row.userTagVos.length === 0)">
												暂无所属标签
											</el-button>
											<i class="el-icon-caret-bottom pointer" />
										</div>
										<div><span>一级：{{ row.oneTeamNum }}</span><span style="margin-left: 20px;">二级：{{
											row.twoTeamNum }}</span> </div>
									</div>
								</div>
							</template>
</m-table-column>
<m-table-column prop="userName" label="购次">
	<template v-slot="{ row }">
								<span>{{ row.consumeNum }}</span>
							</template>
</m-table-column>
<m-table-column prop="userName" label="交易总额">
	<template v-slot="{ row }">
								<span>{{ row.consumeTotleMoney }}</span>
							</template>
</m-table-column>
<m-table-column prop="userName" label="推荐人">
	<template v-slot="{ row }">
								<span>{{ row.recommendName }}</span>
							</template>
</m-table-column>
<m-table-column prop="userName" label="上次交易时间" width="160">
	<template v-slot="{ row }">
								<span>{{ row.orderLastDealTime }}</span>
							</template>
</m-table-column>
<m-table-column prop="userName" label="佣金">
	<template v-slot="{ row }">
								<div>
									<div>可提现：{{ row.currentCommission ? row.currentCommission : '0' }}</div><br>
									<div>累计：{{ row.commission }}</div>
								</div>


							</template>
</m-table-column>
<m-table-column prop="userName" label="操作" width="150">
	<template v-slot="{ row }">
								<set-drop setName="加入黑名单" :dropdownList="itemList" @setClick="itemClick($event, row)"
									v-if="isSupper || addBackListButton" @command="itemCommand($event, row)" />
								<set-drop setName="" :dropdownList="itemList" v-else @command="itemCommand($event, row)" />
								<el-dialog title="更改等级" :visible.sync="dialogVisible" width="42.7%">
									<el-table :data="EditwareList" style="width:100%">
										<el-table-column prop="memberLevelName" label="会员卡等级" width="260">
										</el-table-column>
										<el-table-column prop="cardNumber" label="会员卡号" width="260">
										</el-table-column>
										<el-table-column prop="firstLoginTime" label="开卡时间" width="260">
										</el-table-column>
									</el-table>
									<p style="margin-top: 50px; margin-bottom: -15px;">更改等级为:</p>
									<template>
										<el-select v-model="id" placeholder="请选择" style="margin-top: -20px; margin-left: 80px;">
											<el-option v-for="item in gtlevel" :key="item.id" :label="item.memberLevel" :value="item.id">
											</el-option>
										</el-select>
									</template>
	<span slot="footer" class="dialog-footer">
		<el-button @click="dialogVisible = false">取 消</el-button>
		<el-button type="primary" @click="determine()">确 定</el-button>
	</span>
	</el-dialog>

	</template>
</m-table-column>
</m-table> -->


					<template>
						<el-table ref="multipleTable" :data="dataList" border tooltip-effect="dark" style="width: 100%"
							height="670" @selection-change="handleSelectionChange">
							<el-table-column type="selection" width="50" fixed="left" align="center">
							</el-table-column>
							<el-table-column label="客户信息" min-width="260" fixed="left" align="center">
								<template slot-scope="scope">
									<div>
										<div class="info">
											<img class="info__img" :src="scope.row.avatarUrl" alt />
											<div class="info__msg">
												<div class="info__msg--text">
													<div style="width: 350px">{{ scope.row.nikeName }}</div>
													<div v-if="scope.row.phone" style="width: 350px">{{ scope.row.phone
													}}</div>
												</div>
												<div class="info__msg--tags" v-if="isSupper || setLableButton">
													<span
														v-if="scope.row.userTagVos !== null && scope.row.userTagVos.length > 0"
														@click="setLabel(scope.row)" class="pointer">
														<el-button type="text">
															{{
																scope.row.userTagVos
																	.map(tag => tag.tagName)
																	.slice(0, 2)
																	.join(' ; ')
															}}
														</el-button>
														<span>等共{{ scope.row.userTagVos.length }}个标签</span>
													</span>
													<el-button type="text" @click="setLabel(scope.row)"
														v-if="scope.row.userTagVos === null || (scope.row.userTagVos !== null && scope.row.userTagVos.length === 0)">
														请选择所属标签
													</el-button>
													<i class="el-icon-caret-bottom pointer"
														@click="setLabel(scope.row)" />
												</div>
												<div class="info__msg--tags" v-else>
													<span
														v-if="scope.row.userTagVos !== null && scope.row.userTagVos.length > 0"
														class="pointer">
														<el-button type="text">
															{{
																scope.row.userTagVos
																	.map(tag => tag.tagName)
																	.slice(0, 2)
																	.join(' ; ')
															}}
														</el-button>
														<span>等共{{ scope.row.userTagVos.length }}个标签</span>
													</span>
													<el-button type="text"
														v-if="scope.row.userTagVos === null || (scope.row.userTagVos !== null && scope.row.userTagVos.length === 0)">
														暂无所属标签
													</el-button>
													<i class="el-icon-caret-bottom pointer" />
												</div>
												<div><span>一级：{{ scope.row.oneTeamNum }}</span><span
														style="margin-left: 20px;">二级：{{
															scope.row.twoTeamNum }}</span> </div>
												<div>
													<span v-if="scope.row.selfInviteCode">推荐码:{{
														scope.row.selfInviteCode }}</span>
												</div>
											</div>
										</div>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="购次" min-width="160" align="center">
								<template slot-scope="scope">
									<div>
										<span>{{ scope.row.consumeNum }}</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="交易总额" min-width="160" align="center">
								<template slot-scope="scope">
									<div>
										<span>{{ scope.row.consumeTotleMoney }}</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="推荐人" min-width="160" align="center">
								<template slot-scope="scope">
									<div>
										<span>{{ scope.row.recommendName }}</span>
										<span v-if="scope.row.recommendEmployeeName">(职员：{{
											scope.row.recommendEmployeeName }}) </span>
										<span v-if='scope.row.recommendSaleFlag == "1"'>(业务员) </span>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="上次交易时间" min-width="180" align="center">
								<template slot-scope="scope">
									<div>
										<span>{{ scope.row.orderLastDealTime }}</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="佣金" min-width="180" align="center">
								<template slot-scope="scope">
									<div>
										<div>可提现佣金：{{ scope.row.currentCommission ? scope.row.currentCommission : '0' }}
										</div>
										<div>佣金累计：{{ scope.row.commission }}</div>
										<div>可用金豆：{{ scope.row.currentGolden }}</div>
										<div>已用金豆：{{ scope.row.usedGolden }}</div>
										<div>总金豆：{{ scope.row.golden }}</div>

									</div>
								</template>
							</el-table-column>
							<el-table-column label="用户注册时间" min-width="280" align="center">
								<template slot-scope="scope">
									<div>
										<span>{{ scope.row.registerTime }}</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="会员信息" min-width="320" align="center">
								<template slot-scope="scope">
									<div
										v-if="scope.row.userMemberLevelList && scope.row.userMemberLevelList.length > 0">
										<div v-for="(userMemberLevel, index) in scope.row.userMemberLevelList"
											:key="index">
											<span>会员类型：{{ userMemberLevel.memberTypeName }}，会员等级：{{
												userMemberLevel.memberLevelName }}，
												<br/>
												加入时间：{{
													userMemberLevel.joinTime }}，
													<br/>
													到期时间：{{ userMemberLevel.endTime }}，
													<br/>
													成为会员时间：{{
													userMemberLevel.joinDays }}</span>
										</div>

									</div>
								</template>
							</el-table-column>
							<el-table-column label="实名状态" min-width="160" align="center">
								<template slot-scope="scope">
									<div>
										<div v-if="scope.row.cardAuthorization == -1">审核不通过</div>
										<div v-if="scope.row.cardAuthorization == 0">未实名</div>
										<div v-if="scope.row.cardAuthorization == 1">待审核</div>
										<div v-if="scope.row.cardAuthorization == 2">已实名</div>
									</div>
								</template>
							</el-table-column>

							<el-table-column label="业务员" min-width="160" align="center">
								<template slot-scope="scope">
									<div>
										<div v-if="scope.row.saleFlag == 1">是</div>
										<div v-if="scope.row.saleFlag == 0">否</div>
									</div>
								</template>
							</el-table-column>


							<el-table-column fixed="right" label="操作" width="160" align="center">
								<template slot-scope="scope">
									<set-drop setName="加入黑名单" :dropdownList="itemDropList(scope.row)"
										@setClick="itemClick($event, scope.row)" v-if="isSupper || addBackListButton"
										@command="itemCommand($event, scope.row)" />

									<set-drop setName="" :dropdownList="itemDropList(scope.row)" v-else
										@command="itemCommand($event, scope.row)" />

								</template>
							</el-table-column>
						</el-table>

						<el-dialog title="更改等级" :visible.sync="dialogVisible" width="42.7%">
							<el-table :data="EditwareList" style="width:100%">
								<el-table-column prop="memberLevelName" label="会员卡等级" width="260">
								</el-table-column>
								<el-table-column prop="cardNumber" label="会员卡号" width="260">
								</el-table-column>
								<el-table-column prop="firstLoginTime" label="开卡时间" width="260">
								</el-table-column>
							</el-table>
							<p style="margin-top: 50px; margin-bottom: -15px;">更改等级为:</p>
							<template>
								<el-select v-model="id" placeholder="请选择" style="margin-top: -20px; margin-left: 80px;">
									<el-option v-for="item in gtlevel" :key="item.id" :label="item.memberLevel"
										:value="item.id">
									</el-option>
								</el-select>
							</template>
							<span slot="footer" class="dialog-footer">
								<el-button @click="dialogVisible = false">取 消</el-button>
								<el-button type="primary" @click="determine()">确 定</el-button>
							</span>
						</el-dialog>

						<!-- 审核对话框 -->
						<el-dialog title="实名审核" :visible.sync="dialogVisibleName" width="42.7%" @close="resetForm">

							<el-form :model="formName" :rules="rules" label-position="left" ref="ruleFormName"
								label-width="100px">
								<!-- 姓名显示 -->
								<el-form-item label="姓名：">
									<span>{{ formName.name }}</span>
								</el-form-item>

								<!-- 身份证号显示 -->
								<el-form-item label="身份证号：">
									<span>{{ formName.idNumber }}</span>
								</el-form-item>

								<!-- 身份证正反面图片 -->
								<el-form-item label="身份证正面：">
									<el-image style="width: 300px; height: 200px" :src="formName.frontImage"
										:preview-src-list="[formName.frontImage]">
									</el-image>
								</el-form-item>

								<el-form-item label="身份证反面：">
									<el-image style="width: 300px; height: 200px" :src="formName.backImage"
										:preview-src-list="[formName.backImage]">
									</el-image>
								</el-form-item>

								<!-- 审核状态选择 -->
								<el-form-item label="审核状态：" prop="status">
									<el-radio-group v-model="formName.status">
										<el-radio label="2">通过</el-radio>
										<el-radio label="-1">不通过</el-radio>
									</el-radio-group>
								</el-form-item>

								<!-- 审核意见 -->
								<el-form-item label="审核意见：" prop="comment">
									<el-input type="textarea" v-model="formName.comment" placeholder="请输入审核意见"
										:rows="3">
									</el-input>
								</el-form-item>
							</el-form>

							<span slot="footer" class="dialog-footer">
								<el-button @click="dialogVisibleName = false">取 消</el-button>
								<el-button type="primary" @click="determineName('ruleFormName')">确 定</el-button>
							</span>
						</el-dialog>

						<!-- 驳回确认对话框 -->
						<el-dialog title="驳回确认" :visible.sync="dialogVisibleReject" width="30%"
							@close="resetRejectForm">
							<p>确定要驳回该用户的申请吗？</p>
							<span slot="footer" class="dialog-footer">
								<el-button @click="dialogVisibleReject = false">取 消</el-button>
								<el-button type="danger" @click="confirmReject()">确定驳回</el-button>
							</span>
						</el-dialog>

					</template>

					<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
						@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
					<black-list ref="blackListDialogRef" v-if="blackListVisible" @refreshDataList="getDataList" />
					<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList"
						@refreshDataList="getDataList(1)" />
					<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList"
						@refreshDataList="getDataList(1)" />
				</div>
			</el-tab-pane>


			<el-tab-pane label="会员类型" name="secondType" style="margin-left: 20px;">
				<div class="memberCard">
					<el-form :model="vipCard" ref="vipCard" class="demo-ruleForm" label-width="100px">
						<el-button type="text" @click="addMemberType">新增</el-button>

						<el-table style="width:100%;" border :data="memberTypeList">
							<el-table-column type="index" width="60" align="center" fixed="left">
							</el-table-column>
							<el-table-column label="类型名称" align="center" fixed="left">
								<template slot-scope="scope">
									<el-input v-model="scope.row.name" placeholder="请输入类型名称"></el-input>
								</template>
							</el-table-column>
							<el-table-column label="默认会员类型" align="center">
								<template slot-scope="scope">
									<!-- <el-select v-model="scope.row.defaultType" placeholder="请选择" :disabled="scope.row.defaultType == '是' ? true : false" >
										<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
										</el-option>
									</el-select> -->
									<div v-if="scope.row.defaultType == 1">
										是
									</div>
									<div v-else>
										<el-button type="text" @click="setButton(scope.row)">设置为默认会员类型</el-button>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="区域类型" align="center">
								<template slot-scope="scope">
									<el-select v-model="scope.row.regionFlag" placeholder="请选择">
										<el-option v-for="item in options" :key="item.value" :label="item.label"
											:value="item.value">
										</el-option>
									</el-select>
								</template>
							</el-table-column>

							<el-table-column label="并存类型" align="center">
								<template slot-scope="scope">
									<el-select v-model="scope.row.togetherFlag" placeholder="请选择">
										<el-option v-for="item in options" :key="item.value" :label="item.label"
											:value="item.value">
										</el-option>
									</el-select>
								</template>
							</el-table-column>


							<el-table-column label="状态" align="center">
								<template slot-scope="scope">
									<el-button type="text">{{ scope.row.status == 1 ? "启用" : "停用" }}</el-button>
								</template>
							</el-table-column>

							<el-table-column label="备注" align="center">
								<template slot-scope="scope">
									<el-input type="textarea" :rows="2" placeholder="请输入备注" v-model="scope.row.remark">
									</el-input>
								</template>
							</el-table-column>
							<el-table-column label="操作" align="center">
								<template slot-scope="scope">
									<!--											<el-button type="text" @click="">设置</el-button>-->

									<el-button type="text" v-if="isSupper || updateStatusButton"
										@click="setStatus(scope.row)">{{
											scope.row.status == 1 ? "停用" :
												"启用" }}</el-button>
									<el-button v-if="isSupper || deleteButton" type="text"
										@click="delMemberType(scope.row)">删除</el-button>

									<el-button v-if="isSupper || deleteButton" type="text"
										@click="goCard(scope.row, 1)">会员等级</el-button>

									<el-button v-if="isSupper || deleteButton" type="text"
										@click="goCard(scope.row, 2)">会员排名</el-button>
								</template>
							</el-table-column>
						</el-table>
						<el-button type="primary" @click="submitMemberType()" v-show="!openYDERP">提交</el-button>
					</el-form>


				</div>
			</el-tab-pane>

			<el-tab-pane label="会员等级" name="second" style="margin-left: 20px;">
				<div class="memberCard">
					<el-form :model="vipCard" ref="vipCard" class="demo-ruleForm" label-width="100px">
						<!-- <el-button type="text" @click="addDomain" v-show="!openYDERP" v-if="isSupper || addButton">新增</el-button> -->
						<el-button type="text" @click="addDomain" v-show="memberTypeId"
							v-if="isSupper || addButton">新增</el-button>
						<el-table style="width:100%;" border :data="memberData">
							<el-table-column type="index" width="60" align="center">
							</el-table-column>
							<el-table-column label="等级名称" align="center">
								<template slot-scope="scope">
									<el-input v-model="scope.row.memberLevel"></el-input>
								</template>
							</el-table-column>
							<el-table-column prop="membercardLive" label="会员权益" align="center">
								<template slot-scope="scope">
									<span>{{ scope.row.rightsAndInterests && scope.row.rightsAndInterests[0] &&
										scope.row.rightsAndInterests[0].name }}等......</span>
									<el-button type="text" @click="modifyBtn(scope.row)"
										v-if="isSupper || editButton">修改</el-button>
								</template>
							</el-table-column>
							<el-table-column label="状态" align="center">
								<template slot-scope="scope">
									<el-button type="text">{{ scope.row.disable == 0 ? "启用" : "停用" }}</el-button>
								</template>
							</el-table-column>
							<el-table-column label="默认等级" align="center">
								<template slot-scope="scope">
									<div v-if="isSupper || setLevelButton">
										<span v-if="scope.row.defaultLevel === 1" style="color: #2d8cf0">默认</span>
										<el-button v-else-if="scope.row.id" type="text"
											@click="setDefaultLevel(scope.row)">设置为默认等级</el-button>
										<el-button v-else disabled type="text">设置为默认等级</el-button>
									</div>
									<div v-else>
										<span v-if="scope.row.defaultLevel === 1" style="color: #2d8cf0">默认</span>
									</div>
								</template>
							</el-table-column>
							<el-table-column label="默认仓库" align="center">
								<template slot-scope="scope">
									<el-checkbox @change="checkChange($event, scope.$index)"
										:value="scope.row.stockFlag === 1"></el-checkbox>
								</template>
							</el-table-column>
							<el-table-column label="会员体系会员" align="center">
								<template slot-scope="scope">
									<el-checkbox @change="checkChangeFlag($event, scope.$index)"
										:value="scope.row.memberFlag === 1"></el-checkbox>
								</template>
							</el-table-column>
							<el-table-column label="区域类型" align="center">
								<template slot-scope="scope">
									<el-select v-model="scope.row.regionType" placeholder="请选择"
										:disabled="regionFlag == '0'">
										<el-option v-for="item in optionsRegionType" :key="item.value"
											:label="item.label" :value="item.value">
										</el-option>
									</el-select>
								</template>
							</el-table-column>

							<el-table-column label="操作" align="center">
								<template slot-scope="scope">
									<!--											<el-button type="text" @click="">设置</el-button>-->

									<el-button type="text" v-if="(isSupper || updateStatusButton) && scope.row.id"
										@click="deactivate(scope.row)">{{
											scope.row.disable == 0 ? "停用" :
												"启用" }}</el-button>
									<el-button type="text" disabled v-else>{{
										scope.row.disable == 0 ? "停用" :
											"启用" }}</el-button>
									<el-button v-if="isSupper || deleteButton" type="text"
										@click="delMemberLevel(scope.row)">删除</el-button>
									<el-button v-if="ruleShow == 103 && (isSupper || updateStatusButton)" type="text"
										@click="settingRule(scope.row)">佣金设置</el-button>
								</template>
							</el-table-column>
						</el-table>
						<el-button type="primary" @click="Subbtn()" v-show="memberTypeId"
							v-if="isSupper || addButton || editButton" :loading="submitLoading"
							:disabled="submitLoading">
							{{ submitLoading ? '提交中...' : '提交' }}
						</el-button>
						<!-- <el-button type="primary" v-if="isSupper || addButton || editButton" @click="Subbtn()"
							v-show="!openYDERP">提交</el-button> -->
					</el-form>
					<!-- 会员卡页面开卡方式的付费购买修改模块 -->
					<el-dialog title="选择权益" :visible.sync="modify" width="30%">

						<template>
							<el-checkbox v-for="(itemTwo, index) in RightsList" v-model="itemTwo.isSelected"
								:key="itemTwo.id" @change='change' :false-label="0" :true-label="1">
								{{ itemTwo.name }}
							</el-checkbox>
							<!-- 								<el-checkbox v-for="itemTwo,index in RightsList" :key="itemTwo.id" :checked="itemTwo.isSelected==1? true:false" true-label="1" false-label="0" @change="e => changeCheckboxone(e, itemTwo)">
									{{itemTwo.name}}
								</el-checkbox> -->
						</template>

						<span slot="footer" class="dialog-footer">
							<el-button @click="modify = false">取 消</el-button>
							<el-button type="primary" @click="modifytest()">确 定</el-button>
						</span>
					</el-dialog>

					<el-dialog title="佣金规则设置" :visible.sync="commissionSetting" width="50%">
						<template>
							<el-form :model="ruleForm" ref="ruleForm" :label-position="'left'" label-width="110px">
								<div class="distributionSettings">
									<div>分销设置</div>
								</div>
								<el-form-item label="分销佣金类型">
									<el-radio-group v-model="ruleForm.ruleType">
										<el-radio :label="100">固定金额</el-radio>
										<el-radio :label="101">百分比</el-radio>
									</el-radio-group>
								</el-form-item>
								<el-form-item label-width="20px">
									<div>
										<el-row>
											<el-col :span="2" :align="'right'">
												<div>分销佣金 </div>
											</el-col>
											<el-col :span="12">
												<div class="DistributionCommission">
													<div class="commission__top">
														<span class="span1">1级分销佣金</span>
														<input v-model="ruleForm.parentReceive"
															class="distribution__commission" />
														<span class="span2">{{ ruleForm.ruleType == 100 ? '元' : '%'
														}}</span>
													</div>
													<div class="commission__top">
														<span class="span1">2级分销佣金</span>
														<input v-model="ruleForm.aboveParentReceive"
															class="distribution__commission" />
														<span class="span2">{{ ruleForm.ruleType == 100 ? '元' : '%' }}
														</span>
													</div>
												</div>
											</el-col>
										</el-row>
									</div>
								</el-form-item>


								<el-form-item label="最低消费" v-if="ruleForm.ruleType == 100">

									<el-input v-model="ruleForm.minPayAmount" style="width:180px;"></el-input>
									<span class="first">元</span>
								</el-form-item>

							</el-form>
						</template>
						<span slot="footer" class="dialog-footer">
							<el-button @click="commissionSetting = false">取 消</el-button>
							<el-button type="primary" @click="modifyCommissionRule()">确 定</el-button>
						</span>
					</el-dialog>

				</div>
			</el-tab-pane>



			<el-tab-pane label="会员排名" name="third" style="margin-left: 20px;">
				<!-- 会员列表 -->
				<memberList ref="memberList" :memberTypeId="memberTypeId" />
			</el-tab-pane>

			<el-tab-pane label="会员有效期规则" name="rule" style="margin-left: 20px;">
				<div class="div">
					<el-form :model="vipCard" ref="vipCard" class="demo-ruleForm" label-width="100px"
						label-position="left">

						<el-row>
							<el-col :span="6">
								<el-form-item label="有效期天数">
									<el-input v-model="validityPeriod.activeDays" placeholder="请输入有效期天数"
										style="width: 220px;"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="剩余天数提醒">
									<el-input v-model="validityPeriod.remainingDays" placeholder="请输入剩余天数提醒"
										style="width: 220px;"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-form-item label="状态">
									<el-select v-model="validityPeriod.status" placeholder="请选择" style="width: 220px;">
										<el-option v-for="item in optionsPeriod" :key="item.value" :label="item.label"
											:value="item.value">
										</el-option>
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>



						<el-button type="text" @click="addValidityPeriodList">新增</el-button>

						<!-- <el-table style="width:100%;" border :data="validityPeriod">
							<el-table-column type="index" width="50" align="center" fixed="left">
							</el-table-column>
							<el-table-column label="有效期天数" width="180" align="center" fixed="left">
								<template slot-scope="scope">
									<el-input v-model="scope.row.activeDays" placeholder="请输入有效期天数"></el-input>
								</template>
							</el-table-column>

							<el-table-column label="剩余天数提醒" width="180" align="center" fixed="left">
								<template slot-scope="scope">
									<el-input v-model="scope.row.remainingDays" placeholder="请输入剩余天数提醒"></el-input>
								</template>
							</el-table-column>

							<el-table-column label="状态" width="180" align="center">
								<template slot-scope="scope">
									<el-select v-model="scope.row.status" placeholder="请选择">
										<el-option v-for="item in optionsPeriod" :key="item.value" :label="item.label" :value="item.value">
										</el-option>
									</el-select>
								</template>
							</el-table-column>

						</el-table> -->

						<el-table style="width:100%;" border :data="validityPeriod.productList">
							<el-table-column type="index" width="60" align="center" fixed="left">
							</el-table-column>

							<el-table-column label="商品名称" align="center" min-width="250">
								<template slot-scope="scope">
									<el-select v-model="scope.row.productSkuId"  filterable 
										@change="selectInspectType($event, scope.$index)" style="width: 100%;"
										placeholder="请选择">
										<el-option v-for="(item, key) in library" :key="key"
											:label="`${item.goodsCode}.${item.name}.${item.skuStock}`"
											:value="item.skuId">
										</el-option>
									</el-select>
								</template>
							</el-table-column>

							<el-table-column label="商品数量" align="center">
								<template slot-scope="scope">
									<el-input v-model="scope.row.productQuantity" placeholder="请输入商品数量"></el-input>
								</template>
							</el-table-column>
							<el-table-column label="商品规格" prop="skuSpecs" align="center">
								<template slot-scope="scope">
									<span>{{ scope.row.skuSpecs }}</span>
								</template>
							</el-table-column>
							<el-table-column label="商品sku编号" prop="productSkuId" align="center">
							</el-table-column>
							<el-table-column label="商品sku条码" prop="productSkuCode" align="center">
							</el-table-column>

							<el-table-column label="操作" align="center">
								<template slot-scope="scope">
									<el-button type="text" @click="delValidityPeriod(scope.$index)">删除</el-button>
								</template>
							</el-table-column>

						</el-table>
						<el-button type="primary" @click="submitValidityPeriod()">提交</el-button>
					</el-form>
				</div>

			</el-tab-pane>

		</el-tabs>
		<associateSubordinates :dialogVisible="dialogVisibleSubordinates" :objData="objData"
			@clickVisible="clickVisible" @onSureGoods="getDataList(1)">
		</associateSubordinates>

		<!-- 修改佣金-->
		<!--		<updateCommission :dialogVisible="dialogVisibleUpdateCommission" :objData="updateCommissionData" @clickVisible="clickCommissionVisible"
			@onSureGoods="getDataList(1)">
		</updateCommission>-->

		<el-dialog :visible.sync="dialogVisibleUpdateCommission" width="600px"
			:before-close="updateCommissionHandleClose">
			<div slot="title" class="digTitle">修改佣金</div>
			<updateCommission ref="addModel" :editCurrent="updateCommissionData"
				:addDialog="dialogVisibleUpdateCommission">
			</updateCommission>
			<span slot="footer" class="dialog-footer">
				<el-button @click="updateCommissionHandleClose">取 消</el-button>
				<el-button type="primary" @click="updateCommissionSubmit">确认</el-button>
			</span>
		</el-dialog>

		<el-dialog :visible.sync="dialogVisibleUpdateGoldBean" width="600px"
			:before-close="() => { dialogVisibleUpdateGoldBean = false }">
			<div slot="title" class="digTitle">修改金豆</div>
			<updateGoldBean ref="updateGoldBean" :editCurrent="updateGoldBeanData"
				:addDialog="dialogVisibleUpdateGoldBean">
			</updateGoldBean>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisibleUpdateGoldBean = false">取 消</el-button>
				<el-button type="primary" @click="updateGoldBeanSubmit">确认</el-button>
			</span>
		</el-dialog>

		<editUserInfo @load="getDataList(1)" ref="editUserInfo" />
		<activitiesMessage ref="activitiesMessage" />
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref, Watch } from "vue-property-decorator";
import PageManage from '@/components/PageManage.vue';
import BlackList, { NewCustomerTagList } from '../common/SetBlackList.vue';
import SetDrop from '@/views/customer/common/SetDrop.vue';
import SetLabel from '@/views/customer/list/components/dialog/SetTags.vue';
import associateSubordinates from '@/views/customer/list/components/dialog/associateSubordinates.vue';
import editUserInfo from './components/dialog/editUserInfo.vue'
import { CustomerListState, ApiCustomerList, CustomerTagList, CustomerRank, ApiMemberCardList } from './customerListType';
import { getYDERPOpen, memberLevelList, dragSort } from "@/api/sign/index";
import { ElForm } from 'element-ui/types/form';
import activitiesMessage from "./components/dialog/activitiesMessage.vue";
import { getAccountInfo, ByNameOrNumber } from '@/api/warehouse/warehouse';
import memberList from "./components/list/memberList.vue";
import updateCommission from '@/views/customer/list/components/dialog/updateCommission.vue';
import updateGoldBean from '@/views/customer/list/components/dialog/updateGoldBean.vue';

import {
	getCustomerList,
	getAllTags,
	getMemberList,
	rightsinterests,
	SubMemberList,
	memberlevel,
	updateMemberLeve,
	getMemberLevelList,
	delMemberLevel,
	setDefaultLevel,
	updateMemberSendStatus,
	getMemberType,
	addOrUpdateMemberType,
	deleteMemberType,
	updateStatus,
	setDefaultType,
	setAuditCard,
	rejectCard,
	getMemberActiveSetting,
	addOrUpdate,
	updateMemberSaleFlag,
	exportCustomerList
} from "@/api/customer/customer";
import { ElTable } from 'element-ui/types/table';
import { getCommissionRule } from "@/api/withdrawalApi/withdrawalApi";
import { getShopsPartnerList } from "@/api/shopsPartner/shopsPartner";
import { manualAddCommission, addGoldBean } from "@/api/reward/reward";
@Component({
	components: {
		PageManage,
		BlackList,
		SetDrop,
		SetLabel,
		associateSubordinates,
		editUserInfo,
		memberList,
		activitiesMessage,
		updateCommission,
		updateGoldBean
	}
})
export default class Index extends Vue implements CustomerListState {
	@Ref()
	readonly dataFormRef!: ElForm;

	@Ref()
	readonly customerListRef!: ElTable;

	@Ref()
	readonly blackListDialogRef!: BlackList;

	@Ref()
	readonly labelDialogRef!: SetLabel;

	@Ref()
	readonly updateGoldBean!: updateGoldBean;

	@Ref()
	readonly addModel!: updateCommission;

	dataForm = {
		nikeName: '',
		phone: '',
		becomeMemberTime: [],
		orderSuccessTime: [],
		memberNumber: null,
		rankCode: null,
		sortType: '',
		tagId: null,
		shopIds: '',
		cardAuthorization: -2, // 实名状态
		expireDays: '' // 到期天数
	};

	dialogVisibleName = false;
	dialogVisibleReject = false;
	currentRejectId = '';
	formName = {
		id: '',
		name: '',
		idNumber: '',
		frontImage: '',
		backImage: '',
		status: '2', // 审核不通过：-1,审核通过：2
		comment: ''
	}
	// 实名状态选项
	cardAuthorizationOptions = [
		{
			value: -2,
			label: "全部"
		},
		{
			value: -1,
			label: '审核不通过'
		},
		{
			value: 0,
			label: '未实名'
		},
		{
			value: 1,
			label: '待审核'
		},
		{
			value: 2,
			label: '已实名'
		}
	];
	rules = {
		status: [
			{ required: true, message: '请选择审核状态', trigger: 'blur' },
		],
		comment: [
			{ required: true, message: '请输入审核意见', trigger: 'blur' },
		],
	}

	dataList: Array<ApiCustomerList> = [];

	allTagList: Array<CustomerTagList> = [];

	memberList: Array<ApiMemberCardList> = [];

	statusList: Array<ApiStatusList> = [];

	selectionList: Array<ApiCustomerList | NewCustomerTagList | ApiMemberCardList> = [];

	multipleSelection: Array<ApiCustomerList | NewCustomerTagList | ApiMemberCardList> = [];

	shopsPartnerList: Array[any] = [];

	menuName = "客户列表";

	buttonList = [];

	isSupper = 0;

	setLableButtonCode = "customerList.memberList.setLable";
	setLableButton = false;

	addBackListButtonCode = "customerList.memberList.addBackList";
	addBackListButton = false;

	modifySendStatusButtonCode = "customerList.memberList.modifySendStatus";
	modifySendStatusButton = false;


	memberUpgradeButtonCode = "customerList.memberList.memberUpgrade";
	memberUpgradeButton = false;

	addButtonCode = "customerList.card.add";
	addButton = false;

	editButtonCode = "customerList.card.edit";
	editButton = false;

	setLevelButtonCode = "customerList.card.setLevel";
	setLevelButton = false;

	deleteButtonCode = "customerList.card.delete";
	deleteButton = false;

	updateStatusButtonCode = "customerList.card.updateStatus";
	updateStatusButton = false;

	activityButtonCode = "customerList.memberList.activity";
	activityButton = false;

	setSalespersonButtonCode = "customerList.memberList.setSalesperson";
	setSalespersonButton = false;

	cancelSalespersonButtonCode = "customerList.memberList.cancelSalesperson";
	cancelSalespersonButton = false;

	itemList = [
		{
			text: '重置未发送',
			command: '重置未发送',
			show: this.isSupper || this.modifySendStatusButton,
			disabled: false
		},
		{
			text: '重置已发送',
			command: '重置已发送',
			show: this.isSupper || this.modifySendStatusButton,
			disabled: false
		},
		{
			text: '下级',
			command: '下级',
			show: true,
			disabled: false
		}, {
			text: '关联上下级',
			command: '关联上下级',
			show: true,
			disabled: false
		},
		{
			text: '编辑',
			command: '编辑',
			show: true,
			disabled: false
		},
		{
			text: '实名审核',
			command: '实名审核',
			show: true,
			disabled: false
		},
		{
			text: '修改佣金',
			command: 'updateCommission',
			show: this.isSupper && this.isMainShop,
			disabled: false
		},
		{
			text: '修改金豆',
			command: 'updateGoldBean',
			show: this.isSupper && this.isMainShop,
			disabled: false
		}
	];




	managerVisible = false;

	blackListVisible = false;

	labelVisible = false;

	visible = true;

	activeName = 'first';
	//开启易达接口
	openYDERP = true;
	// 是否主店铺
	isMainShop = 0;
	sortOptions = [
		{
			value: '',
			label: '按注册时间降序'
		},
		{
			value: 1,
			label: '按交易总额降序'
		},
		{
			value: 2,
			label: '按交易总额升序'
		}
	];
	//会员列表分页的对话框模块
	dialogVisible = false;
	memberRenew = false;
	memberbtn = '';
	//更改等级模块的table
	upgrade = [{
		membercardName: '普通卡会员',
		membercardLive: '小金',
		cardMode: '付费购买',
		membercardnumber: '1000022201066089226',
		validity: '2022-6-18 17:49:38',
	}];
	//更改等级模块的选择等级模块
	EditwareList = [];
	gtlevel = [];
	id = '';
	value = '';
	//会员等级设置模块
	memberData = [];

	//提交按钮loading状态
	submitLoading = false;

	//会员权益
	rightsAnds = [];
	RightsList = [];
	values: [];  // 存储value的数组
	labels: [];   // 存储label的数组
	//积分等级设置模块
	// integral=[{
	// 	  gradename: '普通会员',
	// 	  amount: '',
	// 		benefits: '商品折扣，积分加倍等2项权益',
	// }]
	//会员卡页面开卡方式的付费购买修改模块
	modify = false;
	modifyData = '';
	checked = true;
	//会员卡页面开卡方式的积分兑换修改模块
	// pointsEx = false;
	// pointsExData = [];
	//会员卡页面禁用模块
	status = 0;
	statusText = '';
	statList = [{
		disable: '',
		memberId: ''
	}];
	// 佣金设置规则
	ruleShow = null
	commissionSetting = false;
	ruleForm = {
		ruleType: null,
		parentReceive: null,
		aboveParentReceive: null,
		minPayAmount: null
	}
	// 佣金规则设置单击当前行的记录
	commissionRow = {}
	//会员注册信息模块
	// registerData=[{
	//   date: '2016-05-02',
	//   name: '王小虎',
	//   address: '上海市普陀区金沙江路 1518 弄'
	//   }];
	//会员权益设置模块
	// vipBenefits = [{
	// 	benefitsList: '2016-05-02',
	// 	benefitsName: '王小虎',
	// 	benefitsExplain: '上海市普陀区金沙江路 1518 弄'
	// 	}];

	// 2025.2.27有改动
	get itemDropList() {
		return (row: GoodDetailInfo) => {
			return [
				{
					text: '更改等级',
					command: '3',
					show: this.isSupper || this.memberUpgradeButton,
					disabled: false
				},
				{
					text: '重置未发送',
					command: '重置未发送',
					show: this.isSupper || this.modifySendStatusButton,
					disabled: false
				},
				{
					text: '重置已发送',
					command: '重置已发送',
					show: this.isSupper || this.modifySendStatusButton,
					disabled: false
				},
				{
					text: '下级',
					command: '下级',
					show: true,
					disabled: false
				}, {
					text: '关联上下级',
					command: '关联上下级',
					show: true,
					disabled: false
				},
				{
					text: '编辑',
					command: '编辑',
					show: true,
					disabled: false
				},
				{
					text: '实名审核',
					command: '实名审核',
					show: row.cardAuthorization == '1',
					disabled: false
				},
				{
					text: '设为业务员',
					command: '设为业务员',
					show: row.saleFlag == 0 || row.saleFlag == null,
					disabled: false
				},
				{
					text: '驳回',
					command: '驳回',
					show: row.cardAuthorization == '2',
					disabled: false
				},
				{
					text: '修改佣金',
					command: 'updateCommission',
					show: this.isSupper && this.isMainShop,
					disabled: false
				},
				{
					text: '修改金豆',
					command: 'updateGoldBean',
					show: this.isSupper && this.isMainShop,
					disabled: false
				}
			];
		};
	}

	// @Watch("openYDERP")
	// handleBtnVisible(v: boolean) {
	// 	if (!v) {
	// 		this.itemList = [
	// 			{
	// 				text: '更改等级',
	// 				command: '3',
	// 				show: this.isSupper || this.memberUpgradeButton,
	// 				disabled: false
	// 			},
	// 			{
	// 				text: '重置未发送',
	// 				command: '重置未发送',
	// 				show: this.isSupper || this.modifySendStatusButton,
	// 				disabled: false
	// 			},
	// 			{
	// 				text: '重置已发送',
	// 				command: '重置已发送',
	// 				show: this.isSupper || this.modifySendStatusButton,
	// 				disabled: false
	// 			}, {
	// 				text: '下级',
	// 				command: '下级',
	// 				show: true,
	// 				disabled: false
	// 			}, {
	// 				text: '关联上下级',
	// 				command: '关联上下级',
	// 				show: true,
	// 				disabled: false
	// 			}, {
	// 				text: '编辑',
	// 				command: '编辑',
	// 				show: true,
	// 				disabled: false
	// 			},
	// 			{
	// 				text: '实名审核',
	// 				command: '实名审核',
	// 				show: true,
	// 				disabled: false
	// 			}
	// 		]
	// 	} else {
	// 		this.itemList = [
	// 			{
	// 				text: '重置未发送',
	// 				command: '重置未发送',
	// 				show: this.isSupper || this.modifySendStatusButton,
	// 				disabled: false
	// 			},
	// 			{
	// 				text: '重置已发送',
	// 				command: '重置已发送',
	// 				show: this.isSupper || this.modifySendStatusButton,
	// 				disabled: false
	// 			}, {
	// 				text: '下级',
	// 				command: '下级',
	// 				show: true,
	// 				disabled: false
	// 			}, {
	// 				text: '关联上下级',
	// 				command: '关联上下级',
	// 				show: true,
	// 				disabled: false
	// 			},
	// 			{
	// 				text: '编辑',
	// 				command: '编辑',
	// 				show: true,
	// 				disabled: false
	// 			},
	// 			{
	// 				text: '实名审核',
	// 				command: '实名审核',
	// 				show: true,
	// 				disabled: false
	// 			}
	// 		]
	// 	}
	// }
	//会员卡分页的输入框数组
	vipCard = {
		// memberName : '',
		effectiveDate: '',
		duration: '0',
	};

	//会员卡分页的默认单选框
	// opening = '1';

	rankOptions: Array<CustomerRank> = [];

	/** 分页条数 */
	pageSize = 10;

	/** 分页页码 */
	pageNum = 1;

	/** 数据长度 */
	total = 0;



	/** 赠送优惠券下拉菜单 */
	couponDropList: any[] = [

	];

	dialogVisibleSubordinates = false   //是否关联上下级弹窗

	dialogVisibleUpdateCommission = false   // 修改佣金弹窗

	objData = {}
	updateCommissionData = {}

	dialogVisibleUpdateGoldBean = false
	updateGoldBeanData = {}

	// 会员类型
	options = [{
		value: 0,
		label: '否'
	}, {
		value: 1,
		label: '是'
	}];

	// 会员类型列表
	memberTypeList = [];
	// 会员类型id
	memberTypeId = '';
	regionFlag = '';
	// 区域类型
	optionsRegionType = [{
		value: 1,
		label: '区/县'
	}, {
		value: 2,
		label: '市级'
	}, {
		value: 3,
		label: '省级'
	}];
	//商品编码的下拉框模块
	library = [];
	// 会员有效期列表
	validityPeriod = {};
	// 会员类型
	optionsPeriod = [{
		value: 0,
		label: '停用'
	}, {
		value: 1,
		label: '启用'
	}];

	mounted() {
		this.getDataList(1);
		this.buttonAuth();

		this.getTags();
		this.getYDERPOpen();
		this.getShopsPartner();
		this.isMainShop = this.$STORE.userStore.userInfo.shopInfoVo.mainFlag
	}
	checkChange(e: any, index: any) {
		if (e) {
			this.memberData[index].stockFlag = 1
		} else {
			this.memberData[index].stockFlag = 0
		}

	}
	checkChangeFlag(e: any, index: any) {
		if (e) {
			this.memberData[index].memberFlag = 1
		} else {
			this.memberData[index].memberFlag = 0
		}

	}

	buttonAuth() {
		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});


		this.buttonList = buttonList

		var setLableButtonData = buttonList.find(e => e == this.setLableButtonCode);

		if (setLableButtonData != null && setLableButtonData != undefined) {
			this.setLableButton = true;
		}

		var addBackListButtonData = buttonList.find(e => e == this.addBackListButtonCode);

		if (addBackListButtonData != null && addBackListButtonData != undefined) {
			this.addBackListButton = true;
		}

		var modifySendStatusButtonData = buttonList.find(e => e == this.modifySendStatusButtonCode);
		if (modifySendStatusButtonData != null && modifySendStatusButtonData != undefined) {
			this.modifySendStatusButton = true;
		}

		var memberUpgradeButtonData = buttonList.find(e => e == this.memberUpgradeButtonCode);
		if (memberUpgradeButtonData != null && memberUpgradeButtonData != undefined) {
			this.memberUpgradeButton = true;
		}

		var addButtonData = buttonList.find(e => e == this.addButtonCode);
		if (addButtonData != null && addButtonData != undefined) {
			this.addButton = true;
		}

		var editButtonData = buttonList.find(e => e == this.editButtonCode);
		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}

		var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);
		if (deleteButtonData != null && deleteButtonData != undefined) {
			this.deleteButton = true;
		}

		var updateStatusButtonData = buttonList.find(e => e == this.updateStatusButtonCode);
		if (updateStatusButtonData != null && updateStatusButtonData != undefined) {
			this.updateStatusButton = true;
		}

		var setLevelButtonData = buttonList.find(e => e == this.setLevelButtonCode);
		if (setLevelButtonData != null && setLevelButtonData != undefined) {
			this.setLevelButton = true;
		}

		var activityButtonData = buttonList.find(e => e == this.activityButtonCode);
		if (activityButtonData != null && activityButtonData != undefined) {
			this.activityButton = true;
		}

		var setSalespersonButtonData = buttonList.find(e => e == this.setSalespersonButtonCode);
		if (setSalespersonButtonData != null && setSalespersonButtonData != undefined) {
			this.setSalespersonButton = true;
		}

		var cancelSalespersonButtonData = buttonList.find(e => e == this.cancelSalespersonButtonCode);
		if (cancelSalespersonButtonData != null && cancelSalespersonButtonData != undefined) {
			this.cancelSalespersonButton = true;
		}



		if (this.isSupper || this.addBackListButton) {
			this.couponDropList.push({
				command: '加入黑名单',
				disabled: false,
				show: true,
				text: '加入黑名单'
			})
		}
		if (this.isSupper || this.modifySendStatusButton) {
			this.couponDropList.push({
				command: '重置未发送',
				disabled: false,
				show: true,
				text: '重置未发送'
			})
			this.couponDropList.push({
				command: '重置已发送',
				disabled: false,
				show: true,
				text: '重置已发送'
			})
		}
		if (this.isSupper || this.activityButton) {
			this.couponDropList.push({
				command: '活动通知',
				disabled: false,
				show: true,
				text: '活动通知'
			})
		}
		if (this.isSupper || this.setSalespersonButton) {
			this.couponDropList.push({
				command: '设为业务员',
				disabled: false,
				show: true,
				text: '设为业务员'
			})
		}
		if (this.isSupper || this.cancelSalespersonButton) {
			this.couponDropList.push({
				command: '取消业务员',
				disabled: false,
				show: true,
				text: '取消业务员'
			})
		}

	}
	// 确定按钮提交审核
	determineName(ruleFormName: any) {

		this.$refs[ruleFormName].validate((valid) => {
			if (valid) {
				const param = {
					"id": this.formName.id,
					"cardAuthorization": this.formName.status,
					"cardAuditReason": this.formName.comment,
				}

				setAuditCard(param).then(res => {
					this.$message.success(res.data)
					this.dialogVisibleName = false;
					// 获取用户列表
					this.getDataList(1);
				}).catch(err => {
					this.$message.error(err);
				})
			} else {
				console.log('error submit!!');
				return false;
			}
		});

	}

	// 重置驳回表单
	resetRejectForm() {
		this.currentRejectId = '';
	}

	// 确认驳回
	confirmReject() {
		if (!this.currentRejectId) {
			this.$message.error('请选择要驳回的记录');
			return;
		}

		const param = {
			id: this.currentRejectId
		};

		rejectCard(param).then(res => {
			this.$message.success('驳回成功');
			this.dialogVisibleReject = false;
			this.getDataList(1);
		}).catch(err => {
			this.$message.error(err.data || '驳回失败');
		});
	}

	// 重置表单
	resetForm() {
		this.formName = {
			id: '',
			name: '',
			idNumber: '',
			frontImage: '',
			backImage: '',
			status: '2',
			comment: ''
		}
	}

	// 导航栏切换分页
	handleClick(tab, event) {
		this.memberTypeId = '';
		console.log(event)
		if (this.activeName == 'second') {
			this.pomemberList()
			this.Rightsinterests()
			// this.getRule()
			// this.getmemberList()
		}
		if (this.activeName == 'secondType') {
			this.getMemberType();
		}
		if (this.activeName == 'rule') {
			this.getValidityPeriod();
		}



	}

	// 会员类型列表
	getMemberType() {
		getMemberType({}).then(res => {
			this.memberTypeList = res.data;
			// console.log('res=', res.data);

		});
	}
	// 会员类型新增/编辑
	addMemberType() {
		this.memberTypeList.push({
			id: "",
			name: "",
			defaultType: 0,
			regionFlag: 0,
			togetherFlag: 0,
			status: 0,
			remark: ""
		})
	}

	// 会员类型新增/编辑
	addValidityPeriodList() {
		this.validityPeriod.productList.push(
			{
				"productId": '',
				"productPic": '',
				"productName": '',
				"productSkuId": '',
				"productSkuCode": "",
				"productQuantity": '',
				"skuSpecs": '',
			}
		)

	}

	// 获取会员有效期设置
	getValidityPeriod() {
		getMemberActiveSetting(null).then(res => {
			if (res.data.id) {
				this.validityPeriod = res.data
				// 确保每个商品项都有skuStock字段
				if (this.validityPeriod.productList && this.validityPeriod.productList.length > 0) {
					this.validityPeriod.productList.forEach(item => {
						if (!item.hasOwnProperty('skuSpecs')) {
							item.skuSpecs = '';
						}
					});
				}
			} else {
				this.validityPeriod = {
					"id": "",
					"activeDays": '',
					"remainingDays": '',
					"status": '',
					"productList": []
				}
			}
			this.dataFilter();
		}).catch(err => {
			this.$message.error(err)
		})
	}

	selectInspectType(inspectType: any, index) {
		this.library.forEach(library => {
			if (inspectType == library.skuId) {
				let item = this.validityPeriod.productList[index];
				item.productId = library.productId;
				item.productPic = library.pic;
				item.productName = library.name;
				item.productSkuId = library.skuId;
				item.productSkuCode = library.goodsCode;
				item.skuSpecs = library.skuStock;
			}
		})


		console.log('this.validityPeriod.productList[index]=', this.validityPeriod.productList[index]);
		console.log('inspectType=', inspectType);


	}
	delValidityPeriod(index: any) {
		this.validityPeriod.productList.splice(index, 1);
	}

	submitValidityPeriod() {
		if (!this.validityPeriod.activeDays) {
			this.$message.error("请输入有效期天数")
			return;
		}
		if (!this.validityPeriod.remainingDays) {
			this.$message.error("请输入剩余天数提醒")
			return;
		}
		if (!this.validityPeriod.status && this.validityPeriod.status != 0) {
			this.$message.error("请输入状态")
			return;
		}
		let flag1 = true;
		let flag2 = true;
		this.validityPeriod.productList.forEach(item => {
			if (!item.productQuantity) {
				flag1 = false;
			}
			if (!item.productSkuId) {
				flag2 = false;
			}
		});
		if (!flag2) {
			this.$message.error("请选择商品")
			return;
		}
		if (!flag1) {
			this.$message.error("请输入商品数量")
			return;
		}
		addOrUpdate(this.validityPeriod).then(res => {
			this.$message.success('提交成功')
		}).catch(err => {
			this.$message.error(err)
		})
	}

	//商品编码搜索框
	dataFilter() {
		const param = {
			keyword: '',
			current: 1,
			size: 10,
		}
		ByNameOrNumber(param).then(res => {
			this.library = res.data.list
		})
	}

	// 会员类型提交按钮
	submitMemberType() {
		let newData = JSON.parse(JSON.stringify(this.memberTypeList));
		let flag = true;
		if (newData.length) {
			newData.forEach((item, i) => {
				if (!item.name) {
					this.$message.error("类型名称不能为空！");
					flag = false;
				}

				// console.log('item=', item);
			});
			console.log('newData=', newData);

			// 全部输入才请求接口
			if (flag) {
				addOrUpdateMemberType({ list: newData }).then(res => {
					if (res.code == 200) {
						this.$message.success('提交成功');
						// 重新请求会员类型列表
						this.getMemberType();
					}
				}).catch(er => {
					this.$message.error(er);
				})

			}

		}
	}

	// 启用/停用会员类型按钮
	setStatus(row: any) {
		this.$confirm('更改会员状态停用/启用，确定继续更改吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const data = { id: row.id, status: row.status == 1 ? 0 : 1 };
			updateStatus(data).then(res => {
				if (res.code == 200) {
					this.$message.success('设置成功');
					// 重新请求会员类型列表
					this.getMemberType();
				}
			}).catch(er => {
				this.$message.error(er);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消更改会员状态'
			});
		});
	}

	// 会员类型列表删除按钮
	delMemberType(row: any) {
		this.$confirm('此操作将永久删除该会员类型, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			deleteMemberType({ id: row.id }).then(res => {
				if (res.code == 200) {
					this.$message.success('删除成功');
					// 重新请求会员类型列表
					this.getMemberType();
				}
			}).catch(er => {
				this.$message.error(er);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消删除'
			});
		});

	}
	// 设置为默认会员类型
	setButton(row: any) {
		this.$confirm('设置为默认会员类型, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			setDefaultType({ id: row.id }).then(res => {
				if (res.code == 200) {
					this.$message.success('设置成功');
					// 重新请求会员类型列表
					this.getMemberType();
				}
			}).catch(er => {
				this.$message.error(er);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消设置为默认会员类'
			});
		});
	}

	// 前往会员卡和会员排名页面
	goCard(row: any, index: number) {
		this.memberTypeId = row.id;
		this.regionFlag = row.regionFlag;
		if (index == 1) {
			this.activeName = 'second';
			this.pomemberList();
			this.Rightsinterests();
		} else {
			this.activeName = 'third'
		}
	}



	/**
	 * 获取用户列表
	 */
	getDataList(pageNum: number) {
		const form = this.dataForm;
		let shopIdsParam = ''
		if (form.shopIds) {
			// 将shopIds 数组 转换成用英文逗号分隔字符串
			shopIdsParam = form.shopIds.join(',')
		}
		const param = {
			page: pageNum,
			size: this.pageSize,
			sortType: form.sortType,
			memberNumber: form.memberNumber,
			rankCode: form.rankCode,
			nikeName: form.nikeName,
			phone: form.phone,
			tagId: form.tagId,
			cardAuthorization: form.cardAuthorization,
			expireDays: form.expireDays, // 到期天数
			becomeMemberStartTime: form.becomeMemberTime !== null ? form.becomeMemberTime[0] : '',
			becomeMemberEndTime: form.becomeMemberTime !== null ? form.becomeMemberTime[1] : '',
			orderSuccessStartTime: form.orderSuccessTime !== null ? form.orderSuccessTime[0] : '',
			orderSuccessEndTime: form.orderSuccessTime !== null ? form.orderSuccessTime[1] : '',
			shopIds: shopIdsParam,
		};
		getCustomerList(param).then(res => {
			this.dataList = res.data.list;
			this.pageSize = res.data.size;
			this.pageNum = res.data.current;
			this.total = res.data.total;
		});
	}

	/**
	 * @method getTags
	 * @description 获取所有标签
	 */
	getTags() {
		getAllTags().then(res => {
			this.allTagList = res.data;
		});
	}
	/**
	 * @method getShopsPartner
	 * @description 获取所有商家
	 */
	getShopsPartner() {
		getShopsPartnerList({}).then(res => {
			this.shopsPartnerList = res.data;
		});
	}
	/**
	 * @method getYDERPOpen
	 * @description 获取易达接口是否开启
	 */
	getYDERPOpen() {
		getYDERPOpen().then(res => {
			this.openYDERP = res.data;
		});
	}

	batchCouponClick() {
		if (this.multipleSelection.length > 0) {
			this.labelVisible = true;
			this.$nextTick(() => {
				this.labelDialogRef.init(this.multipleSelection as ApiCustomerList[]);
			});
		} else {
			this.$message.warning('至少选择一个客户');
		}
		// console.log("fd=",this.multipleSelection);

	}

	/**
* 多选
*/
	handleSelectionChange(val: any) {
		this.multipleSelection = []
		val.forEach((item: any) => {
			this.multipleSelection.push(item);
		});
		// this.$emit("update:checked-item", val);
		// console.log("fdf=",this.multipleSelection);
		// console.log("val=",val);

	}

	batchCouponCommand(command: string, row: ApiCustomerList) {
		console.log(row);

		if (this.multipleSelection.length > 0) {
			switch (command) {
				case '加入黑名单':
					this.blackListVisible = true;
					this.$nextTick(() => {
						this.blackListDialogRef.init(this.multipleSelection as ApiCustomerList[], 1);
					});
					break;
				case '重置未发送':
					this.$nextTick(() => {
						this.modifySendStatus(this.multipleSelection as ApiCustomerList[], 0);
					});
					break;
				case '重置已发送':
					this.$nextTick(() => {
						this.modifySendStatus(this.multipleSelection as ApiCustomerList[], 1);
					});
					break;
				case "下级":
					this.$router.push({
						name: 'Subordinate',
						query: {
							id: row.userId
						}, params: {
							id: row.userId
						}
					})
					break;
				case "关联上下级":
					this.$router.push({
						name: 'Subordinate',
						query: {
							id: row.userId
						}, params: {
							id: row.userId
						}
					})
					break;
				case "活动通知":
					const childComp = this.$refs.activitiesMessage as any
					childComp.openDialog(this.multipleSelection as ApiCustomerList[], 1)
					break;
				case '设为业务员':
					this.$nextTick(() => {
						this.setSalespersons(this.multipleSelection as ApiCustomerList[], 1);
					});
					break;
				case '取消业务员':
					this.$nextTick(() => {
						this.setSalespersons(this.multipleSelection as ApiCustomerList[], 0);
					});
					break;
				default:
					break;
			}
		} else if (command === "活动通知" && this.multipleSelection.length === 0) {
			const childComp = this.$refs.activitiesMessage as any
			childComp.openDialog(this.multipleSelection as ApiCustomerList[], 1)
		} else {
			this.$message.info('请至少选择一个客户');
		}
	}

	/**
	 * 批量设为业务员；批量取消业务员
	 * @param {orderData} 订单数据 如果没有参数为批量操作
	 */
	setSalespersons(customerList?: ApiCustomerList[], status?: number) {
		let data = {
			saleFlag: status,
			accountIdList: ''
		}
		this.$confirm(status == 1 ? "确定批量设为业务员" : "确定批量取消业务员", "提示", {
			confirmButtonText: "确定",
			cancelButtonText: "取消",
			type: "warning",
		})
			.then(() => {
				//console.log("modifySendStatus-customerList", customerList)
				let accountIdList: any[] = [];
				customerList && customerList.map(i => {
					//console.log("i", i)
					accountIdList.push(i.id)
				})
				data.accountIdList = JSON.stringify(accountIdList)
				updateMemberSaleFlag(data)
					.then(() => {
						//this.getOrders(Object.assign({}, this.query, this.$route.query));
						this.$message.success(status == 1 ? "批量设为业务员成功" : "批量取消业务员成功");
						this.getDataList(this.pageNum);
					})
					.catch(err => {
						this.$message.warning(err);
					});
			})
			.catch(() => {
				//
			});
	}
	/**
	 * 修改客户发送状态
	 * @param {orderData} 订单数据 如果没有参数为批量操作
	 */
	modifySendStatus(customerList?: ApiCustomerList[], status?: number) {
		let data = {
			status: status,
			accountIdList: ''
		}
		this.$confirm("确定修改客户发送状态？", "提示", {
			confirmButtonText: "确定",
			cancelButtonText: "取消",
			type: "warning",
		})
			.then(() => {
				//console.log("modifySendStatus-customerList", customerList)
				let accountIdList: any[] = [];
				customerList && customerList.map(i => {
					//console.log("i", i)
					accountIdList.push(i.id)
				})
				data.accountIdList = JSON.stringify(accountIdList)
				updateMemberSendStatus(data)
					.then(() => {
						//this.getOrders(Object.assign({}, this.query, this.$route.query));
						this.$message.success("修改会员发送状态成功");
						this.getDataList(this.pageNum);
					})
					.catch(err => {
						this.$message.warning(err || "修改会员发送状态失败");
					});
			})
			.catch(() => {
				//
			});
	}


	itemClick(command: string, row: ApiCustomerList) {
		this.blackListVisible = true;
		this.$nextTick(() => {
			this.blackListDialogRef.init([row], 1);
		});
		// switch (command) {
		//   case '加入黑名单':
		//     this.blackListVisible = true;
		//     this.$nextTick(() => {
		//       this.blackListDialogRef.init([row], 1);
		//     });
		//     break;
		// }
	}

	itemCommand(command: string, row: ApiCustomerList) {

		switch (command) {
			case '加入黑名单':
				this.blackListVisible = true;
				this.$nextTick(() => {
					this.blackListDialogRef.init([row], 1);
				});
				break;
			case "3":
				this.memberUpgrade(row);
				break;
			case "5":
				this.memberRenews();
				break;
			case "重置未发送":
				let apiCustomerList: ApiCustomerList[] = [];
				apiCustomerList.push(row)
				this.modifySendStatus(apiCustomerList, 0);
				break;
			case "重置已发送":
				let apiCustomerList2: ApiCustomerList[] = [];
				apiCustomerList2.push(row)
				this.modifySendStatus(apiCustomerList2, 1);
				break;
			case "下级":
				this.$router.push({
					name: 'Subordinate',
					query: {
						id: row.userId
					}, params: {
						id: row.userId
					}
				})
				break;
			case "关联上下级":
				this.dialogVisibleSubordinates = true
				this.objData = row
				// this.$router.push({
				// 	name: 'Subordinate',
				// 	query: {
				// 		id: row.userId
				// 	}, params: {
				// 		id: row.userId
				// 	}
				// })
				break;
			case "编辑":
				this.$refs.editUserInfo.openDialog(row.userId)
				break;
			case "updateCommission":
				this.dialogVisibleUpdateCommission = true
				this.updateCommissionData = row
				break;
			case "updateGoldBean":
				this.dialogVisibleUpdateGoldBean = true
				this.updateGoldBeanData = row
				break;
			case "实名审核":
				this.formName = {
					id: row.id,
					name: row.userName,
					idNumber: row.card,
					frontImage: row.idCardFaceUrl,
					backImage: row.idCardBackUrl,
					status: '2',
					comment: ''
				}
				this.dialogVisibleName = true
				break;
			case "设为业务员":
				this.setSalespersons2(row, 1)
				break;
			case "驳回":
				this.currentRejectId = row.id;
				this.dialogVisibleReject = true;
				break;
		}
	}

	/**
	 * 批量设为业务员；批量取消业务员
	 * @param {orderData} 订单数据 如果没有参数为批量操作
	 */
	setSalespersons2(row: any, status?: number) {
		let data = {
			saleFlag: status,
			accountIdList: JSON.stringify([row.id])
		}
		this.$confirm(status == 1 ? "确定设为业务员" : "确定取消业务员", "提示", {
			confirmButtonText: "确定",
			cancelButtonText: "取消",
			type: "warning",
		})
			.then(() => {
				updateMemberSaleFlag(data)
					.then(() => {
						//this.getOrders(Object.assign({}, this.query, this.$route.query));
						this.$message.success(status == 1 ? "设为业务员成功" : "取消业务员成功");
						this.getDataList(this.pageNum);
					})
					.catch(err => {
						this.$message.error(err);
					});
			})
			.catch(() => {
				//
			});
	}
	/**
	 * 关联上下级弹窗显示
	 */
	clickVisible(e: boolean) {
		console.log('eeeeeeeeeeee', e);
		this.dialogVisibleSubordinates = e
	}

	/**
	 * 修改佣金弹窗显示
	 */
	clickCommissionVisible(e: boolean) {
		console.log('eeeeeeeeeeee', e);
		this.dialogVisibleSubordinates = e
	}

	setLabel(row: ApiCustomerList) {
		this.labelVisible = true;
		this.$nextTick(() => {
			this.labelDialogRef.init([row]);
		});
	}

	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.pageSize = val;
		this.getDataList(1);
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.pageNum = val;
		this.getDataList(val);
	}

	/**
 * 导出客户列表
 */
	async exportData() {
		this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const form = this.dataForm;
			let shopIdsParam = ''
			if (form.shopIds) {
				// 将shopIds 数组 转换成用英文逗号分隔字符串
				shopIdsParam = form.shopIds.join(',')
			}
			const params = {
				size: this.pageSize,
				sortType: form.sortType,
				memberNumber: form.memberNumber,
				rankCode: form.rankCode,
				nikeName: form.nikeName,
				phone: form.phone,
				tagId: form.tagId,
				becomeMemberStartTime: form.becomeMemberTime !== null ? form.becomeMemberTime[0] : '',
				becomeMemberEndTime: form.becomeMemberTime !== null ? form.becomeMemberTime[1] : '',
				orderSuccessStartTime: form.orderSuccessTime !== null ? form.orderSuccessTime[0] : '',
				orderSuccessEndTime: form.orderSuccessTime !== null ? form.orderSuccessTime[1] : '',
				shopIds: shopIdsParam,
			};

			exportCustomerList(params).then((res) => {
				var blob = new Blob([res.data], {
					type: "application/x-msdownload;charset=UTF-8",
				});
				// 创建一个blob的对象链接
				const url = window.URL.createObjectURL(blob);
				const link = document.createElement('a');
				link.href = url;
				// 把获得的blob的对象链接赋值给新创建的这个 a 链接
				let now = new Date();
				let timestamp = now.getTime();
				link.setAttribute('download', '客户列表_' + timestamp + '.xls'); // 设置下载文件名
				document.body.appendChild(link);

				// 触发下载
				link.click();
				// 清理
				document.body.removeChild(link);
				window.URL.revokeObjectURL(url);
				this.$message.success('导出成功');

			}).catch((err) => {
				this.$message.error("导出失败");
			});
		}).catch(() => {
			// 用户取消导出
		});
	}

	/**
	 * 获取会员卡信息
	 */
	pomemberList() {
		getMemberList({ memberTypeId: this.memberTypeId }).then(res => {
			this.memberData = res.data;
			//console.log("dataTemp1")
			//console.log(this.memberData)
		});
	}

	//获取会员卡权益信息
	Rightsinterests() {
		rightsinterests().then(res => {
			this.modifyData = res.data
			// console.log("dataTemp2")
			// console.log(this.modifyData)
		});
	}
	// 获取佣金规则
	getRule() {
		getCommissionRule({}).then((res) => {
			if (res.data == null) {
				return
			}
			//console.log('ddddddddddddd',res.data);
			this.ruleShow = res.data.ruleType
			// this.ruleForm=res.data;
		}).catch((err) => {
			this.$message(err)
		})
	}

	// 修改佣金规则设置的确定按钮
	modifyCommissionRule() {
		this.commissionSetting = false
		this.commissionRow.ruleType = this.ruleForm.ruleType
		this.commissionRow.parentReceive = this.ruleForm.parentReceive
		this.commissionRow.aboveParentReceive = this.ruleForm.aboveParentReceive
		this.commissionRow.minPayAmount = this.ruleForm.minPayAmount
	}

	//打开佣金规则设置
	settingRule(scope) {
		this.commissionSetting = true
		//this.RightsList = scope.rightsAndInterests
		this.ruleForm.ruleType = scope.ruleType
		this.ruleForm.parentReceive = scope.parentReceive
		this.ruleForm.aboveParentReceive = scope.aboveParentReceive
		this.ruleForm.minPayAmount = scope.minPayAmount
		this.commissionRow = scope
		//console.log(this.RightsList)
	}

	//打开更改会员权益弹框
	modifyBtn(scope) {
		this.modify = true
		this.RightsList = scope.rightsAndInterests
		//console.log(this.RightsList)
	}

	change(val) {
		console.log(val)
	}

	//会员权益选择模块
	selectBox(val) {
		console.log(this.rightsAnds)
		console.log(this.RightsList)
		//  	console.log("val",val);
		//  	// 首先要初始化这两个数组，因为当取消checkbox的时候，数组中就不能有当前取消的值。
		//    this.values = [];
		//    this.labels= [];
		// val.forEach(item=>{
		//   const value = item.split(':')[0];
		//   const label= item.split(':')[1];
		//   this.values.push(value);
		//   this.labels.push(label);
		// });
		// console.log("this.values",this.values);
		// console.log("this.labels",this.labels);
	}

	//会员卡权益选择确定
	modifytest() {
		this.modify = false

	}

	Subbtn() {
		// 防止重复点击
		if (this.submitLoading) {
			return;
		}
		this.SubMemberList()
	}

	//提交功能的接口对接
	SubMemberList() {
		console.log("-===========================提交", this.memberData, "=================-");

		// 设置loading状态
		this.submitLoading = true;

		for (let i = 0; this.memberData.length > i; i++) {
			if (this.memberData[i].memberLevel == '' || this.memberData[i].memberLevel == null) {
				this.$message.error("等级名称不能为空！");
				this.submitLoading = false; // 重置loading状态
				return
			}
		}
		//console.log("this.memberData", this.memberData, "this.memberData");
		//console.log("提交了！！！！！！！=============");
		this.memberList = this.memberData;
		const merlist = this.memberList;

		SubMemberList(merlist)
			.then(() => {
				this.$message.success("设置成功");
				//console.log(merlist)
				this.pomemberList();
			})
			.catch(err => {
				this.$message.error(err || "网络错误");
			})
			.finally(() => {
				// 0.5秒后才能再次点击
				setTimeout(() => {
					this.submitLoading = false;
				}, 500);
			});
	}

	//更改等级的对话框显示方法
	memberUpgrade(row) {
		//console.log(row)
		this.EditwareList = [row]
		// console.log(this.EditwareList)
		// updateMemberLeve()
		this.dialogVisible = !this.dialogVisible;
		// console.log('dia=',this.dialogVisible)
		getMemberLevelList().then(res => {
			this.gtlevel = res.data
		})
	}

	//更改会员等级
	determine() {
		//console.log(this.id)
		const parem = {
			id: this.EditwareList[0].id,
			memberLevelId: this.id
		}
		updateMemberLeve(parem).then(res => {
			this.$message({
				message: '修改成功',
				type: 'success'
			});
			this.getDataList(1)
		}).catch(err => {
			this.$message.error(err || "错误");
		});
		this.dialogVisible = false
	}



	// getMemberLevelList(){

	// }

	//会员卡分页的输入框
	onSubmit() {
		console.log(this.vipCard.memberName);
		console.log(this.vipCard.effectiveDate);
	}
	//会员卡页面修改的对话框

	//会员卡停用按钮
	deactivate(scope) {
		this.statList.disable = scope.disable
		this.statList.memberId = scope.id
		this.statusList = this.statList;
		//console.log(this.statList.disable)
		//  console.log(this.statusList.memberId)
		// console.log(this.statusList)
		this.$confirm('更改等级状态小程序端将停用/启用该等级，确定继续更改吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		})
			.then(() => {
				if (scope.disable == 0) {
					scope.disable = 1
				} else if (scope.disable == 1) {
					scope.disable = 0
				}
				const statusList = {
					id: scope.id,
					disable: scope.disable
				};
				memberlevel(statusList)
					.then((res) => {
						// console.log('sfsdf=========================',res);
						this.$message.success(res.data);
					})
					.catch(err => {
						scope.disable = scope.disable == 1 ? 0 : 1;
						this.$message.error(err + scope.disable == 1 ? "  启用失败!" : "  停用失败!" || "网络错误");
					});
			})
	}

	delMemberLevel(scope) {
		this.$confirm('确定删除等级吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			delMemberLevel(scope)
				.then((res) => {
					// console.log('sfsdf=========================',res);
					this.$message.success(res.msg);
					this.pomemberList();
				})
				.catch(err => {
					this.$message.error(err || "网络错误");
				});
		})
	}

	//会员卡停用数据对接
	// memberlevel(){
	// 	if(this.statList.disable == 0){
	// 	 this.statList.disable = 1
	// 	}else if(this.statList.disable == 1){
	// 	 	 this.statList.disable = 0
	// 	 	}
	// 		console.log(this.statList.disable)
	// 	const statusList = {
	// 	id : this.statList.memberId,
	// 	disable : this.statList.disable
	// 	};
	// 	memberlevel(statusList)
	// 	.then((res) => {
	// 	  this.$message.success(res.data);
	// 	})
	// 	.catch(err => {
	// 	  this.$message.error(err || "网络错误");
	// 	});
	// }

	//动态表单：增加
	addDomain() {
		debugger
		let rightsAndInterest = JSON.parse(JSON.stringify(this.modifyData));
		debugger
		this.memberData.push({
			// memberLevel: '',
			// membercardLive: '',
			// disable: 0,
			// level: this.memberData.length,
			// key: Date.now(),
			// rightsAndInterests: rightsAndInterest,
			"id": "",
			"memberLevel": "",
			"level": this.memberData.length,
			"disable": 0,
			"stockFlag": 0,
			"memberTypeId": this.memberTypeId,
			"regionType": '',
			"memberFlag": 1,
			"rightsAndInterests": rightsAndInterest
		})
	}
	/**
	 * 设置默认会员等级
	 * @param scope
	 */
	setDefaultLevel(scope) {
		this.$confirm('确定设置为默认会员等级吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			setDefaultLevel(scope)
				.then((res) => {
					this.$message.success(res.msg);
					this.pomemberList();
				})
				.catch(err => {
					this.$message.error(err || "网络错误");
				});
		})
	}
	/**
 * 关闭修改佣金弹窗
 */
	updateCommissionHandleClose() {
		this.dialogVisibleUpdateCommission = false;
	}

	/**
	 * 修改佣金
	 */
	updateCommissionSubmit() {

		//console.log("this.addModel.serveOption", this.addModel.serveOption);
		/*if (!this.addModel.serveOption.orderId) {
		  this.$message.warning("订单不能为空");
		  return;
		  }*/
		if (!this.addModel.serveOption.commissionType) {
			this.$message.warning("佣金类型不能为空");
			return;
		}
		if (!this.addModel.serveOption.amount) {
			this.$message.warning("金额不能为空或者0");
			return;
		}
		if (!this.addModel.serveOption.platformRemark) {
			this.$message.warning("修改原因不能为空");
			return;
		}
		if (!this.addModel.serveOption.remark) {
			this.$message.warning("备注不能为空");
			return;
		}
		manualAddCommission(this.addModel.serveOption)
			.then(() => {
				this.$message.success("修改成功,");
				this.dialogVisibleUpdateCommission = false;
				this.getDataList(1);
			})
			.catch(err => {
				this.$message.error(err || "网络异常");
			});
	}
	/**
	 * 修改佣金
	 */
	updateGoldBeanSubmit() {

		//console.log("this.addModel.serveOption", this.addModel.serveOption);
		/*if (!this.addModel.serveOption.orderId) {
		  this.$message.warning("订单不能为空");
		  return;
		  }*/
		if (!this.updateGoldBean.serveOption.type) {
			this.$message.warning("变更方式");
			return;
		}
		if (!this.updateGoldBean.serveOption.amount) {
			this.$message.warning("金额不能为空或者0");
			return;
		}
		if (!this.updateGoldBean.serveOption.remark) {
			this.$message.warning("备注不能为空");
			return;
		}
		addGoldBean(this.updateGoldBean.serveOption)
			.then(() => {
				this.$message.success("修改成功,");
				this.dialogVisibleUpdateGoldBean = false;
				this.getDataList(1);
			})
			.catch(err => {
				this.$message.error(err || "网络异常");
			});
	}

}
</script>

<style lang="scss" scoped>
@import '../../../assets/styles/cutomer/customer';

.multipleTable {
	font-size: 14px;

}


.distributionSettings {
	background: #E6E6E6;

	height: 30px;
	display: flex;
	align-items: center;
	padding-left: 10px;

	div {
		color: #101010;
		border-left: 3px solid #09F016;
		padding-left: 10px;
	}

}

.distributionType {
	margin: 10px 0 10px 20px;

	span {
		margin-right: 10px;
	}
}

.DistributionCommission {
	margin-left: 5px;

	.commission__top {
		display: flex;
		border: 1px solid #a0b3d6;
		margin-bottom: 2px;

	}

	span {
		display: inline-block;
		text-align: center;
		background: #bbbbbb;
		line-height: 20px;
		color: rgba(33, 31, 31, 1);

	}

	.span1 {
		width: 200px;

	}

	.span2 {
		width: 50px;
	}

	.distribution__commission {

		width: 295px;
		height: 20px;
		line-height: 20px;
		padding: 3px;
		font-size: 12px;
		outline: none;
		text-align: center;
		border: none;
		// overflow-x: hidden;
		// overflow-y: auto;
	}
}

.first {
	display: inline-block;
	width: 100px;
	margin-left: 10px;
	color: #f50101;
}

.illustrate {
	color: #585454;
}
</style>
