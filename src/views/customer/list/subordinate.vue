<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:09
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-12 13:17:51
-->
<template>
	<div class="customer">
				<div class="memberList">
					<div class="line"></div>
					<m-card class="form" :needToggle="true">
						<el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
							<el-row :gutter="40">
								<el-col :span="10">
									<el-form-item label="微信昵称"><el-input v-model="dataForm.nikeName" clearable
											placeholder="请输入会员昵称" /></el-form-item>
								</el-col>
								<el-col :span="10">
									<el-form-item label="手机号"><el-input v-model="dataForm.phone" clearable
											placeholder="请输入会员手机号" /></el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="40">
								<el-col :span="10">
									<el-form-item label="上次交易时间">
										<el-date-picker v-model="dataForm.orderSuccessTime"
											:default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss"
											style="width: 256px" type="daterange" range-separator="-"
											start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="标  签">
										<el-select v-model="dataForm.tagId" placeholder="请选择标签" style="width: 256px"
											clearable>
											<el-option label="全部" :value="null" />
											<el-option v-for="tag in allTagList" :key="tag.tagId" :label="tag.tagName"
												:value="tag.tagId" />
										</el-select>
									</el-form-item>
								</el-col>
							</el-row>
							<el-button type="primary" style="margin-left:100px" @click="getDataList(1)">搜索</el-button>
						</el-form>
					</m-card>
					<div>
						<el-tabs v-model="activeNames" @tab-click="handleClicks">
						  <el-tab-pane v-for="(item, index) in lists" :key="index" :label="item.name" :name="item.name"
						    :id="item.id"></el-tab-pane>
						</el-tabs>
					</div>
					<el-row class="customer__filterForm" type="flex" justify="space-between">
						<el-col :span="4">
							<set-drop setName="设置标签" :dropdownList="couponDropList" v-if="isSupper || setLableButton"
								@setClick="batchCouponClick" @command="batchCouponCommand($event, row)" />
							<set-drop setName="设置标签" :dropdownList="couponDropList"
								v-else-if="isSupper || addBackListButton || modifySendStatusButton"
								@setClick="batchCouponClick" @command="batchCouponCommand($event, row)" />
						</el-col>
						<el-col :span="4">
							<el-select v-model="dataForm.sortType" placeholder="请选择排序方式"
								@change="handleCurrentChange(1)">
								<el-option v-for="item in sortOptions" :key="item.value" :label="item.label"
									:value="item.value"></el-option>
							</el-select>
						</el-col>
					</el-row>
					<m-table :data.sync="dataList" :selection="true" :checked-item.sync="multipleSelection"
						slot="content" needHoverBorder ref="customerListRef" class="customerList">
						<template v-slot:header="{ row }">
							<div style="width: 40%">
								<span>会员卡号:{{ row.cardNumber }}</span>
							</div>
							<div style="width: 30%">
								<span>会员等级:{{ row.memberLevelName }}</span>
							</div>
							<div style="width: 30%">
								对接状态：<span v-if="row.sendStatus===1">已发送</span><span v-else>未发送</span>
							</div>
							<div style="width: 50%">
								<span>成为用户时间:{{ row.firstLoginTime }}</span>
							</div>
						</template>
						<m-table-column prop="userName" label="客户信息" :showsSlection="true" width="300">
							<template v-slot="{ row }">
								<div class="info">
									<img class="info__img" :src="row.avatarUrl" alt />
									<div class="info__msg">
										<div class="info__msg--text">
											<div style="width: 350px">{{ row.nikeName }}</div>
											<div v-if="row.phone" style="width: 350px">({{ row.phone }})</div>
										</div>
										<div class="info__msg--tags" v-if="isSupper || setLableButton">
											<span v-if="row.userTagVos !== null && row.userTagVos.length > 0"
												@click="setLabel(row)" class="pointer">
												<el-button type="text">
													{{
														row.userTagVos
															.map(tag => tag.tagName)
															.slice(0, 2)
															.join(' ; ')
													}}
												</el-button>
												<span>等共{{ row.userTagVos.length }}个标签</span>
											</span>
											<el-button type="text" @click="setLabel(row)"
												v-if="row.userTagVos === null || (row.userTagVos !== null && row.userTagVos.length === 0)">
												请选择所属标签
											</el-button>
											<i class="el-icon-caret-bottom pointer" @click="setLabel(row)" />
										</div>
										<div class="info__msg--tags" v-else>
											<span v-if="row.userTagVos !== null && row.userTagVos.length > 0"
												class="pointer">
												<el-button type="text">
													{{
														row.userTagVos
															.map(tag => tag.tagName)
															.slice(0, 2)
															.join(' ; ')
													}}
												</el-button>
												<span>等共{{ row.userTagVos.length }}个标签</span>
											</span>
											<el-button type="text"
												v-if="row.userTagVos === null || (row.userTagVos !== null && row.userTagVos.length === 0)">
												暂无所属标签
											</el-button>
											<i class="el-icon-caret-bottom pointer" />
										</div>
										<div><span>一级：{{row.oneTeamNum}}</span><span
												style="margin-left: 20px;">二级：{{row.twoTeamNum}}</span> </div>
									</div>
								</div>
							</template>
						</m-table-column>
						<m-table-column prop="userName" label="购次">
							<template v-slot="{ row }">
								<span>{{ row.consumeNum }}</span>
							</template>
						</m-table-column>
						<m-table-column prop="userName" label="交易总额">
							<template v-slot="{ row }">
								<span>{{ row.consumeTotleMoney }}</span>
							</template>
						</m-table-column>
						<m-table-column prop="userName" label="推荐人">
							<template v-slot="{ row }">
								<span>{{ row.recommendName }}</span>
							</template>
						</m-table-column>
						<m-table-column prop="userName" label="上次交易时间" width="160">
							<template v-slot="{ row }">
								<span>{{ row.orderLastDealTime }}</span>
							</template>
						</m-table-column>
						<m-table-column prop="userName" label="佣金">
							<template v-slot="{ row }">
								<div>
									<div>可提现：{{ row.currentCommission?row.currentCommission:'0' }}</div><br>
									<div>累计：{{ row.commission }}</div>
								</div>


							</template>
						</m-table-column>
					</m-table>
					<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
						@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
					<black-list ref="blackListDialogRef" v-if="blackListVisible" @refreshDataList="getDataList" />
					<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList"
						@refreshDataList="getDataList(1)" />
					<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList"
						@refreshDataList="getDataList(1)" />
				</div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref, Watch } from "vue-property-decorator";
import PageManage from '@/components/PageManage.vue';
import BlackList, { NewCustomerTagList } from '../common/SetBlackList.vue';
import SetDrop from '@/views/customer/common/SetDrop.vue';
import SetLabel from '@/views/customer/list/components/dialog/SetTags.vue';

import { CustomerListState, ApiCustomerList, CustomerTagList, CustomerRank, ApiMemberCardList } from './customerListType';
import { getYDERPOpen } from "@/api/sign/index";
import { ElForm } from 'element-ui/types/form';

import {
  getCustomerList,
  getAllTags,
  getMemberList,
  rightsinterests,
  SubMemberList,
  memberlevel,
  updateMemberLeve,
  getMemberLevelList,
  updateMemberSendStatus,
} from "@/api/customer/customer";
import { ElTable } from 'element-ui/types/table';
import { updateSendStatus } from "@/api/order";

	@Component({
	  components: {
	    PageManage,
	    BlackList,
	    SetDrop,
	    SetLabel
	  }
	})
export default class Index extends Vue implements CustomerListState {
		@Ref()
		readonly dataFormRef! : ElForm;

		@Ref()
		readonly customerListRef! : ElTable;

		@Ref()
		readonly blackListDialogRef! : BlackList;

		@Ref()
		readonly labelDialogRef! : SetLabel;

		dataForm = {
		  nikeName: '',
		  phone: '',
		  becomeMemberTime: [],
		  orderSuccessTime: [],
		  memberNumber: null,
		  rankCode: null,
		  sortType: '',
		  tagId: null
		};

  activeNames='一级'

    lists=[{name:'一级',id:2},{name:'二级',id:1}];

		dataList : Array<ApiCustomerList> = [];

		allTagList : Array<CustomerTagList> = [];

		memberList : Array<ApiMemberCardList> = [];

		statusList : Array<ApiStatusList> = [];

		selectionList : Array<ApiCustomerList | NewCustomerTagList | ApiMemberCardList> = [];

		multipleSelection : Array<ApiCustomerList | NewCustomerTagList | ApiMemberCardList> = [];


		menuName = "客户列表";

		buttonList = [];

		isSupper = 0;

		setLableButtonCode = "customerList.memberList.setLable";

		setLableButton = false;

		addBackListButtonCode = "customerList.memberList.addBackList";

		addBackListButton = false;

		modifySendStatusButtonCode = "customerList.memberList.modifySendStatus";

		modifySendStatusButton = false;


		memberUpgradeButtonCode = "customerList.memberList.memberUpgrade";

		memberUpgradeButton = false;	


		managerVisible = false;

		blackListVisible = false;

		labelVisible = false;

		visible = true;

		activeName = 'first';

		//开启易达接口
		openYDERP = true;

		sortOptions = [
		  {
		    value: '',
		    label: '按注册时间降序'
		  },
		  {
		    value: 1,
		    label: '按交易总额降序'
		  },
		  {
		    value: 2,
		    label: '按交易总额升序'
		  }
		];

		//会员列表分页的对话框模块
		dialogVisible = false;

		memberRenew = false;

		memberbtn = '';

		//更改等级模块的table
		upgrade = [{
		  membercardName: '普通卡会员',
		  membercardLive: '小金',
		  cardMode: '付费购买',
		  membercardnumber: '1000022201066089226',
		  validity: '2022-6-18 17:49:38',
		}];

		//更改等级模块的选择等级模块
		EditwareList = [];

		gtlevel = [];

		id = '';

		value = '';

		//会员等级设置模块
		memberData = [];

		//会员权益
		rightsAnds = [];

		RightsList = [];

		//积分等级设置模块
		// integral=[{
		// 	  gradename: '普通会员',
		// 	  amount: '',
		// 		benefits: '商品折扣，积分加倍等2项权益',
		// }]
		//会员卡页面开卡方式的付费购买修改模块
		modify = false;

		modifyData = '';

		checked = true;

		//会员卡页面开卡方式的积分兑换修改模块
		// pointsEx = false;
		// pointsExData = [];
		//会员卡页面禁用模块
		status = 0;

		statusText = '';

		statList = [{
		  disable: '',
		  memberId: ''
		}];


		//会员卡分页的输入框数组
		vipCard = {
		  // memberName : '',
		  effectiveDate: '',
		  duration: '0',
		};

		//会员卡分页的默认单选框
		// opening = '1';

		rankOptions : Array<CustomerRank> = [];

		/** 分页条数 */
		pageSize = 10;

		/** 分页页码 */
		pageNum = 1;

		/** 数据长度 */
		total = 0;

		/** 赠送优惠券下拉菜单 */
		couponDropList : any[] = [
		];

		parentId:string|number=''//上下级  1-下一级 2-上一级

		aboveParentId:string|number=''//上下级  1-下一级 2-上一级
		// superior:string|number='2'//上下级  1-下一级 2-上一级

		mounted() {
		  this.parentId=this.$route.query.id as string
			
		  // 间推
		  if (this.$route.query.tab == 'indirect') {
		    this.activeNames = '二级';
		    this.parentId = '';
		    this.aboveParentId = this.$route.query.id as string;
		  } else {
		    // 直推
		    this.activeNames = '一级';
		    this.parentId = this.$route.query.id as string;
		    this.aboveParentId = '';
		  }
			
		  this.getDataList(1);
		  this.buttonAuth();
		}

		buttonAuth() {
		  this.isSupper = this.$STORE.userStore.userInfo.isSupper
		  let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		  let buttonList = [];

		  authMenuButtonVos.forEach(element => {
		    buttonList.push(element.buttonCode);
		  });

		  this.buttonList = buttonList

		  var setLableButtonData = buttonList.find(e => e == this.setLableButtonCode);

		  if (setLableButtonData != null && setLableButtonData != undefined) {
		    this.setLableButton = true;
		  }

		  var addBackListButtonData = buttonList.find(e => e == this.addBackListButtonCode);

		  if (addBackListButtonData != null && addBackListButtonData != undefined) {
		    this.addBackListButton = true;
		  }

		  var modifySendStatusButtonData = buttonList.find(e => e == this.modifySendStatusButtonCode);
		  if (modifySendStatusButtonData != null && modifySendStatusButtonData != undefined) {
		    this.modifySendStatusButton = true;
		  }

		  var memberUpgradeButtonData = buttonList.find(e => e == this.memberUpgradeButtonCode);
		  if (memberUpgradeButtonData != null && memberUpgradeButtonData != undefined) {
		    this.memberUpgradeButton = true;
		  }
		  if (this.isSupper || this.addBackListButton) {
		    this.couponDropList.push({
		      command: '加入黑名单',
		      disabled: false,
		      show: true,
		      text: '加入黑名单'
		    })
		  }
		  if (this.isSupper || this.modifySendStatusButton) {
		    this.couponDropList.push({
		      command: '重置未发送',
		      disabled: false,
		      show: true,
		      text: '重置未发送'
		    })
		    this.couponDropList.push({
		      command: '重置已发送',
		      disabled: false,
		      show: true,
		      text: '重置已发送'
		    })
		  }
		}

		// 导航栏切换分页
		handleClick(tab, event) {
		  console.log(event)
		  if (this.activeName == 'second') {
		    this.pomemberList()
		    this.Rightsinterests()
		    // this.getmemberList()
		  }
		}

		/**
     * 顶部专区选择
     */
		handleClicks(tab: { index: number }) {
		 if(tab.index==0){
			 this.parentId = this.$route.query.id as string
			 this.aboveParentId=''
		 }else if(tab.index==1){
			 this.parentId = ''
			 this.aboveParentId=this.$route.query.id as string
			 
		 }
		  console.log('顶部专区选择',tab.index,this.lists[tab.index].id);
		  this.getDataList(1);
		}

		/**
		 * 获取用户列表
		 */
		getDataList(pageNum : number) {
		  const form = this.dataForm;
		  const param = {
		    page: pageNum,
		    size: this.pageSize,
		    sortType: form.sortType,
		    memberNumber: form.memberNumber,
		    rankCode: form.rankCode,
		    nikeName: form.nikeName,
		    parentId:this.parentId,
		    aboveParentId:this.aboveParentId,
		    phone: form.phone,
		    tagId: form.tagId,
		    becomeMemberStartTime: form.becomeMemberTime !== null ? form.becomeMemberTime[0] : '',
		    becomeMemberEndTime: form.becomeMemberTime !== null ? form.becomeMemberTime[1] : '',
		    orderSuccessStartTime: form.orderSuccessTime !== null ? form.orderSuccessTime[0] : '',
		    orderSuccessEndTime: form.orderSuccessTime !== null ? form.orderSuccessTime[1] : ''
		  };
			
		  getCustomerList(param).then(res => {
		    this.dataList = res.data.list;
		    this.pageSize = res.data.size;
		    this.pageNum = res.data.current;
		    this.total = res.data.total;
		  });
		  this.getTags();
		  this.getYDERPOpen();
		}

		/**
		 * @method getTags
		 * @description 获取所有标签
		 */
		getTags() {
		  getAllTags().then(res => {
		    this.allTagList = res.data;
		  });
		}

		/**
		 * @method getYDERPOpen
		 * @description 获取易达接口是否开启
		 */
		getYDERPOpen() {
		  getYDERPOpen().then(res => {
		    this.openYDERP = res.data;
		  });
		}

		batchCouponClick() {
		  if (this.multipleSelection.length > 0) {
		    this.labelVisible = true;
		    this.$nextTick(() => {
		      this.labelDialogRef.init(this.multipleSelection as ApiCustomerList[]);
		    });
		  } else {
		    this.$message.warning('至少选择一个客户');
		  }
		}

		batchCouponCommand(command : string) {
		  if (this.multipleSelection.length > 0) {
		    switch (command) {
		      case '加入黑名单':
		        this.blackListVisible = true;
		        this.$nextTick(() => {
		          this.blackListDialogRef.init(this.multipleSelection as ApiCustomerList[], 1);
		        });
		        break;
		      case '重置未发送':
		        this.$nextTick(() => {
		          this.modifySendStatus(this.multipleSelection as ApiCustomerList[], 0);
		        });
		        break;
		      case '重置已发送':
		        this.$nextTick(() => {
		          this.modifySendStatus(this.multipleSelection as ApiCustomerList[], 1);
		        });
		        break;
		      default:
		        break;
		    }
		  } else {
		    this.$message.info('请至少选择一个客户');
		  }
		}

		/**
		 * 修改客户发送状态
		 * @param {orderData} 订单数据 如果没有参数为批量操作
		 */
		modifySendStatus(customerList ?: ApiCustomerList[], status ?: number) {
		  let data = {
		    status: status,
		    accountIdList: ''
		  }
		  this.$confirm("确定修改客户发送状态？", "提示", {
		    confirmButtonText: "确定",
		    cancelButtonText: "取消",
		    type: "warning",
		  })
		    .then(() => {
		      //console.log("modifySendStatus-customerList", customerList)
		      let accountIdList : any[] = [];
		      customerList && customerList.map(i => {
		        //console.log("i", i)
		        accountIdList.push(i.id)
		      })
		      data.accountIdList = JSON.stringify(accountIdList)
		      updateMemberSendStatus(data)
		        .then(() => {
		          //this.getOrders(Object.assign({}, this.query, this.$route.query));
		          this.$message.success("修改会员发送状态成功");
		          this.getDataList(this.pageNum);
		        })
		        .catch(err => {
		          this.$message.warning(err || "修改会员发送状态失败");
		        });
		    })
		    .catch(() => {
		      //
		    });
		}




		setLabel(row : ApiCustomerList) {
		  this.labelVisible = true;
		  this.$nextTick(() => {
		    this.labelDialogRef.init([row]);
		  });
		}

		/**
		 * @method handleSizeChange
		 * @description 每页 条
		 */
		handleSizeChange(val : number) {
		  this.pageSize = val;
		  this.getDataList(1);
		}

		/**
		 * @method handleCurrentChange
		 * @description 当前页
		 */
		handleCurrentChange(val : number) {
		  this.pageNum = val;
		  this.getDataList(val);
		}

		/**
		 * 获取会员卡信息
		 */
		pomemberList() {
		  getMemberList().then(res => {
		    this.memberData = res.data
		    //console.log("dataTemp1")
		    //console.log(this.memberData)
		  });
		}

		//获取会员卡权益信息
		Rightsinterests() {
		  rightsinterests().then(res => {
		    this.modifyData = res.data
		    console.log("dataTemp2")
		    // console.log(this.modifyData)
		  });
		}

		//打开更改会员权益弹框
		modifyBtn(scope) {
		  this.modify = true
		  this.RightsList = scope.rightsAndInterests
		  //console.log(this.RightsList)
		}

		change(val) {
		  console.log(val)
		}

		//会员权益选择模块
		selectBox(val) {
		  console.log(this.rightsAnds)
		  console.log(this.RightsList)
		  //  	console.log("val",val);
		  //  	// 首先要初始化这两个数组，因为当取消checkbox的时候，数组中就不能有当前取消的值。
		}

		//会员卡权益选择确定
		modifytest() {
		  this.modify = false

		}

		Subbtn() {
		  this.SubMemberList()
		}

		//提交功能的接口对接
		SubMemberList() {
		  console.log("-===========================提交", this.memberData, "=================-");
		  for (let i = 0; this.memberData.length > i; i++) {
		    if (this.memberData[i].memberLevel == '' || this.memberData[i].memberLevel == null) {
		      this.$message.error("等级名称不能为空！");
		      console.log("等级名称不能为空！=================");
		      return
		    }
		  }
		  console.log("this.memberData", this.memberData, "this.memberData");
		  console.log("提交了！！！！！！！=============");
		  this.memberList = this.memberData;
		  const merlist = this.memberList;

		  SubMemberList(merlist)
		    .then(() => {
		      this.$message.success("设置成功");
		      console.log(merlist)
		    })
		    .catch(err => {
		      this.$message.error(err || "网络错误");
		    });
		}

		//更改等级的对话框显示方法
		memberUpgrade(row) {
		  console.log(row)
		  this.EditwareList = [row]
		  console.log(this.EditwareList)
		  // updateMemberLeve()
		  this.dialogVisible = !this.dialogVisible;
		  getMemberLevelList().then(res => {
		    this.gtlevel = res.data
		  })
		}

		//更改会员等级
		determine() {
		  console.log(this.id)
		  const parem = {
		    id: this.EditwareList[0].id,
		    memberLevelId: this.id
		  }
		  updateMemberLeve(parem).then(res => {
		    this.$message({
		      message: '修改成功',
		      type: 'success'
		    });
		    this.getDataList(1)
		  }).catch(err => {
		    this.$message.error(err || "错误");
		  });
		  this.dialogVisible = false
		}
}
</script>

<style lang="scss" scoped>
	@import '../../../assets/styles/cutomer/customer';
</style>