<!--
 * @description: 修改佣金
-->
<template>
  <div class="diaLog">
    <div class="diaLog__content">
      <span class="diaLog__content--left">会员名称:</span>
      {{ editCurrent.nikeName }}
    </div>
    
    <div class="diaLog__content">
      <span class="diaLog__content--left">
        <span class="star">*</span>变更方式:
      </span>
      <el-select v-model="serveOption.type" placeholder="请选择变更方式" style="width:250px">
        <el-option v-for="item in commissionTypeList" :key="item.id" :label="item.text" :value="item.id"></el-option>
      </el-select>
    </div>

    <div class="diaLog__content">
      <span class="diaLog__content--left">
        <span class="star">*</span>本次修改金额:
      </span>
      <el-input v-model="serveOption.amount" placeholder="请输入本次修改金额" style="width:250px"></el-input>
      <!-- <span style="margin-left:10px; font-size: 11px; color: red">实际提现金额=本次金额*提现率</span> -->
    </div>
   
    <div class="diaLog__content">
      <span class="diaLog__content--left">
        <span class="star">*</span>备注:
      </span>
      <el-input type="textarea" :rows="2" v-model="serveOption.remark" placeholder="请填写备注"
        style="width:320px"></el-input>
    </div>
   
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import { ApiCustomerList } from "@/views/customer/list/components/dialog/associateSubordinatesType";
import { searchByMiniAccount } from "@/api/order";

@Component
export default class AddModel extends Vue {
  @Prop({
    type: Boolean,
    default: {},
  })
  addDialog!: boolean;

  @Prop({
    type: Object,
    default: {},
  })
  editCurrent!: any;

  orderDialogVisible: boolean = false

  formInline = {
    orderId: '',
    userName: '',
    phone: '',
    note: '',
  }
  /** 分页条数 */
  pageSize = 10;
  /** 分页页码 */
  pageNum = 1;
  /** 记录总数 */
  total = 0;

  selectionList = []

  orderData = []

  choseOrderId = null

  serveOption = {
    id: this.editCurrent.id,
    userId: this.editCurrent.userId,
    type: this.editCurrent.type,
    amount: this.editCurrent.amount,
    remark:""
  };

  @Watch("addDialog")
  getAddDialog() {
    //console.log('this.editCurrent', this.editCurrent)
    this.serveOption.id = this.editCurrent.id
    this.serveOption.userId = this.editCurrent.userId
    this.serveOption.type = this.editCurrent.type
    this.serveOption.amount = this.editCurrent.amount
    this.serveOption.remark = ""
  }

  /** 佣金类型选择 */
  commissionTypeList = [
    {
      "id": 1,
      "text": '增加'
    },
    {
      "id": 2,
      "text": '减少'
    },
    
  ];

  /*mounted() {

  }*/



  /**
   * 选择订单按钮
   */
  selectOrder() {
    this.orderDialogVisible = true

    this.formInline = {
      orderId: '',
      userName: '',
      phone: '',
      note: '',
    }
    this.pageNum = 1
    this.pageSize = 10;
    this.getOrderList(this.pageNum)
  }


  /**关闭订单列表弹窗*/
  cancelOrder() {
    this.$refs.multipleTable.clearSelection();
    this.selectionList = []
    this.orderDialogVisible = false
  }

  onSubmit() {
    this.pageNum = 1
    this.getOrderList(this.pageNum, this.formInline)
    //console.log('submit!', this.formInline);
  }

  getOrderList(pageNum: any, formInline = false) {

    const param = {
      current: pageNum,
      size: this.pageSize,
      miniAccountShopUserId: this.editCurrent.userId
    };
    if (formInline) {
      param.orderId = formInline.orderId
      param.userName = formInline.userName
      param.phone = formInline.phone
      param.note = formInline.note
    }
    searchByMiniAccount(param).then(res => {
      this.orderData = res.data.list;

      this.pageSize = res.data.size;
      this.pageNum = param.current;
      this.total = res.data.total;

    }).catch((err) => {
      this.$message.warning(err);
    });
  }

  getRowKey(row) {
    return row.id
  }

  // 单选框
  handleRadioClick(row) {
    this.choseOrderId = row.id;
    console.log("handleRadioClick", this.choseOrderId)
  }

  /**
   * @method handleSizeChange
   * @description 每页 条
   */
  handleSizeChange(val: number) {
    this.pageSize = val;
    this.getOrderList(1)
  }


  /**
   * @method handleCurrentChange
   * @description 当前页
   */
  handleCurrentChange(val: number) {
    this.pageNum = val;
    this.getOrderList(val)
  }

  /**确定选择订单*/
  confirmOrder(e) {
    this.orderDialogVisible = false
    this.serveOption.orderId = this.choseOrderId;

  }

}
</script>

<style lang="scss" scoped>
.star {
  color: #f56c84;
  margin-right: 3px;
}

@include b(diaLog) {
  @include e(content) {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    @include m(left) {
      width: 100px;
      display: flex;
      justify-content: flex-end;
      margin-right: 40px;
    }
  }

  @include e(agree) {
    width: 100%;
    display: flex;
    // justify-content: center;
    margin-left: 140px;

    @include m(open) {
      color: #1e7fff;
      cursor: pointer;
    }
  }
}
</style>
