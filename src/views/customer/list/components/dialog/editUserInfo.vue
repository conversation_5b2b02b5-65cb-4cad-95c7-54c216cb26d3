<template>
    <el-dialog title="编辑用户信息" :visible.sync="dialogVisible" width="50%">
        <!---->
        <div class="detail__field">
            <el-row :gutter="10">
                <el-col :span="24">
                    <b>固定信息</b>
                </el-col>
                <el-col :span="8" style="margin-bottom: 20px;"> 会员卡号：{{ userInfo.cardNumber }} </el-col>
                <template class="template__col">
                    <el-col :span="8">
                        会员昵称：{{ userInfo.nikeName }}
                    </el-col>
                    <el-col :span="8">
                        会员等级：{{ userInfo.memberLevel }}
                    </el-col>
                    <el-col :span="8">
                        成为会员时间: {{ userInfo.firstLoginTime }}

                    </el-col>
                    <el-col :span="8">
                        手机号码: {{ userInfo.phone }}
                    </el-col>
                    <el-col :span="8">
                        推荐人: {{ userInfo.parentName }}
                    </el-col>
                    <el-col :span="8">
                        交易总额: {{ userInfo.consumeTotalMoney }}
                    </el-col>
                    <el-col :span="8">
                        购次: {{ userInfo.consumeNum }}
                    </el-col>
                    <el-col :span="8">
                        会员消费额: {{ userInfo.memberMoney }}
                    </el-col>
                    <el-col :span="8">
                        总积分: {{ userInfo.allIntegral || 0 }}
                    </el-col>
                </template>


            </el-row>
            <el-divider></el-divider>
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="可编辑信息" name="editInfo">
                    <el-form ref="form" :model="userInfo" label-width="80px">
                        <el-row :gutter="10">
                            <el-col :span="24">
                                <b>可编辑信息</b>
                            </el-col>
                            <el-col :span="8"> <el-form-item label="客户名称"><el-input v-model="userInfo.userName"
                                        clearable placeholder="请输入客户名称" /></el-form-item> </el-col>
                            <el-col :span="8"> <el-form-item label="年龄"><el-input :readonly="true" v-model="age"
                                        clearable placeholder="" /></el-form-item></el-col>
                            <el-col :span="8"> <el-form-item label="性别">
                                    <el-select v-model="value" placeholder="请选择">
                                        <el-option v-for="item in options" :key="item.value" :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="生日"><el-input type="date" v-model="birthday" clearable
                                        placeholder="请输入生日" /></el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="身份证"><el-input v-model="userInfo.card" clearable
                                        placeholder="请输入身份证" /></el-form-item>
                            </el-col>
                            <el-col :span="24"><el-button type="primary" @click="editUserInfo">提交修改<i
                                        class="el-icon-upload el-icon--right"></i></el-button></el-col>
                        </el-row>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="支付信息" name="paymentInfo">
                    <div class="paymentInfo">
                        <div class="paymentInfo-img">
                            <div class="text">
                                支付宝收款码
                            </div>
                            <img v-if="userInfo.alipayAccountUrl" :src="userInfo.alipayAccountUrl"
                                alt="支付宝收款码" />
                            <div v-else class="no-image">暂无支付宝收款码</div>
                        </div>
                        <div class="paymentInfo-img">
                            <div class="text">
                                微信收款码
                            </div>
                            <img v-if="userInfo.wxAccountUrl" :src="userInfo.wxAccountUrl"
                                alt="微信收款码" />
                            <div v-else class="no-image">暂无微信收款码</div>
                        </div>
                        <div class="">
                            <el-table :data="tableData" border style="width: 100%">
                                <el-table-column label="序号" type="index" width="50"  align="center"/>
                                <el-table-column prop="bankName" label="开户行" width="150" align="center">
                                </el-table-column>
                                <el-table-column prop="bankNo" label="卡号" width="200" align="center">
                                </el-table-column>
                                <el-table-column prop="bankUserName" label="开户人姓名" width="120" align="center">
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="实名信息" name="realNameInfo">
                    <div class="realNameInfo">
                        <div class="realNameInfo-img">
                            <div class="text">
                                身份证国徽面
                            </div>
                            <img v-if="userInfo.idCardBackUrl" :src="userInfo.idCardBackUrl" alt="身份证国徽面" />
                            <div v-else class="no-image">暂无实名信息</div>
                        </div>
                        <div class="realNameInfo-img">
                            <div class="text">
                                身份证人像面
                            </div>
                            <img v-if="userInfo.idCardFaceUrl" :src="userInfo.idCardFaceUrl" alt="身份证人像面" />
                            <div v-else class="no-image">暂无实名信息</div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>



        </div>
    </el-dialog>
</template>

<script>
import { getEditUserInfo, modifyUserInfo } from '@/api/sign'


export default {
    name: 'editUserInfo', //编辑用户信息
    data() {
        return {
            dialogVisible: false,
            userId: '',//父组件传过来的用户id
            userInfo: [], //用户信息
            dataForm: {
                userName: '',
                age: '',
                gender: '',
                birthday: '',
                card: ''
            },
            options: [{
                value: '1',
                label: '男'
            }, {
                value: '2',
                label: '女'
            }],
            value: '',
            age: '',
            activeName: 'editInfo',
            tableData: [] // 银行卡信息，从API获取
        }
    },
    methods: {
        handleClick(tab, event) {
            console.log(tab, event);
        },
        GetEditUserInfo() {
            let params = { userId: this.userId }
            getEditUserInfo(params).then(res => {
                this.userInfo = res.data;
                this.age = this.userInfo.age;
                if (res.data.gender == 1) {
                    this.value = '1'
                } else if (res.data.gender == 2) {
                    this.value = '2'
                }
                // 处理银行卡信息
                if (res.data.accountBank && res.data.accountBank.length > 0) {
                    this.tableData = res.data.accountBank;
                } else {
                    this.tableData = [];
                }
            })
        },
        /**
         * 修改用户信息
         */
        editUserInfo() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    let params = {}
                    params.id = this.userInfo.id,
                        params.birthday = this.userInfo.birthday,
                        params.userName = this.userInfo.userName,
                        params.gender = this.value,
                        params.age = this.age,
                        params.card = this.userInfo.card
                    modifyUserInfo(params).then(res => {
                        this.$message.success('修改成功')
                        this.dialogVisible = false;
                        this.$emit('load')//调用父组件方法刷新页面
                    }).catch(err => {
                        this.$message.error('修改失败')
                    })
                }
            })
        },
        /**
         * 弹窗打开事件
         * @param userId 父组件传过来的用户id
         */
        openDialog(userId) {
            this.dialogVisible = true;
            this.userId = userId;
            this.GetEditUserInfo();
        }
    },
    computed: {
        birthday: {
            get() {
                return this.userInfo.birthday ? this.userInfo.birthday.slice(0, 10) : ''
            },
            set(val) {
                this.userInfo.birthday = val
                //计算年龄精确到日
                this.age = val ? new Date().getFullYear() - val.slice(0, 4) : ''
            }
        },

    }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/order/detail.scss";

.iconPC-beizhu {
    color: red;
    font-size: 22px;
    position: relative;
    bottom: -3px;
    left: -5px;
}

b {
    margin-bottom: 20px;
    display: block;
}

.template__col:nth-child(1) {
    margin-bottom: 20px;
}

.image--btn {
    cursor: pointer;
    color: rgb(23, 143, 241);
}

.detail__btn--mini {
    padding: 3px 5px;
    margin-top: 10px;
}

.detail__btn--small {
    padding: 10px 18px;
    margin-top: 10px;
}

.form__col {
    padding-right: 10px;
}

.packageDetails {
    margin-top: 10px;
}

.el-col-8 {
    margin-bottom: 20px;
}
.paymentInfo{
    display: flex;
    justify-content: space-around;
    &-img{
        .text{
            text-align: center;
            margin-bottom: 10px;
        }
        img{
            width: 120px;
            height: 120px;
        }
        .no-image{
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border: 1px dashed #ddd;
            color: #999;
            font-size: 12px;
            text-align: center;
        }
    }
}
.realNameInfo{
    display: flex;
    justify-content: space-around;
    &-img{
        .text{
            text-align: center;
            margin-bottom: 10px;
        }
        img{
            width: 200px;
            height: 120px;
        }
    }
}

</style>