<!--
 * @description: 修改佣金
-->
<template>
  <div class="diaLog">
    <div class="diaLog__content">
      <span class="diaLog__content--left">会员名称:</span>
      {{ editCurrent.nikeName }}
    </div>
    <div class="diaLog__content">
      <span class="diaLog__content--left">
        订单:
      </span>
      <el-input v-model="serveOption.orderId" placeholder="请输入订单编号" style="width:250px"></el-input>
      <el-button type="text" style="margin-left:20px" @click="selectOrder">点击选择</el-button>
    </div>
    <div class="diaLog__content">
      <span class="diaLog__content--left">
        <span class="star">*</span>佣金类型:
      </span>
      <el-select v-model="serveOption.commissionType" placeholder="请选择佣金类型" style="width:250px">
        <el-option v-for="item in commissionTypeList" :key="item.id" :label="item.text" :value="item.id"></el-option>
      </el-select>
    </div>

    <div class="diaLog__content">
      <span class="diaLog__content--left">
        <span class="star">*</span>本次修改金额:
      </span>
      <el-input v-model="serveOption.amount" placeholder="请输入本次修改金额" style="width:250px"></el-input>
      <span style="margin-left:10px; font-size: 11px; color: red">实际提现金额=本次金额*提现率</span>
    </div>
    <div class="diaLog__content">
      <span class="diaLog__content--left">
        <span class="star">*</span>修改原因:
      </span>
      <el-input type="textarea" :rows="2" v-model="serveOption.platformRemark" placeholder="请填写修改原因"
        style="width:320px"></el-input>
    </div>
    <div class="diaLog__content">
      <span class="diaLog__content--left">
        <span class="star">*</span>备注:
      </span>
      <el-input type="textarea" :rows="2" v-model="serveOption.remark" placeholder="请填写备注"
        style="width:320px"></el-input>
    </div>
    <!--弹窗选择订单 -->
    <el-dialog title="选择订单" :visible.sync="orderDialogVisible" width="80%" append-to-body :before-close="cancelOrder">
      <!-- <el-form ref="form" :model="searchType" label-width="90px"> -->
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="订单编号">
          <el-input v-model="formInline.orderId" placeholder="请输入订单编号"></el-input>
        </el-form-item>
        <el-form-item label="会员昵称">
          <el-input v-model="formInline.userName" placeholder="请输入会员昵称"></el-input>
        </el-form-item>
        <el-form-item label="会员电话">
          <el-input v-model="formInline.phone" placeholder="请输入电话号码"></el-input>
        </el-form-item>
        <el-form-item label="订单备注">
          <el-input v-model="formInline.note" placeholder="请输入订单备注"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
      </el-form>
      <div style="border: 1px solid #ccc;width: 100%;margin: 20px 0;"></div>
      <el-row>
        <el-col :span="24">
          <el-table ref="multipleTable" :header-cell-style="{ backgroundColor: '#eee' }" :data="orderData"
            tooltip-effect="dark" style="width: 100%" :row-key="getRowKey">
            <el-table-column label width="50">
              <template slot-scope="scope">
                <el-radio :label="scope.row.id" v-model="choseOrderId"
                  @click.native="handleRadioClick(scope.row)">&nbsp;</el-radio>
              </template>
            </el-table-column>
            <el-table-column label="序号" type="index">
            </el-table-column>
            <el-table-column width="55">
            </el-table-column>
            <el-table-column label="订单编号" width="150">
              <template slot-scope="scope">{{ scope.row.id }}</template>
            </el-table-column>
            <el-table-column label="会员昵称" width="120">
              <template slot-scope="scope">{{ scope.row.userName }}</template>
            </el-table-column>
            <el-table-column prop="phone" label="会员电话号码" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="createTime" label="下单时间" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="note" label="订单备注" show-overflow-tooltip>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <!-- </el-form> -->
      <!-- 分页 -->
      <div class="listBottom">
        <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange" style="margin-top: 0px">
        </PageManage>
      </div>
      <span slot="footer" class="dialog-footer">

        <el-button @click="cancelOrder">取 消</el-button>
        <el-button type="primary" @click="confirmOrder">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import { ApiCustomerList } from "@/views/customer/list/components/dialog/associateSubordinatesType";
import { searchByMiniAccount } from "@/api/order";

@Component
export default class AddModel extends Vue {
  @Prop({
    type: Boolean,
    default: {},
  })
  addDialog!: boolean;

  @Prop({
    type: Object,
    default: {},
  })
  editCurrent!: ApiCustomerList;

  orderDialogVisible: boolean = false

  formInline = {
    orderId: '',
    userName: '',
    phone: '',
    note: '',
  }
  /** 分页条数 */
  pageSize = 10;
  /** 分页页码 */
  pageNum = 1;
  /** 记录总数 */
  total = 0;

  selectionList = []

  orderData = []

  choseOrderId = null

  serveOption = {
    userId: this.editCurrent.userId,
    orderId: "",
    commissionType: "",
    amount: "",
    platformRemark: "",
    remark:""
  };

  @Watch("addDialog")
  getAddDialog() {
    //console.log('this.editCurrent', this.editCurrent)
    this.serveOption.userId = this.editCurrent.userId
    this.serveOption.commissionType = ""
    this.serveOption.orderId = ""
    this.serveOption.amount = ""
    this.serveOption.platformRemark = ""
    this.serveOption.remark = ""
  }

  /** 佣金类型选择 */
  commissionTypeList = [
    {
      "id": 1,
      "text": '佣金'
    },
    {
      "id": 3,
      "text": '平级'
    },
    {
      "id": 4,
      "text": '级差'
    },
    {
      "id": 5,
      "text": '团队'
    }, {
      "id": 6,
      "text": '循环分佣'
    },
    {
      "id": 104,
      "text": '订单消费'
    },
    {
      "id": 200,
      "text": '佣金提现'
    },
  ];

  /*mounted() {

  }*/



  /**
   * 选择订单按钮
   */
  selectOrder() {
    this.orderDialogVisible = true

    this.formInline = {
      orderId: '',
      userName: '',
      phone: '',
      note: '',
    }
    this.pageNum = 1
    this.pageSize = 10;
    this.getOrderList(this.pageNum)
  }


  /**关闭订单列表弹窗*/
  cancelOrder() {
    this.$refs.multipleTable.clearSelection();
    this.selectionList = []
    this.orderDialogVisible = false
  }

  onSubmit() {
    this.pageNum = 1
    this.getOrderList(this.pageNum, this.formInline)
    //console.log('submit!', this.formInline);
  }

  getOrderList(pageNum: any, formInline = false) {

    const param = {
      current: pageNum,
      size: this.pageSize,
      miniAccountShopUserId: this.editCurrent.userId
    };
    if (formInline) {
      param.orderId = formInline.orderId
      param.userName = formInline.userName
      param.phone = formInline.phone
      param.note = formInline.note
    }
    searchByMiniAccount(param).then(res => {
      this.orderData = res.data.list;

      this.pageSize = res.data.size;
      this.pageNum = param.current;
      this.total = res.data.total;

    }).catch((err) => {
      this.$message.warning(err);
    });
  }

  getRowKey(row) {
    return row.id
  }

  // 单选框
  handleRadioClick(row) {
    this.choseOrderId = row.id;
    console.log("handleRadioClick", this.choseOrderId)
  }

  /**
   * @method handleSizeChange
   * @description 每页 条
   */
  handleSizeChange(val: number) {
    this.pageSize = val;
    this.getOrderList(1)
  }


  /**
   * @method handleCurrentChange
   * @description 当前页
   */
  handleCurrentChange(val: number) {
    this.pageNum = val;
    this.getOrderList(val)
  }

  /**确定选择订单*/
  confirmOrder(e) {
    this.orderDialogVisible = false
    this.serveOption.orderId = this.choseOrderId;

  }

}
</script>

<style lang="scss" scoped>
.star {
  color: #f56c84;
  margin-right: 3px;
}

@include b(diaLog) {
  @include e(content) {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    @include m(left) {
      width: 100px;
      display: flex;
      justify-content: flex-end;
      margin-right: 40px;
    }
  }

  @include e(agree) {
    width: 100%;
    display: flex;
    // justify-content: center;
    margin-left: 140px;

    @include m(open) {
      color: #1e7fff;
      cursor: pointer;
    }
  }
}
</style>
