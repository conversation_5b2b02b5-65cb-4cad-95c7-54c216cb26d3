<!-- 2025-7-17 -->

<template>
    <div style="text-align: center;">
        <el-table row-key="id" :row-style="{ height: '50px', 'text-align': 'center' }"
            :cell-style="{ 'text-align': 'center', 'border-right': '1px solid #EBF0F5' }"
            :header-cell-style="{ 'text-align': 'center' }" class="multipleTable" ref="multipleTable"
            :data="membersList" :row-class-name="tableRowClassName">

            <el-table-column type="index" label="序号" width="45" fixed="left">
            </el-table-column>
            <el-table-column prop="memberLevel" label="会员等级名称" width="140"  fixed="left">
            </el-table-column>
            <el-table-column label="状态" show-overflow-tooltip width="60"  fixed="left">
                <template slot-scope="scope">
                    {{ scope.row.disable == 1 ? '停用' : '启用' }}
                </template>
            </el-table-column>
            <el-table-column label="消费额指标" width="220">
                <template slot-scope="scope">
                    <el-input-number v-model="scope.row.amountStart" style="width: 90px;" :min="0"
                        :controls="false"></el-input-number> -
                    <el-input-number v-model="scope.row.amountEnd" style="width: 90px;" :min="0"
                        :controls="false"></el-input-number>
                </template>
            </el-table-column>
            <el-table-column label="积分指标" width="220">
                <template slot-scope="scope">
                    <el-input-number v-model="scope.row.integralStart" style="width: 90px;" :min="0"
                        :controls="false"></el-input-number> -
                    <el-input-number v-model="scope.row.integralEnd" style="width: 90px;" :min="0"
                        :controls="false"></el-input-number>
                </template>
            </el-table-column>
            <el-table-column label="会员消费额指标" width="220">
                <template slot-scope="scope">
                    <el-input-number v-model="scope.row.memberAmountStart" style="width: 90px;" :min="0"
                        :controls="false"></el-input-number> -
                    <el-input-number v-model="scope.row.memberAmountEnd" style="width: 90px;" :min="0"
                        :controls="false"></el-input-number>
                </template>
            </el-table-column>
            <!-- <el-table-column label="直推条件关系" width="220" >
                <template slot-scope="scope">
                    <el-select v-model="scope.row.directConditionRelation" style="margin-left: 20px;" placeholder="请选择">
                        <el-option v-for="item in directConditionRelationList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </template>
            </el-table-column> -->
            <el-table-column label="直推会员指标" width="280">
                <template slot-scope="scope">
                    <div>
                        指标1
                        <el-input-number v-model="scope.row.directMemberQty" style="width: 90px;" :min="0"
                            :controls="false"></el-input-number> -
                        <el-select v-model="scope.row.directLowMemberLevelId" clearable  placeholder="请选择" style="width: 120px;">
                            <el-option v-for="(item, key) in memberTypeList" :key="key" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>

                    <div style="margin-top: 8px;">
                        直推条件关系
                        <el-select v-model="scope.row.directConditionRelation" clearable  style="width: 180px;" placeholder="请选择">
                            <el-option v-for="item in directConditionRelationList" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div style="margin-top: 8px;">
                        指标2
                        <el-input-number v-model="scope.row.directMemberQty2" style="width: 90px;" :min="0"
                            :controls="false"></el-input-number> -
                        <el-select v-model="scope.row.directLowMemberLevelId2" clearable  placeholder="请选择" style="width: 120px;">
                            <el-option v-for="(item, key) in memberTypeList" :key="key" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </template>
            </el-table-column>

            <el-table-column label="指定商品数指标" width="300">
                <template slot-scope="scope">
                    数量：
                    <el-input-number v-model="scope.row.productQty" style="width: 90px;" :min="0"
                        :controls="false"></el-input-number>
                    金额：
                    <el-input-number v-model="scope.row.productAmount" style="width: 90px;" :min="0"
                        :controls="false"></el-input-number>
                </template>
            </el-table-column>

            <el-table-column label="团队业绩指标" width="280">
                <template slot-scope="scope">
                    <el-input-number v-model="scope.row.teamAmountStart" style="width: 90px;" :min="0" :precision="0"
                        :controls="false"></el-input-number>元 ——
                    <el-input-number v-model="scope.row.teamAmountEnd" style="width: 90px;" :min="0" :precision="0"
                        :controls="false">元</el-input-number>元
                </template>
            </el-table-column>

            <el-table-column label="团队业绩周期" width="160">
                <template slot-scope="scope">
                    <el-input-number v-model="scope.row.teamTimes" style="width: 90px;" :min="0" :precision="0"
                        :controls="false"></el-input-number> 天
                </template>
            </el-table-column>

            <el-table-column label="前置最低会员等级" width="220">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.preLowMemberLevelId" clearable  style="margin-left: 20px;" placeholder="请选择">
                        <el-option v-for="item in memberTypeList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </template>
            </el-table-column>

            <el-table-column label="能申请商家" width="100">
                <template slot-scope="scope">
                    <el-checkbox :checked="scope.row.applyShopPartner === 1 ? true : false"
                        @change="handleAgainFlagChange2($event, scope.row)"></el-checkbox>
                </template>
            </el-table-column>

            <el-table-column label="会员复购" width="70">
                <template slot-scope="scope">
                    <el-checkbox :checked="scope.row.againFlag === 1 ? true : false"
                        @change="handleAgainFlagChange($event, scope.row)"></el-checkbox>
                </template>
            </el-table-column>
            <!-- <el-table-column label="消费额升级" show-overflow-tooltip width="80">
                <template slot-scope="scope">
                    <el-checkbox :checked="scope.row.amountFlag === 1 ? true : false"
                        @change="handleAmountFlagChange($event)"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column label="积分升级" show-overflow-tooltip width="80">
                <template slot-scope="scope">
                    <el-checkbox :checked="scope.row.integralFlag === 1 ? true : false"
                        @change="handleIntegralFlagChange($event)"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column label="积分升级" show-overflow-tooltip width="80">
                <template slot-scope="scope">
                    <el-checkbox :checked="scope.row.integralFlag === 1 ? true : false"
                        @change="handleIntegralFlagChange($event)"></el-checkbox>
                </template>
            </el-table-column> -->
        </el-table>
        <el-card class="box-card" v-show="memberTypeId">
            <el-form ref="form" :model="formData" label-width="100px" style="margin-top: 20px;">
                <div>
                    <el-row>
                        <el-col :span="2" style="display: flex;">
                            <el-button type="primary" plain size="small" @click="addTemPackage">+添加商品</el-button>
                        </el-col>
                        <el-col :span="6" style="display: flex;">
                            <el-form-item label="升级方式">
                                <!-- <el-select v-model="formData.type" placeholder="请选择" clearable @change="$forceUpdate()">
                                    <el-option v-for="(item, key) in upgradeList" :key="key" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select> -->

                                <el-select v-model="formData.type" clearable  multiple collapse-tags style="margin-left: 20px;"
                                    placeholder="请选择">
                                    <el-option v-for="item in upgradeList" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6" style="display: flex;">
                            <el-form-item label="订单会员类型">
                                <el-select v-model="formData.orderMemberTypeIdList" clearable multiple collapse-tags
                                    style="margin-left: 20px;" placeholder="请选择">
                                    <el-option v-for="item in orderMemberTypeList" :key="item.id" :label="item.name"
                                        :value="item.id">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" style="display: flex;">
                            <el-form-item label="商品">
                                <div class="valueName">
                                    <el-table :data="formData.products" ref="inventorySumTable">
                                        <el-table-column prop="option" label="操作" width="140">
                                            <template slot-scope="scope">
                                                <el-button type="text" style="color: red;"
                                                    @click="deletePackageProducts(scope.$index)">删除</el-button>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="productName" label="商品名称" width="180"
                                            show-overflow-tooltip>
                                        </el-table-column>
                                        <el-table-column prop="specs" label="商品规格" width="180" show-overflow-tooltip>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" style="display: flex;">
                            <el-form-item label="会员说明">
                                <RichEditor :text="descriptionName" ref="wEditor" style="width:700px;min-height: 50px;">
                                </RichEditor>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
        </el-card>
        <el-dialog :visible.sync="dialogVisible" highlight-current-row width="60%" :before-close="handleClose">
            <el-form ref="form" :model="searchType" label-width="90px">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="商品名称">
                            <el-input v-model="searchType.productName" placeholder="请输入商品名称"
                                style="width: 200px;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="展示分类">
                            <el-select v-model="searchType.showCategoryId"  style="width: 200px" placeholder="请选择分类"
                                :popper-append-to-body="false">
                                <el-option label="全部" :value="''" />
                                <el-option-group v-for="group in temAllShowList" :key="group.showCategoryId"
                                    :label="group.name">
                                    <el-option v-for="item in group.showCategoryVos" :key="item.showCategoryId"
                                        :label="item.name" :value="item.showCategoryId"></el-option>
                                </el-option-group>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="商品状态">
                            <el-select v-model="searchType.status" placeholder="请选择状态" style="width: 150px" clearable>
                                <el-option label="全部" :value="''" />
                                <el-option v-for="tag in statusList" :key="tag.value" :label="tag.key"
                                    :value="tag.value" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-button @click="searchGoods" type="primary" size="mini" round
                            style="margin-top: 2px;">搜索</el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-table ref="multipleTable" :data="goodList" tooltip-effect="dark" style="width: 100%"
                :row-key="getRowKeys" @selection-change="handleSelectionChange">
                <el-table-column type="selection" :reserve-selection="true">
                </el-table-column>
                <el-table-column label="序号" type="index">
                </el-table-column>
                <el-table-column width="20">
                </el-table-column>
                <el-table-column prop="productName" label="商品名称" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="specs" label="商品规格" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="categoryName" label="商品分类" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="status" label="商品状态" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-if="scope.row.status == '0'">下架</span>
                        <span v-if="scope.row.status == '1'">上架</span>
                    </template>
                </el-table-column>
            </el-table>
            <PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange" />
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" @click="primaryButton">确 定</el-button>
            </span>
        </el-dialog>
        <el-button style="margin-top: 20px;" type="primary" @click="sort" v-if="isSupper || addButton"
            :loading="loading" v-show="memberTypeId">保存</el-button>
    </div>

</template>

<script>
import { memberLevelList, dragSort, getMemberLevelList } from "@/api/sign/index";
import PageManage from "@/components/PageManage.vue";
import {
    getAddProductPackageVo,
    queryShowCategoryListAll
} from "@/api/good/goods";
import {
    getMainSpecialSetting,
} from "@/api/specialSetting/index";
import debounce from "lodash/debounce";
import Sortable from 'sortablejs';
import RichEditor from "@/components/RichEditor.vue";
import {
    getMemberType,
} from "@/api/sign/index";
export default {
    components: {
        'RichEditor': RichEditor,
        'PageManage': PageManage
    },
    props: {
        memberTypeId: String
    },
    data() {
        return {
            membersList: [], // 会员列表
            multipleSelection: [],// 多选列表
            menuName: "客户列表",
            buttonList: [],
            selectedArray: [],
            isSupper: 0,
            addButtonCode: "customerList.sort.add",
            addButton: false,
            current: 1,
            memberSales: 0,
            size: 10,
            total: 0,
            dialogVisible: false,
            temAllShowList: [],
            searchType: {
                productName: "",
                showCategoryId: "",
                status: "",
            },
            descriptionName: '',
            statusList: [
                { key: '上架', value: '1' },
                { key: '下架', value: '0' },
            ],
            goodList: [],
            orderMemberTypeList: [],//订单会员类型
            loading: false,
            //使用防抖方法
            sort: debounce(() => {
                this.saveSort()
            }, 100),
            upgradeList: [
                {
                    value: '1',
                    label: '消费额升级'
                },
                {
                    value: '2',
                    label: '积分升级'
                },
                {
                    value: '3',
                    label: '指定商品消费额升级'
                },
                {
                    value: '4',
                    label: '指定商品数升级'
                },
                {
                    value: '5',
                    label: '直推会员升级'
                },
                {
                    value: '6',
                    label: '团队业绩升级'
                }
            ],
            formData: {
                products: [],
                type: []
            },
            // 推会员指标
            indicatorSelect: '',
            // 前置会员等级
            gradeSelect: '',

            memberTypeList: [],

            directConditionRelationList: [
                {
                    value: 'and',
                    label: '且'
                },
                {
                    value: 'or',
                    label: '或'
                }
            ],


        }
    },
    mounted() {

        this.getMenberList();
        this.getMemberType();
        this.buttonAuth();
        this.getAllCategoryList();
        this.getMemberLevelList();
    },
    watch: {
        memberTypeId(newVal, oldVal) {
            console.log('newVal=', newVal)
            // console.log('oldVal=', oldVal)
            this.membersList = [];
            this.formData = {
                products: [],
                type: []
            };
            this.descriptionName = '';
            if (newVal) {
                this.getMenberList();
                this.rowDrop();
                this.buttonAuth();
                this.getAllCategoryList();
                this.getMemberLevelList();
            }
        }

    },
    methods: {
        tableRowClassName({row, rowIndex}) {
        if (rowIndex % 2 == 0) {
          return 'warning-row';
        } else  {
          return 'success-row';
        }
      },
        getMemberLevelList() {
            this.memberTypeList = [];
            getMemberLevelList(null).then(res => {
                console.log('getMemberLevelList=', res.data)
                res.data.forEach(item => {
                    this.memberTypeList.push({
                        value: item.id,
                        label: item.memberLevel
                    })
                })
            }).catch(er => {
                this.$message.error(er);
            })
        },
        getMemberType() {
            getMemberType({ status: 1 }).then(res => {
                this.orderMemberTypeList = res.data;
            }).catch(err => {
            this.$message.error(err);
        })
        },
        /**
     * 获取会员列表
     */
        getMenberList() {
            this.formData = {
                products: [],
                type: [],
                orderMemberTypeIdList:[]
            };
            this.descriptionName = '';
            memberLevelList({ memberTypeId: this.memberTypeId }).then(res => {
                console.log('memberLevelList=', res)

                if (res.data.memberTypeId != null) {
                    this.formData.memberTypeId = res.data.memberTypeId
                } else {
                    this.formData.memberTypeId = this.memberTypeId
                }
                if (res.data.id != null) {
                    this.formData.id = res.data.id
                } else {
                    this.formData.id = ''
                }
                if (res.data.description != null) {
                    this.formData.description = res.data.description
                    this.descriptionName = res.data.description
                }
                if (res.data.type != null) {
                    this.formData.type = res.data.type.split(",");
                }
                if (res.data.orderMemberTypeIds != null&&res.data.orderMemberTypeIds != undefined&&res.data.orderMemberTypeIds != "") {
                    this.formData.orderMemberTypeIdList = res.data.orderMemberTypeIds.split(",");
                }


                // getMainSpecialSetting({}).then(res => {
                //     this.memberSales = res.data.memberSales
                //     if (this.memberSales == 1) {
                //         this.upgradeList = [
                //             {
                //                 value: '1',
                //                 label: '消费额升级'
                //             },
                //             {
                //                 value: '2',
                //                 label: '积分升级'
                //             },
                //             {
                //                 value: '3',
                //                 label: '指定商品消费额升级'
                //             },
                //             {
                //                 value: '4',
                //                 label: '指定商品数升级'
                //             },
                //             {
                //                 value: '5',
                //                 label: '直推会员升级'
                //             },
                //             {
                //                 value: '6',
                //                 label: '团队业绩升级'
                //             }
                //         ]
                //     } else {
                //         this.upgradeList = [
                //             {
                //                 value: '1',
                //                 label: '消费额升级'
                //             },
                //             {
                //                 value: '2',
                //                 label: '积分升级'
                //             },

                //             {
                //                 value: '4',
                //                 label: '指定商品数升级'
                //             },
                //             {
                //                 value: '5',
                //                 label: '直推会员升级'
                //             },
                //             {
                //                 value: '6',
                //                 label: '团队业绩升级'
                //             }
                //         ]
                //         this.formData.type = []
                //     }
                // })

                this.formData.products = res.data.products

                this.membersList = res.data.rules;
                var i = this.membersList.length
                this.membersList.forEach(e => {
                    if (e.integralStart == null) {
                        e.integralStart = undefined
                    }
                    if (e.integralEnd == null) {
                        e.integralEnd = undefined
                    }
                    if (e.memberAmountStart == null) {
                        e.memberAmountStart = undefined
                    }
                    if (e.memberAmountEnd == null) {
                        e.memberAmountEnd = undefined
                    }
                    if (e.amountStart == null) {
                        e.amountStart = undefined
                    }
                    if (e.amountEnd == null) {
                        e.amountEnd = undefined
                    }
                    if (e.sort == null) {
                        e.sort = i
                        i--
                    }
                })

                let index = this.membersList.length;
                if (this.membersList[0].sort < this.membersList[--index].sort) {
                    this.membersList.reverse(); // 直接修改原数组
                }
            })
        },
        getDetailHtml() {

            return this.$refs.wEditor.getHtml();
        },
        changeValue(val) {
            console.log("sss", val);
        },
        getRowKeys(row) {
            return row.productId + '-' + row.skuId //唯一性
        },
        close() {
            this.selectedArray = []
            this.dialogVisible = false;
            this.$refs.multipleTable.clearSelection();
        },
        deletePackageProducts(index) {
            this.formData.products.splice(index, 1);
        },
        primaryButton() {
            this.dialogVisible = false;
            this.formData.products = this.formData.products.concat(this.selectedArray)
            this.selectedArray = [];
            this.$refs.multipleTable.clearSelection();
        },
        handleSelectionChange(val) {
            this.selectedArray = val
        },
        searchGoods() {
            this.current = 1;
            this.size = 10;
            this.getGoodsList();
        },
        async getAllCategoryList() {
            const param = {

            };
            const { data } = await queryShowCategoryListAll(param);
            this.temAllShowList = JSON.parse(JSON.stringify(data)) || [];
        },
        addTemPackage() {
            this.current = 1;
            this.size = 10;
            this.searchType = {
                productName: "",
                showCategoryId: "",
                status: "1",
            }
            this.getGoodsList();
        },
        handleClose(done) {
            this.selectedArray = [];
            this.$refs.multipleTable.clearSelection();
            done();
        },
        getGoodsList() {
            let params = this.searchType
            params.current = this.current
            params.size = this.size
            let dataList = this.formData.products;
            let skuIds = [];
            if (dataList != null && dataList.length > 0) {
                dataList.forEach(element => {
                    skuIds.push(element.skuId);
                });
            }
            params.skuIds = skuIds
            getAddProductPackageVo(params).then((res) => {
                this.goodList = res.data.list
                this.total = res.data.total
                this.dialogVisible = true
            }).catch((err) => {
                this.$message.error(err)
            })
        },
        handleSizeChange(val) {
            this.size = val;
            this.getGoodsList()
        },
        handleCurrentChange(val) {
            this.current = val;
            this.getGoodsList()
        },
        /**
         * 排序方法
         */
        rowDrop() {
            const tbody = document.querySelector('.multipleTable tbody')
            const _this = this
            Sortable.create(tbody, {
                animation: 150,
                onEnd(evt) {
                    const currRow = _this.membersList.splice(evt.oldIndex, 1)[0]
                    _this.membersList.splice(evt.newIndex, 0, currRow)
                    let index = _this.membersList.length
                    _this.membersList.forEach((item) => {
                        item.sort = index
                        index--
                    })

                }
            })
            console.log("_this.membersList", _this.membersList)
        },

        /**
         * 保存排序
         */
        saveSort() {
            this.loading = true;

            if (!this.loading) {
                return false;
            }
            console.log("this.membersList", this.membersList);
            //根据sort进行升序
            this.membersList.sort((a, b) => b.sort - a.sort)
            this.formData.description = this.getDetailHtml()
            console.log("formData=", this.formData);

            if (!this.formData.type.length) {
                this.$message.error('请选择升级方式');
                this.loading = false
                return false;
            }

            this.formData.rules = this.membersList


            let newData = JSON.parse(JSON.stringify(this.formData));

            newData.products.forEach(item => {
                item.memberTypeId = this.memberTypeId
            })

            newData.type = newData.type.join(',');
            newData.orderMemberTypeIds = "";
            if(newData.orderMemberTypeIdList!=null&&
                newData.orderMemberTypeIdList!=undefined
            ){
                newData.orderMemberTypeIds = newData.orderMemberTypeIdList.join(',');
            }


            console.log("newData=", newData);
            //保存后提交数据
            dragSort(newData).then(res => {
                this.$message.success('保存成功');
                //保存之后500毫秒刷新页面
                setTimeout(() => {
                    this.getMenberList();
                    this.loading = false
                }, 500)
            }).catch(err => {
                this.$message.error(err);
                this.loading = false
            })


        },
        handleAgainFlagChange2(event, row) {
            if (event) {
                row.applyShopPartner = 1
            } else {
                row.applyShopPartner = 0
            }
        },
        handleAgainFlagChange(event, row) {
            if (event) {
                row.againFlag = 1
            } else {
                row.againFlag = 0
            }
        },
        handleAmountFlagChange(event) {
            this.membersList.forEach(item => item.amountFlag = event ? 1 : 0);
        },
        handleIntegralFlagChange(event) {
            this.membersList.forEach(item => item.integralFlag = event ? 1 : 0);
        },

        /**
         * 
         * 合并列
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 6) {
                if (rowIndex % this.total === 0) {
                    return {
                        rowspan: this.total,
                        colspan: 1
                    };
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0
                    };
                }
            }
            if (columnIndex === 7) {
                if (rowIndex % this.total === 0) {
                    return {
                        rowspan: this.total,
                        colspan: 1
                    };
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0
                    };
                }
            }
            if (columnIndex === 8) {
                if (rowIndex % this.total === 0) {
                    return {
                        rowspan: this.total,
                        colspan: 1
                    };
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0
                    };
                }
            }
        },
        buttonAuth() {
            this.isSupper = this.$STORE.userStore.userInfo.isSupper
            let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)
            let buttonList = [];
            authMenuButtonVos.forEach(element => {
                buttonList.push(element.buttonCode);
            });
            this.buttonList = buttonList
            var addButtonData = buttonList.find(e => e == this.addButtonCode);
            if (addButtonData != null && addButtonData != undefined) {
                this.addButton = true;
            }
        }
    },


}
</script>

<style lang="scss" scoped>
.valueName {
    margin-top: 10px;
    width: 620px;
    padding: 20px 20px;
    border: 1px solid #d7d7d7;
}

.DistributionCommission {
    margin-left: 5px;

    .commission__top {
        display: flex;
        border: 1px solid #a0b3d6;
        margin-bottom: 2px;

    }

    span {
        display: inline-block;
        text-align: center;
        background: #bbbbbb;
        line-height: 20px;
        color: rgba(33, 31, 31, 1);

    }

    .span1 {
        width: 200px;

    }

    .span2 {
        width: 50px;
    }

    .distribution__commission {

        width: 295px;
        height: 20px;
        line-height: 20px;
        padding: 3px;
        font-size: 12px;
        outline: none;
        text-align: center;
        border: none;
        // overflow-x: hidden;
        // overflow-y: auto;
    }
}

@include b(regionTop) {
    width: 100%;
    height: 50px;
    background-color: #f2f2f2;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 10px 30px;
    color: #666666;
    font-size: 12px;

    @include e(lineWidth) {
        width: 100%;
        display: flex;
    }
}

@include b(regionAdd) {
    margin: 20px 0px;
    display: flex;
    justify-content: space-between;

    @include e(tip) {
        color: #666666;
        font-size: 14px;
    }
}

.dialogTitle {
    display: flex;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
}

.dialogName {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    background-color: #e9f3fb;
    font-size: 14px;
    font-weight: bold;
    margin-top: -20px;
}

@include b(dialogList) {
    margin-top: 20px;

    @include e(item) {
        display: flex;
        margin-bottom: 20px;
        justify-content: space-between;
        width: 300px;
    }
}

.miniUnitSpan {
    margin-left: 10px;
}

/deep/ .el-table .warning-row {
    background: white ;
  }

  /deep/ .el-table .success-row {
    background: #d9ecff ;
  }
  /deep/ .el-icon-arrow-up:before {
    content: none;
  }
</style>