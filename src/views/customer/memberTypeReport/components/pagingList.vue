<!--
 * 销售汇总表分页列表
 -->
<template>
  <div>
    <el-table
      :data="data"
      style="width: 100%"
      :summary-method="getSummaries" show-summary>
      <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
      <el-table-column prop="memberTypeName" label="会员类型名称" min-width="120" align="center">
        <template slot-scope="scope">
          <span
            class="member-type-link"
            @click="goToMemberLevelReport(scope.row.memberTypeId)"
          >
            {{ scope.row.memberTypeName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="memberCount" label="会员数量" min-width="120" align="center"></el-table-column>
      <el-table-column prop="totalAmount" label="消费总金额" min-width="120" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";

@Component({
  components: { PageManage }
})
export default class PagingList extends Vue {
  @Prop({ type: Array, default: () => [] }) data!: Array<{
    memberTypeId: string;
    memberTypeName: string;
    memberCount: number;
    totalAmount: number;
  }>;

  @Prop({ type: Object, default: () => ({}) }) query!: any;

  @Prop({ type: Boolean, default: false }) loading!: boolean;


  getSummaries(param: any) {
    const { columns, data } = param;
    const sums: any[] = [];
    columns.forEach((column: any, index: number) => {
      if (index === 0) {
        sums[index] = '合计';
        return;
      }
      if (column.property === 'memberCount' || column.property === 'totalAmount' ) {
        const values = data.map((item: any) => Number(item[column.property]));
        const total = values.reduce((prev: any, curr: any) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          }
          return prev;
        }, 0);
        const totalStr = total.toFixed(2);
        sums[index] = totalStr;
      }
    });

    return sums;
  }

  goToMemberLevelReport(memberTypeId: string) {
    const { size,total,current,memberTypeIds,dateRange, ...restQuery } = this.query;
    const param = {
      ...restQuery,
      memberTypeIds: [memberTypeId]
    };
    this.$router.push({
      path: '/customer/memberTypeReport/memberLevelReport',
      query: param

    });
  }
 
}
</script>

<style lang="scss" scoped>
.pagingList-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.member-type-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;
}
</style> 