<template>
    <div>

        <!-- 提现列表 -->
        <Search @searchBy="getSearch"></Search>
        <!-- 设置分类 -->
        <!-- <SetClassify :goodIds="chooseId" @batchAll="batchAll" @printExcel="printExcel" @commandsx="commandValsx"
            style="margin-left: 10px;margin-top: 10px;"></SetClassify> -->
        <!-- <SetClassify @command="commandVal"  @showPointsPrice="PointsPrice" ref="setClass"
                    :goodIds="goodIds" :showGetList="showGetList" @changeIds="getGoodList" style="margin-left: 20px"
                    :is-item="false" :is-value="false">设置分类</SetClassify> -->

        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane v-for="(item, index) in list" :key="index" :label="item.modeName" :name="item.modeName"
                :id="item.id"></el-tab-pane>
        </el-tabs>
        <!-- 商品列表 -->
        <withAllList :changeId="chooseId" :searchTypeStor="searchType" @getShowProList="getShowProList" ref="withAllList">
        </withAllList>
        <!-- 设置分类 -->
        <div class="listBottom">
            <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange" style="margin-top: 0px"></PageManage>
        </div>



    </div>
</template>
  
<script lang="ts">
import { Vue, Component, Ref } from "vue-property-decorator";
import Search from "./components/Search.vue";
import withAllList from "./components/withAllList.vue";
import SetClassify from "./components/SetClassify.vue";
import PageManage from "@/components/PageManage.vue";

import { listType } from "./components/withAllListType";

@Component({
    components: {
        Search,
        withAllList,
        SetClassify,
        PageManage
    }
})
export default class withdrawalList extends Vue {
    /** 获取商品数组信息 */
    @Ref()
    readonly withAllList!: withAllList;

    list: Array<listType> = [
        { modeName: '待审核', id: 100 },
        { modeName: '审核通过', id: 101 },
        { modeName: '已驳回', id: 200 },
        { modeName: '所有审核', id: '' }]
    chooseId: string | number = this.list[0].id
    activeName = this.list[0].modeName;

    searchType: any = {}
    pageSize = 0;

    pageNum = 0;
    total = 0;
    /**
    * 顶部专区选择
    */
    handleClick(tab: { index: number }) {
        
        this.chooseId = this.list[tab.index].id;
        console.log(this.chooseId);

    }
    mounted() {
        // 加载搜索缓存
        // var cache = JSON.parse(
        //     localStorage.getItem("cache_withdrawalList_search_form") || "{}"
        // );
        // this.searchType = Object.assign(cache);
    }
    /**顶部搜索信息 */
    getSearch(data: any) {
        this.searchType = data;
        this.searchType.current = 1;
        console.log('顶部搜索信息,', this.searchType);

        // 缓存搜索条件
        localStorage.setItem(
            "cache_withdrawalList_search_form",
            JSON.stringify(this.searchType)
        );
        this.getGoodList();
    }
    /**
 * 合并获取商品列表搜索条件
 */
    getGoodList() {
      
        this.withAllList.searchType = Object.assign(
            this.withAllList.searchType,
            this.searchType
        );
        console.log('合并获取商品列表搜索条件11', this.withAllList.searchType);

        this.withAllList.getProduct();
    }
    /** 根据排序选择操作 */
    commandValsx(val: string, sortcommand: string) {
        let types: any = {}
        types[sortcommand] = val;
        this.searchType = types;
        // this.withAllList.searchType.
        for (let key in this.withAllList.searchType) {
            if (key == 'createTimeSort' || key == 'amountSort' || key == 'payTimeSort') {
                this.$delete(this.withAllList.searchType, key)
            }
        }
        console.log('根据排序选择操作', val, this.searchType, types)
        console.log('0000', this.withAllList.searchType);
        this.getGoodList();

    }
    batchAll() {
        console.log('批量审核');
        this.withAllList.batchExamine();
    }
    printExcel() {
        this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            this.withAllList.exprotData();
        }).catch(() => {
           
        });

    }
    /** 展示分类获取已选择的分类  */
    getShowProList(data: any) {
        this.showGetList = data || [];
        // this.goodsList = this.$refs.goodsList as GoodsList;
        this.total = this.withAllList.total;
        this.pageSize = this.withAllList.searchType.size as number;
        this.pageNum = this.withAllList.searchType.current as number;
    }
    /**
     * @method handleSizeChange
     * @description 每页 条
     */
    handleSizeChange(val: number) {
        this.withAllList.searchType.size = val;
        this.withAllList.getProduct();
    }

    /**
     * @method handleCurrentChange
     * @description 当前页
     */
    handleCurrentChange(val: number) {
        this.withAllList.searchType.current = val;
        console.log('当前页', val);

        this.withAllList.getProduct();
    }
}
</script>
<style lang="scss" scoped>
.listBottom {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: fixed;
    bottom: 10px;
    width: 990px !important;
    background-color: white;
    padding: 10px 0px;
    z-index: 10;
}
</style>