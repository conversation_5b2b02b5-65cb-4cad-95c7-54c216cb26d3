<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:47:50
-->
<template>
    <!-- 设置分类 -->
    <div class="all">
        <div class="batch__export">
            <div @click="batchAll" class="batch" v-if="isSupper||toExamineButton">批量审核</div>
            <span> | </span>
            <div @click="doPrint" class="export" v-if="isSupper||exportButton">导出</div>
        </div>
        <div class="el-dropdown-link">
            <el-dropdown @command="handleCommandsx">
                <span>
                    {{ sort }}
                </span>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-for="item in dropdownNum" :key="item.command"
                        :command="{ command: item.command, item: item }">{{ item.text }}</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
            <div class="dropdownsx">
                <i @click="commandSort(1)" :class="sortIndex == 1 ? 'el-icon1' : ''"
                    class="el-icon-arrow-up el-icon--right"></i>
                <i @click="commandSort(2)" :class="sortIndex == 2 ? 'el-icon2' : ''"
                    class="el-icon-arrow-down el-icon--right"></i>
            </div>
        </div>


    </div>
</template>
  
<script lang="ts">
import { Vue, Component, Prop, Ref } from "vue-property-decorator";
import { ApiSpecArea } from "./withAllListType";

@Component({})
export default class SetClassify extends Vue {
    name = "SetClassify";

    // @Prop()
    // isItem!: boolean;

    @Prop()
    goodIds!: number[];

    // @Prop()
    // showGetList!: GoodDetailInfo[];

    idList: string[] = [];

    regionList: Array<Partial<ApiSpecArea>> = [];

    popVisible = false;
    sort = '排序'
    sortcommand = ''
    sortIndex:number|string= ''
    changeId = "108"
    radio = ''

    dropdownList: Array<Partial<ApiSpecArea>> = [
        {
            text: "批量上架",
            command: "6",
            disabled: false,
        },
        {
            text: "批量下架",
            command: "7",
            disabled: false,
        },
    ];
    dropdownNum = [
        {
            text: "申请时间",
            command: "createTimeSort",
            disabled: false,
        },
        {
            text: "提现金额",
            command: "amountSort",
            disabled: false,
        }, {
            text: "放款时间",
            command: "payTimeSort",
            disabled: false,
        }
    ];

    menuName = "提现列表";

    buttonList = [];

    isSupper = 0;

    toExamineButtonCode = "commissionList.toExamine";

    toExamineButton = false;

    exportButtonCode = "commissionList.export";

    exportButton = false;


    /**
     * 获取选择商品id
     */
    // showGetId() {
    //     const list = (this.$parent.$refs.goodsList as GoodList).tableCheckedItem;
    //     this.idList = [];
    //     list.forEach(item => {
    //         this.idList.push(String(item.id));
    //     });
    // }

    /**
     * 发布积分方案
     */
    handleClick() {
        this.$router.push({
            name: "AddPointsGoods",
            query: { saleMode: this.changeId }
        });
        // @click="publishGoods"
        // this.$emit("click", e);
    }
    mounted() {
        // window.document = this.doPrint;
        this.buttonAuth();

    }


    buttonAuth() {


        this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList


        var toExamineButtonData = buttonList.find(e => e == this.toExamineButtonCode);

        if (toExamineButtonData != null && toExamineButtonData != undefined) {
            this.toExamineButton = true;
        }

        var exportButtonData = buttonList.find(e => e == this.exportButtonCode);

        if (exportButtonData != null && exportButtonData != undefined) {
            this.exportButton = true;
        }
    }

    /**
     * 点击的下拉项
     */
    handleCommand(val: any) {
        let type = false;
        if (val.item.modeName) {
            type = true;
        }
        this.$emit("command", val.command, type);
    }
    handleCommandsx(val: any) {
        this.sort = val.item.text
        this.sortcommand = val.item.command
        this.sortIndex = ''


        // console.log('222222', val);

    }
    commandSort(val: any) {
        if (this.sortcommand) {
            this.sortIndex = val
            // console.log('排序', val, this.sortcommand);

            this.$emit("commandsx", val, this.sortcommand);
        } else {
            this.$message.error("请先选择排序");

        }

    }
    showpoints(e) {
        this.$emit("showPointsPrice", e);
    }

    showChange(e: boolean) {
        if (e) {
            this.regionList = [];
            this.dropdownList = [
                {
                    text: "批量上架",
                    command: "6",
                    disabled: false,
                },
                {
                    text: "批量下架",
                    command: "7",
                    disabled: false,
                }
            ];
        }
    }

    /**
     * 商品单独操作
     */
    handleItemCommand(val: number) {
        this.$emit("handleItemCommand", val);
    }

    //      doPrint(){
    //       let printbox = document.querySelector("#printList").innerHTML;
    //       document.querySelector("body").innerHTML = printbox;
    //       window.print();//调用打印
    //       this.cancel();
    //       // this.toQuery();
    //       window.location.reload();
    //     }
    /**
     * 批量审核
     */
    batchAll() {
        this.$emit('batchAll', true)
    }
    doPrint() {
        this.$emit('printExcel', true)
        // let printbox = document.querySelector("#printList").innerHTML;
        // document.querySelector("body").innerHTML = printbox;
        // window.print()//调用打印

    }
}
</script>
  
<style lang="scss">
@import "@/assets/styles/mixins/mixins.scss";

@include b(setClassify) {
    display: flex;
    line-height: 30px;
    // border: 1px solid #dcdfe6;
    overflow: hidden;
    margin-left: 20px;
    border-radius: 50px;
    background-color: #409EFF;
    color: #ffffff;
    border: 1px solid #409EFF;
    position: relative;

    @include e(title) {
        text-align: center;
        padding: 0 20px;
        cursor: pointer;
    }

    @include e(icon) {
        width: 40px;
        text-align: center;
        cursor: pointer;
        vertical-align: middle;
        color: #ffffff;
        font-weight: bold;
    }
}

.all {
    display: flex;
    align-items: center;

    .el-dropdown-link {
        height: 32px;
        width: 100px;
        line-height: 20px;
        border-radius: 20px;
        background-color: rgba(255, 255, 255, 1);
        color: rgba(16, 16, 16, 1);
        font-size: 14px;
        text-align: left;
        border: 1px solid rgba(187, 187, 187, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
        margin-left: 17px;

        .el-dropdown {
            width: 80px;

            span {
                cursor: pointer;

            }
        }

        .dropdownsx {
            display: flex;
            flex-direction: column;

            .el-icon1 {
                color: #409EFF;
            }

            .el-icon2 {
                color: #409EFF;
            }
        }
    }

}

.batch__export {
    width: 135px;
    height: 34px;
    border-radius: 25px;
    background-color: rgba(9, 152, 236, 1);
    color: #ffffff;
    font-size: 14px;
    border: 1px solid rgba(255, 255, 255, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 20px;

    span {
        font-weight: 700;
        margin: 0 10px;
    }

    div {
        cursor: pointer;
    }
}

// .setClassify__icon::after {
//     color: #ffffff;
//     content: "|";
//     position: absolute;
//     left: -5px;
//     bottom: 1px;
// }

// .commandClass {
//     height: 90px;
//     overflow: overlay;
// }

// .commandClass::-webkit-scrollbar {
//     width: 4px;
//     height: 4px;
// }

// .commandClass::-webkit-scrollbar-thumb {
//     border-radius: 10px;
//     -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//     background: rgba(0, 0, 0, 0);
// }

// .commandClass::-webkit-scrollbar-track {
//     -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
//     border-radius: 0;
//     background: rgba(0, 0, 0, 0);
// }
</style>
  