<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:52:07
-->
<template>
    <m-card class="form" :needToggle="true">
        <el-form ref="form" :model="searchType" label-width="90px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="用户名称">
                        <el-input v-model="searchType.nikeName" placeholder="请输入用户名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="用户手机">
                        <el-input v-model="searchType.phone" placeholder="请输入用户手机"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="审核人">
                        <el-input v-model="searchType.auditUserName" placeholder="请输入审核人"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <!-- <el-row>
                <el-col :span="13">
                    <el-form-item label="提交时间">
                        <el-date-picker v-model="createDayRange" style="width: 376px" type="datetimerange" :align="'right'"
                            unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="pickerOptions" @change="chooseTime"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-radio-group v-model="searchType.type" @input="radioButton" size="mini">
                        <el-radio-button label="1" style="border-radius: 4px;">近7天</el-radio-button>
                        <el-radio-button label="2" style="margin-left: 20px;border-left: 1px solid #DCDFE6;border-radius: 4px;">近30天</el-radio-button>
                    </el-radio-group>
                </el-col>
            </el-row> -->
           
            <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
        </el-form-item>
        </el-form>
    </m-card>
</template>
  
<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
//   import { getAllCategory } from "@/api/good/goods";
//   import { SearchState, SearchKeyType } from "./searchType";
import DateUtil from "@/store/modules/date";
import { DatePickerOptions } from "element-ui/types/date-picker";
//   import { watch } from "vue";

@Component({})
export default class Search extends Vue {
    name = "Search";

  
    searchType = {
        nikeName: '',
        phone: '',
        auditUserName: '',
    }
    createDayRange = ["", ""];
    mounted(){
        // this.search()
    }
    // 搜索按钮
    search() {
      this.$emit("searchBy", this.searchType);
    }
}
</script>
  
<style lang="scss" scoped>

</style>
  