import FileSaver from 'file-saver'
import * as XLSX from 'xlsx';

//导出
export default function exportExcel(name) {
	//转换成excel时，使用原始的格式
	var xlsxParam = { raw: true };
	let fix = document.querySelector(".el-table__fixed");
	let wb;
	//判断有无fixed定位，如果有的话去掉，后面再加上，不然数据会重复
	if(fix) {
		wb = XLSX.utils.table_to_book(
		document.querySelector("#tableId").removeChild(fix),xlsxParam);
		document.querySelector("#tableId").appendChild(fix);
	} else {
		wb = XLSX.utils.table_to_book(document.querySelector("#tableId"),xlsxParam);
	}
	var wbout = XLSX.write(wb, {
		bookType: "xlsx",
		bookSST: true,
		type: "array",
	});
	try {
		FileSaver.saveAs(new Blob([wbout], { 
			type: "application/octet-stream" }), 
			`${name}_${new Date().getTime()}.xlsx`
		); 
	} catch (e) {
		if (typeof console !== "undefined") console.log(e, wbout);
	}
	return wbout;
}
