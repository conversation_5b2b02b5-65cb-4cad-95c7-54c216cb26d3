<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-14 10:15:03
-->
<template>
    <!-- 商品列表 -->
    <div style="margin-top: 0px;padding-bottom: 30px;">

        <el-table :data="goodList"  slot="content" @selection-change="handleSelectionChange" border>
            <el-table-column type="selection" width="60" fixed="left">
            </el-table-column>
            <el-table-column prop="oddNumbers" label="提现单号" :showsSlection="true" >
                <template v-slot="{ row }">
                    <div class="goodList">
                        {{ row.id }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="person" label="提现人" >
                <template v-slot="{ row }">
                    <div>
                        <div>{{ row.nikeName }}</div>
                        <div class="personPhone">{{ row.phone }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="price" label="提现金额" >
                <template v-slot="{ row }">
                    <span>{{ row.amount }}</span>元
                </template>
            </el-table-column>
            <el-table-column prop="createTime" label="申请时间" >
                <template v-slot="{ row }">
                    <span>{{ row.createTime }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="payTime" label="放款时间" >
                <template v-slot="{ row }">
                    <div v-if="row.status == 300">{{ row.payTime }}</div>

                </template>
            </el-table-column>

            <el-table-column prop="userName" label="单据状态" >
                <template v-slot="{ row }">
                    <span v-if="row.status == 0" style="color:orange">审核中</span>
                    <span v-else-if="row.status == 1" style="color:#09F016;">审核通过</span>
                    <span v-else-if="row.status == -1" style="color:red">已驳回</span>
                    <span v-else-if="row.status == -2" style="color:red">提现失败</span>
                    <span v-else-if="row.status == 2" class="paid">提现成功</span>
                </template>
            </el-table-column>
            <el-table-column prop="userName" label="付款方式" >
                <template v-slot="{ row }">
                    <span v-if="row.payType == 102">微信支付</span>
                    <span v-else-if="row.offlinePayType == 300">银行卡</span>
                    <span v-else-if="row.offlinePayType == 301">微信收款码</span>
                    <span v-else-if="row.offlinePayType == 302">支付宝收款码</span>

                </template>
            </el-table-column>
            <el-table-column prop="userName" label="转账信息" >
                <template v-slot="{ row }" >
                    <div v-if="row.offlinePayType == 300">
                        <div>开户人:{{row.bankUserName}}</div>
                        <div>转款银行:{{row.bankName}}</div>
                        <div>银行卡号:{{row.bankNo}}</div>
                        <div>公司名称:{{row.bankUserName}}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="operate" label="操作" >
                <template v-slot="{ row }">
                    <div class="operateCenter">
                        <el-button size="mini" @click="passThroughpd(row.id)"
                            v-if="row.status == 0 && (isSupper || toExamineButton)" type="success" round>通过</el-button>
                        <!-- <el-button size="mini" @click="successPayment(row.id)" v-if="row.status == 101&&row.payStatus =='FAILED'&&(isSupper||payButton)" type="success"
                            round>打款</el-button> -->
                        <el-button size="mini" @click="dangerReject(row.id)"
                            v-if="row.status == 0 && (isSupper || toExamineButton)" type="danger" round>驳回</el-button>
                        <!-- <el-button size="mini" @click="rejectBottom(row)" v-if="row.status == 1" type="info"
                            round>详情</el-button> -->

                        <el-button size="mini" @click="setPaymentCode(row)"
                            v-if="row.offlinePayType == 301 || row.offlinePayType == 302" type="primary"
                            round>收款码</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 导出数据专用表格 
        id="exportData"-->
        <el-table id="tableId" :data="tableCheckedItem" tooltip-effect="dark" v-show="false">
            <!-- //把导出数据的表格给隐藏 -->
            <el-table-column prop="id" label="提现单号" show-overflow-tooltip>

            </el-table-column>
            <el-table-column prop="nikeName" label="提现人" show-overflow-tooltip></el-table-column>
            <el-table-column prop="amount" label="提现金额" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="申请时间" show-overflow-tooltip></el-table-column>
            <el-table-column prop="payTime" label="放款时间" show-overflow-tooltip></el-table-column>
            <el-table-column prop="pic" label="单据状态" show-overflow-tooltip>
                <template v-slot="{ row }">
                    <span v-if="row.status == 100">未审核</span>
                    <span v-else-if="row.status == 101">已审核</span>
                    <span v-else-if="row.status == 200" class="rejectClass">驳回</span>
                    <span v-else-if="row.status == 300" class="paid">已打款</span>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="name" label="联系电话" show-overflow-tooltip> </el-table-column> -->
        </el-table>
        <!-- 批量审核  -->
        <el-dialog title="提示" :visible.sync="batchShow" width="30%" :before-close="batchClose">
            <div>
                <el-button size="mini" @click="batchDialog('1')" type="success" round>通过</el-button>
                <!-- <el-button size="mini" @click="batchDialog('')" type="success" round>打款</el-button>-->
                <el-button size="mini" @click="batchDialog('-1')" type="danger" round>驳回</el-button>


            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="batchShow = false">取 消</el-button>
                <!-- <el-button type="primary" @click="batchDialog">确 定</el-button> -->
            </span>
        </el-dialog>
        <!-- 审核  是否通过 -->
        <el-dialog :visible.sync="passThrough" width="30%" :before-close="throughClose">
            <div class="dialogPass">
                <div>
                    审核通过，款项立即转账，是否操作？
                </div>
                <el-button type="success" @click="primaryThrough">确 定</el-button>
            </div>

        </el-dialog>

        <el-dialog :visible.sync="dangerShow" width="30%" :before-close="handleClose">
            <div class="dialogWhe">
                驳回原因
            </div>
            <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请输入内容" v-model="rejection">
            </el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dangerShow = false">取 消</el-button>
                <el-button type="primary" @click="primaryReject">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 驳回原因详情 -->
        <el-dialog :visible.sync="showDes" title="提现单详情" center width="40%" :before-close="throughClose">
            <div>
                <el-form :label-position="'left'" label-width="100px" :model="showDesData">
                    <el-form-item label="提现单号">
                        <div class="showDesPass">{{ showDesData.id }}</div>
                    </el-form-item>
                    <el-form-item label="提现人">
                        <!-- <el-input v-model="showDesData.nikeName"></el-input> -->
                        <div class="showDesPass">{{ showDesData.nikeName }}</div>
                    </el-form-item>
                    <el-form-item label="提现金额">
                        <div class="showDesPass">{{ showDesData.amount }}</div>
                    </el-form-item>
                    <el-form-item label="申请时间">
                        <div class="showDesPass">{{ showDesData.createTime }}</div>
                    </el-form-item>
                    <el-form-item class="el-form-item1" label="单据状态">
                        <div class="showDesPass" style="color:red">驳回</div>
                    </el-form-item>
                    <el-form-item class="el-form-item1" label="驳回原因" style="color:red">
                        <div class="showDesPass" style="color:red">{{ showDesData.comments }}</div>

                    </el-form-item>
                </el-form>
            </div>

        </el-dialog>

        <el-dialog :visible.sync="showPaymentCode" width="30%">
            <div class="dialogWhe" v-if="paymentCode.offlinePayType == 301">
                微信收款码
            </div>
            <div class="dialogWhe" v-else>
                支付宝收款码
            </div>
            <div style="display: flex;align-items: center; justify-content: center;">
                <img :src="paymentCode.wxAccountUrl" alt="" style="width: 300px;height: 300px;"
                    v-if="paymentCode.offlinePayType == 301" />
                <img :src="paymentCode.alipayAccountUrl" alt="" style="width: 300px;height: 300px;" v-else>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showPaymentCode = false">取 消</el-button>
                <el-button type="primary" @click="showPaymentCode = false">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import exportExcel from "../exportExcel.js";
import { getMiniCommissionList, approveWithdrawal, batchApprove, exportMiniAccountCommissionCash } from "@/api/withdrawalApi/withdrawalApi"
// import { Loading } from "element-ui";
import { GoodDetailInfo, SearchKeyType, approvalBatchType } from "./withAllListType";
@Component({})
export default class GoodsList extends Vue {
    @Prop({}) searchTypeStor!: any

    @Prop({})
    changeId!: string;

    @Watch("changeId")
    getSaleMode() {
        this.status = this.changeId;
        this.searchType.status = this.changeId;

        console.log("this.status", this.changeId);
        console.log('bbbbbbbb', this.searchType, this.changeId);

        this.searchType.current = 1;
        this.getProduct();
    }

    status = "";
    goodList: Array<GoodDetailInfo> = [];
    dangerShow: boolean = false
    passThrough: boolean = false
    batchShow: boolean = false
    rejection = ''
    passVisibleId = ''
    tableCheckedItem: Array<GoodDetailInfo> = [];
    approvalType = {
        approvalStatus: '0',
        id: ''
    }
    approvalBatch: Array<approvalBatchType> = []
    searchType = {
        current: 1,
        size: 10,
    } as SearchKeyType;
    loading = false;
    total = 0;
    hasList = false;
    showDes = false
    showDesData = {} as GoodDetailInfo;
    menuName = "提现列表";

    buttonList = [];

    isSupper = 0;

    toExamineButtonCode = "commissionList.toExamine";

    toExamineButton = false;

    payButtonCode = "commissionList.pay";

    payButton = false;

    showPaymentCode = false;
    paymentCode = {};



    // selectList=[];
    //表格选中事件回调
    exportTable() {
        // console.log('表格',);
        if (this.tableCheckedItem.length == 0) {
            this.tableCheckedItem = this.goodList
        } else {
            exportExcel('表格1')

        }

    }
    /**驳回的详情 */
    rejectBottom(item) {
        this.showDes = true
        this.showDesData = item
        console.log('批量审核', item);

    }
    /**
     * 批量审核选择
     */
    batchExamine() {
        if (this.tableCheckedItem.length == 0) {
            // 请选择要审核的提现申请
            this.$alert('请选择要审核的提现申请内容', '批量提现', {
                confirmButtonText: '确定'
            });
        } else {
            this.tableCheckedItem.forEach((item, index) => {
                this.batchShow = true


            })

        }

    }
    handleSelectionChange(val: any) {
        this.tableCheckedItem = val;
        console.log('eeeee', val);
      }
    // 批量审核
    batchDialog(val: string) {
        if (!val) {
            this.$message('打款按钮还没作用')
        } else {
            this.tableCheckedItem.forEach((res) => {
                let item = {} as any
                item.approvalStatus = val
                item.id = res.id
                this.approvalBatch.push(item)
            })
            console.log('eeeee', this.approvalBatch);

            //  this.approvalBatch.approvalStatus=val
            if (val == '-1') {
                this.dangerShow = true

            }
            if (val == '1') {
                this.batchApproveHan()
                this.approvalBatch = []
            }
        }
    }
    batchApproveHan() {
        batchApprove(this.approvalBatch).then((res) => {
            console.log('res', res);
            this.batchShow = false
            this.getProduct()
        }).catch((err) => {
            this.$message(err)
            this.batchShow = false
        })
    }
    batchClose() {
        this.batchShow = false

    }
    mounted() {
        this.status = this.changeId;
        console.log('加载搜索缓存11', this.searchType, this.status, 'yy', this.changeId);
        //  加载搜索缓存
        var cache = JSON.parse(
            localStorage.getItem("cache_withdrawalList_search_form") || "{}"
        );


        this.searchType = Object.assign(this.searchType, cache) as SearchKeyType;
        console.log('加载搜索缓存', this.searchType, cache);

        this.getProduct();

        this.buttonAuth();
    }

    buttonAuth() {


        this.isSupper = this.$STORE.userStore.userInfo.isSupper
        let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

        let buttonList = [];

        authMenuButtonVos.forEach(element => {
            buttonList.push(element.buttonCode);
        });

        this.buttonList = buttonList


        var toExamineButtonData = buttonList.find(e => e == this.toExamineButtonCode);

        if (toExamineButtonData != null && toExamineButtonData != undefined) {
            this.toExamineButton = true;
        }

        var payButtonData = buttonList.find(e => e == this.payButtonCode);

        if (payButtonData != null && payButtonData != undefined) {
            this.payButton = true;
        }

    }
    passVisibleShow(id: any) {
        this.passVisibleId = id
    }
    /**
     * 获取商品列表查询参数
     */
    getProListParams() {


        this.searchType.status = this.changeId;

        // 删除请求链接里面的空值
        for (const key in this.searchType) {
            if (this.searchType[key] == "undefined" || (this.searchType[key] == "" && this.searchType[key] != "0")) {
                console.log('合并获取商品列表123', key);
                this.$delete(this.searchType, key)
                console.log('合并获取商品列表1233456', this.searchType);
            }
        }
        // for (const key in this.withAllList.searchType) {
        //     if (this.withAllList.searchType[key]=="undefined"||!this.withAllList.searchType[key]) {
        //         this.$delete(this.withAllList.searchType, key)
        //         console.log('合并获取商品列表123', key);
        //     }
        // console.log('合并获取商品列表搜索条', this.withAllList.searchType[key]);

        // }
        console.log(',,,,', this.searchType, this.changeId);
        return this.searchType;
    }
    /**
     * 获取商品列表
     */
    async getProduct() {
        const param = this.getProListParams();
        console.log('获取商品列表查询参数', param);

        this.loading = true;
        try {
            // getMiniCommissionList
            // const res = await getProList(param);
            const res = await getMiniCommissionList(param);
            const goodList = res.data.list;
            this.total = res.data.total;
            this.hasList = res.data.list.length === 0 ? true : false;
            console.log('获取商品列表', goodList);

            this.goodList = goodList;
        } catch (error) {
            console.log(error);
        }
        this.loading = false;
        this.$emit("getShowProList", this.goodList);
    }
    exprotData() {
        const param = this.getProListParams();
        console.log('获取商品列表查询参数', param);
        exportMiniAccountCommissionCash(param).then((res) => {
            var blob = new Blob([res.data], {
                type: "application/x-msdownload;charset=UTF-8",
            });
            // 创建一个blob的对象链接
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            // 把获得的blob的对象链接赋值给新创建的这个 a 链接
            let now = new Date();
            let timestamp = now.getTime();
            link.setAttribute('download', '提现列表' + timestamp + '.xls'); // 设置下载文件名
            document.body.appendChild(link);
            // 使用js点击这个链接
            link.click();
        }).catch((error) => {
            console.log("error", error);

        })
    }

    /**
     * 审批提现金额
     * approvalReason	string审批结果
approvalStatus	string审批状态 0临时保存 100 待审核 101 审核通过 200 驳回 300 终止/已打款
id	string数据
     */
    approvePrice(approvalType: {
        approvalReason?: string
        approvalStatus: string,
        id: string
    }) {
        approveWithdrawal(approvalType).then((res) => {
            console.log('审批提现金额', res.data);
            this.$confirm(res.data)
            this.getProduct()

        }).catch((err) => {
            this.$confirm(err)

        })
    }
    /**取消审核判断 */
    throughClose() {
        this.passThrough = false
        this.showDes = false
    }
    /**
     * 点击列表中的通过操作
     */
    passThroughpd(id: string) {
        this.passThrough = true
        this.approvalType.id = id

    }
    /**确定审核通过 */
    primaryThrough() {
        this.passThrough = false
        this.approvalType.approvalStatus = '1'
        this.approvePrice(this.approvalType)

    }
    /**点击打款按钮 */
    successPayment(id: string) {
        this.$confirm('点击打款按钮' + id)
        // this.approvalType.id = id
        // this.approvalType.approvalStatus = '101'
        // this.approvePrice(this.approvalType)
    }

    /*** 关闭驳回原因输入 */
    handleClose(done: any) {
        // this.dangerShow = true
        done();
        this.$message('已关闭驳回原因输入');
        // this.$confirm('确认关闭？')
        //     .then(_ => {
        //         done();
        //     })
        //     .catch(_ => { });
    }
    setPaymentCode(row: any) {
        this.showPaymentCode = true;
        this.paymentCode = row;

    }
    /**
     * 点击列表中的驳回操作
     */
    dangerReject(id: string) {
        this.dangerShow = true
        this.approvalType.id = id

    }
    /**确定驳回 */
    primaryReject() {
        this.dangerShow = false
        if (!this.rejection) {
            this.$confirm('驳回原因不能为空')
            return
        }
        // 批量审核中的驳回按钮操作按钮
        if (this.batchShow) {
            this.approvalBatch.forEach(item => {
                item.approvalReason = this.rejection
            })
            this.batchApproveHan()
            this.approvalBatch = []
            this.rejection = ''
            return
        }
        this.approvalType.approvalStatus = '-1'
        this.approvalType.approvalReason = this.rejection
        this.approvePrice(this.approvalType)
        this.this.rejection = ''
        this.$delete(this.approvalType, 'approvalReason')


    }

}
</script>

<style lang="scss" scoped>
.personPhone {
    margin-top: 5px;
}

.showDesPass {
    width: 100%;
    min-height: 32px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 1);
    color: #606266;
    font-size: 14px;
    font-family: Roboto;
    border: 1px solid #DCDFE6;
    display: flex;
    justify-content: center;
    align-items: center;
}

.el-form-item1 {
    /deep/ * {
        * {
            color: red;
        }
    }
}

.dialogPass {
    color: #010101;
    display: flex;
    justify-self: center;
    align-items: center;
    flex-direction: column;

    .el-button {
        margin-top: 20px;
        background: rgb(9, 240, 22);
        color: #ffffff;
        width: 102px;
        height: 29px;
        font-size: 18px;
        outline: none;
        border: none;
        text-align: center;
        line-height: 14px;
    }
}

.dialogWhe {
    color: #040404;
    font-size: 20px;
    margin-bottom: 10px;
    text-align: center;
    padding-bottom: 10px;
    border-bottom: 1px solid rgb(187, 187, 187);
}

.rejectClass {
    color: #010101;
}

.paid {
    color: #670BFB;
}

.operateCenter {
    .el-button {
        margin: 5px 0;
        margin-right: 10px;

    }
}
</style>