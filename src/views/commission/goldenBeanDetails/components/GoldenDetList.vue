<template>
    <div style="margin-top: 20px">
        <el-table :data="goodList" style="width: 100%" :summary-method="getSummaries" show-summary>
            <el-table-column label="序号" type="index" width="60">
            </el-table-column>
            <el-table-column prop="nikeName" label="订单号" show-overflow-tooltip>
                <template v-slot="{ row }">
                    <el-button type="text" @click="toOrder(row.orderId)">{{ row.orderId }}</el-button>
                </template>
            </el-table-column>
            <el-table-column prop="nikeName" label="用户名称" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="phone" label="用户电话" show-overflow-tooltip>
            </el-table-column>

            <el-table-column prop="way" label="变更方式" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="remark" label="分佣标题" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="lastGolden" label="变动前金豆" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="amount" label="金豆增减" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="totalGolden" label="变动后金豆" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="sourceNikeName" label="金豆来源" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="time" label="变更时间" show-overflow-tooltip>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { searchMiniAccountGoldenDet } from '@/api/reward/reward';
import { SearchKeyType } from "./searchType";
@Component({
    components: {

    }
})
export default class GoldenDetList extends Vue {
    name = "GoldenDetList"
    goodList = [];
    searchType = {
        current: 1,
        size: 10
    } as SearchKeyType;
    total = 0;
    created() {
        this.getGoldenDetList();
    }
    getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
            if (index === 0) {
                sums[index] = '合计';
                return;
            }
            //判断字段为payAmount或者productQuantity才进行合计 并且处理小数点位数
            if (column.property === 'amount') {
                const values = data.map(item => Number(item[column.property]));
                const total = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return prev + curr;
                    }
                    return prev;
                }, 0);
                const totalStr = total.toFixed(2);
                sums[index] = totalStr;
            }
        });

        return sums;
    }
    async getGoldenDetList() {
        const param = this.searchType;
        try {
            const res = await searchMiniAccountGoldenDet(param);
            const goodList = res.data.list;
            this.total = res.data.total;
            this.goodList = goodList;
        } catch (error) {
            console.log(error);
        }
        this.$emit("getShowProList", this.goodList);
    }
    toOrder(orderId) {
        this.$router.push({ path: "/order/delivery", query: { orderId, t: Date.now() } })
    }
}
</script>

<style lang="scss" scoped>
.goodList {
    width: 250px;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-right: 20px;
    overflow: hidden;
}

.center {
    display: flex;
    justify-content: center;
}

.digTitle {
    font-size: 17px;
    font-weight: bold;
}
</style>