<template>
    <div>
        <ListApart ref="ListApart"></ListApart>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Ref } from "vue-property-decorator";
import ListApart from "./ListApart.vue";
@Component({
    components: {
        ListApart
    }
})
export default class Goods extends Vue {
    @Ref()
    readonly ListApart?: HTMLFormElement;
}
</script>
<style lang="scss" scoped></style>