<!-- 2025.6.4有改动的页面 -->
<template>
    <div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="佣金规则设置" name="first"></el-tab-pane>
        </el-tabs>
        <el-form :model="ruleForm" ref="ruleForm" :rules="rules" :label-position="'left'" label-width="110px">
            <!-- 取消分销设置
            <div class="distributionSettings">
                <div>分销设置</div>
            </div>
            <el-form-item label="分销佣金类型" >
                <el-radio-group v-model="ruleForm.ruleType">
                    <el-radio :label="100">固定金额</el-radio>
                    <el-radio :label="101">百分比</el-radio>
                    <el-radio :label="102">以单个商品</el-radio>
                    <el-radio :label="103">根据会员等级</el-radio>
                </el-radio-group>
            </el-form-item>-->
            <!-- <el-form-item label-width="20px" v-if="ruleForm.ruleType!=102 && ruleForm.ruleType!=103">
                <div>
                    <el-row>
                        <el-col :span="2" :align="'right'">
                            <div>分销佣金 </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="DistributionCommission">
                                <div class="commission__top">
                                    <span class="span1">1级分销佣金</span>
                                    <input v-model="ruleForm.parentReceive" class="distribution__commission" />
                                    <span class="span2">{{ruleForm.ruleType==100?'元':'%'}}</span>
                                </div>
                                <div class="commission__top">
                                    <span class="span1">2级分销佣金</span>
                                    <input v-model="ruleForm.aboveParentReceive" class="distribution__commission" />
                                    <span class="span2">{{ruleForm.ruleType==100?'元':'%'}} </span>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </el-form-item> -->
            <!-- <el-form-item label="最低消费" v-if="ruleForm.ruleType==100">

                <el-input v-model="ruleForm.minPayAmount" style="width:180px;"></el-input>
                <span class="first">元</span>
            </el-form-item> -->

            <div class="distributionSettings" style="margin-top:30px;margin-bottom:10px;">
                <div>其他设置</div>
            </div>
            <el-form-item label="佣金划转类型" prop="ruleType">
                <el-radio-group v-model="ruleForm.ruleType" @change="changeRuleType">
                    <el-radio :label="100">固定金额</el-radio>
                    <el-radio :label="101">百分比</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="佣金划转比例" prop="commissionTransferRate" v-if="ruleForm.ruleType == 101">
                <el-input v-model="ruleForm.commissionTransferRate" style="width:180px;"></el-input>
                <span class="first">%</span>
                <!-- <span class="illustrate">设置最小提现额度 (0表示不设置最小提现)</span> -->
            </el-form-item>
            <el-form-item label="佣金划转金额" prop="commissionTransferRate" v-if="ruleForm.ruleType == 100">
                <el-input v-model="ruleForm.commissionTransferRate" style="width:180px;"></el-input>
                <span class="first">元</span>
                <!-- <span class="illustrate">设置最小提现额度 (0表示不设置最小提现)</span> -->
            </el-form-item>
            <div class="distributionSettings">
                <div>分销设置</div>
            </div>



            <el-form-item label="最小提现" prop="minCashAmount">
                <el-input v-model="ruleForm.minCashAmount" style="width:180px;"></el-input>
                <span class="first">元</span>
                <span class="illustrate">设置最小提现额度 (0表示不设置最小提现)</span>
            </el-form-item>
            <el-form-item label="最大提现" prop="maxCashAmount">
                <el-input v-model="ruleForm.maxCashAmount" style="width:180px;"></el-input>
                <span class="first">元</span>
                <span class="illustrate">设置最大提现额度 (0表示不设置最大提现)</span>
            </el-form-item>
            <el-form-item label="提现手续费" prop="cashRate">
                <el-input v-model="ruleForm.cashRate" style="width:180px;"></el-input>
                <span class="first">%</span>
                <span class="illustrate">设置提现手续费 (0表示不设置提现手续费)</span>
            </el-form-item>
            <el-form-item label="提现次数" prop="cashTimes">
                <el-input v-model="ruleForm.cashTimes" style="width:180px;"></el-input>
                <span class="first">次/天</span>
                <span class="illustrate">设置每个用户当天提现次数 (0表示不设置提现次数)</span>
            </el-form-item>
            <el-form-item>
                <el-button v-if="isSupper || saveButton" type="primary" round @click="submitForm('ruleForm')"
                    style="margin-top:250px;margin-left:300px;width:100px;height:40px;">保存</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Ref } from "vue-property-decorator";
import { getCommissionRule, saveCommissionRule } from "@/api/withdrawalApi/withdrawalApi";
// @Component({
//     components: {}
// })
import { ruleType } from "./ruleType";
@Component({})
export default class rulesSetup extends Vue {
    activeName = 'first'
    // f
    radio = 1
    distributionPrice = 0.00
    ruleForm = {
        "createTime": "2023-08-29 15:20:37",
        "updateTime": "2023-09-12 09:43:16",
        "deleted": false,
        "openFlag": 1,
        "ruleType": 101,
        "parentReceive": 0,
        "aboveParentReceive": 0,
        "minCashAmount": 0,
        "commissionTransferRate": 0,
        "maxCashAmount": 0,
        "cashRate": 0,
        "cashTimes": 0,
    } as ruleType;
    rules = {
        minCashAmount: [
            { required: true, message: '请输入最小提现', trigger: 'blur' }],
        commissionTransferRate: [
            { required: true, message: '请输入佣金划转比例/金额', trigger: 'blur' }],
        maxCashAmount: [
            { required: true, message: '请输入最大提现', trigger: 'blur' }],
        cashRate: [
            { required: true, message: '请输入提现手续费', trigger: 'blur' }],
        cashTimes: [
            { required: true, message: '请输入提现次数', trigger: 'blur' }],
        ruleType: [
            { required: true, message: '请输入选择佣金划转类型', trigger: 'blur' }],

    }

    menuName = "佣金设置";

    buttonList = [];

    isSupper = 0;

    saveButtonCode = "commissionSetting.save";

    saveButton = false;

    mounted() {
        this.buttonAuth();
    }
    created() {
        this.getRule()
    }

    buttonAuth() {
        this.isSupper = this.$STORE.userStore.userInfo.isSupper
        let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

        let buttonList = [];

        authMenuButtonVos.forEach(element => {
            buttonList.push(element.buttonCode);
        });

        this.buttonList = buttonList

        var saveButtonData = buttonList.find(e => e == this.saveButtonCode);

        if (saveButtonData != null && saveButtonData != undefined) {
            this.saveButton = true;
        }
    }
    changeRuleType(){
        this.ruleForm.commissionTransferRate = 0
    }
    getRule() {
        getCommissionRule({}).then((res) => {
            console.log('wwwwwwwww', res.data);
            if (res.data == null) {
                return
            }
            this.ruleForm = res.data;
        }).catch((err) => {
            this.$message(err)
        })
    }
    submitForm(formName) {
        this.$refs[formName].validate((valid) => {
            console.log('提现设置!', this.ruleForm, formName);
            if (valid) {
                // alert('submit!');
            } else {
                console.log('error submit!!');
                return false;
            }
        });
        this.saveRule()
        console.log('submit!', this.ruleForm);
    }
    /**设置佣金规则*/
    saveRule() {
        // for (let key in this.ruleForm) {
        // console.log(key, ":",);

        // }
        saveCommissionRule((this.ruleForm)).then((res) => {
            // console.log('wwwwwwwww',res.data);
            // let msg=res.msg as string|null
            this.$message.success('设置成功')
            // this.ruleForm=res.data;
        }).catch((err) => {
            this.$message(err)
        })
    }
    handleClick(tab, event) {
        console.log(tab, event);
    }

}
</script>
<style lang="scss" scoped>
.distributionSettings {
    background: #E6E6E6;

    height: 30px;
    display: flex;
    align-items: center;
    padding-left: 10px;

    div {
        color: #101010;
        border-left: 3px solid #09F016;
        padding-left: 10px;
    }

}

.distributionType {
    margin: 10px 0 10px 20px;

    span {
        margin-right: 10px;
    }
}

.DistributionCommission {
    margin-left: 5px;

    .commission__top {
        display: flex;
        border: 1px solid #a0b3d6;
        margin-bottom: 2px;

    }

    span {
        display: inline-block;
        text-align: center;
        background: #bbbbbb;
        line-height: 20px;
        color: rgba(33, 31, 31, 1);

    }

    .span1 {
        width: 200px;

    }

    .span2 {
        width: 50px;
    }

    .distribution__commission {

        width: 295px;
        height: 20px;
        line-height: 20px;
        padding: 3px;
        font-size: 12px;
        outline: none;
        text-align: center;
        border: none;
        // overflow-x: hidden;
        // overflow-y: auto;
    }
}

.first {
    display: inline-block;
    width: 100px;
    margin-left: 10px;
    color: #f50101;
}

.illustrate {
    color: #585454;
}
</style>
