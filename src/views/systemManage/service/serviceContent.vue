<!--
 * @description: 商家服务项目功能
-->
<template>
  <div class="service-content">
    <!-- 搜索部分 -->
    <m-card class="form" :needToggle="true">
      <el-form class="search-form" ref="searchFormRef" :model="searchForm" label-width="100px">
        <el-row :gutter="40">
          <el-col :span="8">
            <el-form-item label="项目名称">
              <el-input v-model="searchForm.serviceName" clearable placeholder="请输入服务项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" clearable placeholder="请选择状态">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注">
              <el-input v-model="searchForm.remark" clearable placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
        <div class="search-btn-wrapper">
          <el-button type="primary" @click="searchQuery(1)">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </el-form>
    </m-card>
    
    <!-- 操作按钮部分 -->
    <div class="operation-wrapper">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
    </div>
    
    <!-- 列表 -->
    <el-table :data="tableData" border style="width: 100%" v-loading="loading">
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="serviceName" label="服务项目名称"  />
      <el-table-column prop="status" label="状态" >
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注"  show-overflow-tooltip />
      <el-table-column label="操作"  fixed="right">
        <template slot-scope="scope">

          <el-tooltip content="停用" placement="top">
            <el-button v-if="scope.row.status == '1'" type="text"
                       size="medium" @click="updateStatus(scope.row)">停用</el-button>
          </el-tooltip>
          <el-tooltip content="启用" placement="top">
            <el-button v-if="scope.row.status == '0'" type="text"
                       size="medium" @click="updateStatus(scope.row)">启用</el-button>
          </el-tooltip>
          <!-- 编辑-->
          <el-tooltip content="编辑" placement="top">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </el-tooltip>
          <!-- 删除-->
          <el-tooltip content="删除" placement="top">
            <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-wrapper">
      <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>
    
    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="服务项目名称" prop="serviceName">
          <el-input v-model="formData.serviceName" placeholder="请输入服务项目名称" />
        </el-form-item>
        <el-form-item label="状态" prop="status" :rules="[
							{ required: true, message: '状态不能为空' }
						]">
          <el-select v-model="formData.status" placeholder="请选择">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.key"
                       :value="item.value" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" maxlength="500" show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import { ElForm } from 'element-ui/types/form';
import {
  getServiceContentList,
  addServiceContent,
  updateServiceContent,
  deleteServiceContent,
  updateStatusService, updateStatusTemplate,
} from "@/api/serviceContent/index";
import { ServiceContent, ServiceTemplate, statusOptions } from "./types";

@Component({
  components: {
    PageManage
  }
})
export default class ServiceContentComponent extends Vue {
  @Ref() readonly searchFormRef!: ElForm;
  @Ref() readonly formRef!: ElForm;
  
  // 搜索表单
  searchForm = {
    serviceName: '',
    status: null,
    remark: ''
  };
  
  // 表格数据
  tableData: ServiceContent[] = [];
  loading = false;
  
  // 分页
  pageSize = 10;
  pageNum = 1;
  total = 0;
  
  // 弹窗
  dialogVisible = false;
  dialogTitle = '新增商家服务项目';
  isEdit = false;
  
  // 表单数据
  formData: ServiceContent = {
    serviceName: '',
    status: 1, // 默认启用
    remark: ''
  };
  
  // 表单规则
  formRules = {
    serviceName: [
      { required: true, message: '请输入服务项目名称', trigger: 'blur' },
      { max: 32, message: '长度不能超过32个字符', trigger: 'blur' }
    ],
    status: [
      { required: true, message: '请选择状态', trigger: 'change' }
    ],
    remark: [
      { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
    ]
  };
  
  // 状态选项
  statusOptions = statusOptions;
  
  mounted() {
    this.searchQuery(1);
  }
  
  /**
   * 搜索查询
   */
  searchQuery(pageNum: number) {
    this.loading = true;
    const params = {
      current: pageNum,
      size: this.pageSize,
      ...this.searchForm
    };
    
    getServiceContentList(params).then(res => {
      this.tableData = res.data.list || [];
      this.total = res.data.total || 0;
      this.pageSize = res.data.size;
      this.pageNum = res.data.current;
      this.loading = false;
    }).catch(() => {
      this.loading = false;
    });
  }
  
  /**
   * 重置搜索
   */
  resetSearch() {
    this.searchFormRef.resetFields();
    this.searchQuery(1);
  }
  
  /**
   * 处理新增
   */
  handleAdd() {
    this.dialogTitle = '新增商家服务项目';
    this.isEdit = false;
    this.formData = {
      serviceName: '',
      status: 1, // 默认启用
      remark: ''
    };
    this.dialogVisible = true;
    
    // 等待DOM更新后重置表单校验结果
    this.$nextTick(() => {
      if (this.formRef) {
        this.formRef.clearValidate();
      }
    });
  }
  
  /**
   * 处理编辑
   */
  handleEdit(row: ServiceContent) {
    this.dialogTitle = '编辑商家服务项目';
    this.isEdit = true;
    this.formData = { ...row };
    this.dialogVisible = true;
    
    // 等待DOM更新后重置表单校验结果
    this.$nextTick(() => {
      if (this.formRef) {
        this.formRef.clearValidate();
      }
    });
  }
  updateStatus(row: ServiceContent) {
    console.log(row);
    let status = row.status;
    let text = "";
    if (status == 0) {
      text = "是否启用服务模版?";
    } else {
      text = "是否停用服务模版?";
    }

    this.$confirm(text, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const updateParam = {
        id: row.id,
      }
      if (status == 0) {
        updateParam.status = 1;
      }
      if (status == 1) {
        updateParam.status = 0;
      }

      updateStatusService(updateParam).then(res => {
        this.$message({
          type: 'success',
          message: '操作成功'
        });
        this.searchQuery(1);
      })
    }).catch(() => {
      this.$message({
        type: 'info',
        message: '已取消'
      });
      this.searchQuery(1);
    });

  }
  /**
   * 处理删除
   */
  handleDelete(row: ServiceContent) {
    this.$confirm('确认删除该服务项目?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      deleteServiceContent({id: row.id}).then(res => {
        this.$message.success('删除成功');
        this.searchQuery(this.pageNum);
      }).catch(err => {
        this.$message.error(err || '删除失败');
      });
    }).catch(() => {});
  }
  
  /**
   * 提交表单
   */
  handleSubmit() {
    this.formRef.validate((valid: boolean) => {
      if (valid) {
        const request = this.isEdit ? updateServiceContent : addServiceContent;
        const successMsg = this.isEdit ? '修改成功' : '新增成功';
        
        request(this.formData).then(res => {
          this.$message.success(successMsg);
          this.dialogVisible = false;
          this.searchQuery(this.pageNum);
        }).catch(err => {
          this.$message.error(err || (this.isEdit ? '修改失败' : '新增失败'));
        });
      }
    });
  }
  
  /**
   * 处理每页显示数量改变
   */
  handleSizeChange(val: number) {
    this.pageSize = val;
    this.searchQuery(1);
  }
  
  /**
   * 处理页码改变
   */
  handleCurrentChange(val: number) {
    this.pageNum = val;
    this.searchQuery(val);
  }
}
</script>

<style lang="scss" scoped>
.service-content {
  padding: 20px;
  
  .search-form {
    .search-btn-wrapper {
      text-align: right;
      padding-right: 50px;
      margin-top: 10px;
    }
  }
  
  .operation-wrapper {
    margin: 20px 0;
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 