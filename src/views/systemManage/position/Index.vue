<!--
 * @description: 平台用户管理
-->
<template>
	<div class="customer">
		<el-tabs>
			<div class="memberList">
				<div class="line"></div>

				<m-card class="form" :needToggle="true">
					<el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="职位名称">
                  <el-input v-model="dataForm.positionName" clearable
										placeholder="请输入职位名称" />
                  </el-form-item>
							</el-col>
              <el-col :span="10">
                <el-form-item label="状态">
                  <el-select v-model="dataForm.status" placeholder="请选择">
                    <el-option v-for="item in statusList" :key="item.value" :label="item.key"
                               :value="item.value" >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="备注">
                  <el-input v-model="dataForm.remark" clearable
                            placeholder="请输入备注" />
                </el-form-item>
              </el-col>
						</el-row>

						<el-button type="primary" style="margin-left:100px" @click="searchQuery(1)">搜索</el-button>
					</el-form>
				</m-card>

				<el-button type="primary" icon="el-icon-circle-plus-outline" @click="openAdd()"
					v-if="isSupper || addButton" style="margin-bottom:20px; float:right; margin-right: 60px;">
					新增
				</el-button>

				<el-dialog title="新增职位" :visible.sync="addUserFlag" width="30%">
					<el-form :model="addEditForm" ref="addEditForm" label-width="100px" >
						<el-form-item label="职位名称" prop="positionName" :rules="[
							{ required: true, message: '职位名称不能为空' }
						]">
							<el-input v-model="addEditForm.positionName" placeholder="请输入职位名称" style="width: 56%;"></el-input>
						</el-form-item>


						<el-form-item label="状态" prop="status" :rules="[
							{ required: true, message: '状态不能为空' }
						]">
							<el-select v-model="addEditForm.status" placeholder="请选择">
								<el-option v-for="item in statusList" :key="item.value" :label="item.key"
									:value="item.value" >
								</el-option>
							</el-select>
						</el-form-item>

            <el-form-item label="备注"  prop="remark">
              <el-input
                type="textarea"
                :rows="4"
                v-model="addEditForm.remark"
                placeholder="填写您的备注内容"
                style="width: 100%; max-width: 500px"
              />
            </el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button @click="addUserFlag = false;">取 消</el-button>
						<el-button type="primary" @click="addHandler()" :loading="loading">确 定</el-button>
					</span>
				</el-dialog>
				<!-- 编辑 -->
				<el-dialog title="编辑职位" :visible.sync="editUserFlag" width="30%">
          <el-form :model="addEditForm" ref="addEditForm" label-width="100px" >
            <el-form-item label="职位名称" prop="positionName" :rules="[
							{ required: true, message: '职位名称不能为空' }
						]">
              <el-input v-model="addEditForm.positionName" placeholder="请输入职位名称" style="width: 56%;"></el-input>
            </el-form-item>

            <el-form-item label="状态" prop="status" :rules="[
							{ required: true, message: '状态不能为空-' }]">
              <el-select v-model="addEditForm.status" placeholder="请选择">
                <el-option v-for="item in statusList" :key="item.value" :label="item.key"
                           :value="item.value" >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                :rows="4"
                v-model="addEditForm.remark"
                placeholder="填写您的备注内容"
                style="width: 100%; max-width: 500px"
              />
            </el-form-item>
          </el-form>
					<span slot="footer" class="dialog-footer">
						<el-button @click="editUserFlag = false;">取 消</el-button>
						<el-button type="primary" @click="editHandler()" :loading="loading">确 定</el-button>
					</span>
				</el-dialog>

        <!-- 列表 -->
				<template>
					<el-table :data="dataList" style="width: 100%" border max-height="100%">

            <el-table-column prop="positionName" label="职位名称"  >
            </el-table-column>

            <el-table-column prop="status" label="状态" >
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                  {{ scope.row.status === 1 ? '启用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" >
            </el-table-column>
						<el-table-column label="操作" >
							<template slot-scope="scope">
								<!-- 删除-->
								<el-tooltip content="停用" placement="top">
									<el-button v-if="scope.row.status == '1' && (isSupper || editButton)" type="text"
										size="medium" @click="updateStatus(scope.row)">停用</el-button>
								</el-tooltip>
								<el-tooltip content="启用" placement="top">
									<el-button v-if="scope.row.status == '0' && (isSupper || editButton)" type="text"
										size="medium" @click="updateStatus(scope.row)">启用</el-button>
								</el-tooltip>
								<!-- 编辑-->
								<el-tooltip content="编辑" placement="top">
									<el-button v-if="isSupper || editButton" type="text" size="medium"
										@click="editBtnHandler(scope.row)">编辑</el-button>
								</el-tooltip>
								<!-- 删除-->
								<el-tooltip content="删除" placement="top">
									<el-button v-if="isSupper || deleteButton" type="text" size="medium"
										@click="delStroeFront(scope.row)">删除</el-button>
								</el-tooltip>
							</template>
						</el-table-column>

					</el-table>
				</template>
				<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
			</div>
		</el-tabs>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import { ElForm } from 'element-ui/types/form';
import { userStore } from "@/store/modules/user";
import {
  pagePosition,
  addPosition,
  deletePosition,
  editPosition,
  updateStatus,
} from "@/api/position/Index";
@Component({
  components: {
    PageManage
  }
})
export default class Index extends Vue {
	@Ref()
	readonly dataFormRef!: ElForm;

	// 数据表单  组成数据用于向后端提交数据
	dataForm = {
	  positionName: '',
	  status: '',
	  remark: '',
	};

  addEditForm = {
    id: '',
    positionName: '',
    status: '',
    remark: '',
  }

	loading = false

	statusList = [
	  { key: '启用', value: 1 },
	  { key: '停用', value: 0 }
	]
  


	/** 分页条数 */
	pageSize = 10;

	/** 分页页码 */
	pageNum = 1;

	/** 数据长度 */
	total = 0;

	// 存放全部仓库数据
  dataList = [];

	menuName = "职位模版管理";

	buttonList = []

	addUserFlag = false

	editUserFlag = false;

	isSupper = 0;

	addButtonCode = "storeFront.add";

	addButton = false;

	editButtonCode = "storeFront.edit";

	editButton = false;

	deleteButtonCode = "storeFront.delete";

	deleteButton = false;


	mounted() {
	  this.searchQuery(1);
	  this.buttonAuth();
	}

	buttonAuth() {
	  this.isSupper = this.$STORE.userStore.userInfo.isSupper
	  let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)
	  let buttonList = [];
	  authMenuButtonVos.forEach(element => {
	    buttonList.push(element.buttonCode);
	  });
	  this.buttonList = buttonList

	  var addButtonData = buttonList.find(e => e == this.addButtonCode);
	  if (addButtonData != null && addButtonData != undefined) {
	    this.addButton = true;
	  }

	  var editButtonData = buttonList.find(e => e == this.editButtonCode);
	  if (editButtonData != null && editButtonData != undefined) {
	    this.editButton = true;
	  }

	  var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);
	  if (deleteButtonData != null && deleteButtonData != undefined) {
	    this.deleteButton = true;
	  }
	}

	openAdd() {
	  //重置弹出框的数据为空
	  this.addEditForm = {
	    id: '',
	    positionName: '',
	    status: 1, //默认
	    remark: '',
	  }
	  this.addUserFlag = true
	}

	//用户新增的提交方法
	addHandler() {
	  if (this.loading) {
	    this.$message.error("禁止重复提交");
	    return;
	  }
	  if (this.addEditForm.positionName == '') {
	    this.$message.error("职位名称不能为空");
	    return;
	  }

    if (this.addEditForm.status == null || isNaN(this.addEditForm.status)) {
      this.$message.error("状态不能为空");
      return;
    }
	  this.loading = true

    addPosition(this.addEditForm).then(res => {
	    this.$message.success("新增成功");
	    // 关闭模态框
	    this.addUserFlag = false;
	    setTimeout(() => {
	      this.loading = false
	    }, 100);
	    this.searchQuery(1)
	  }).catch(err => {
	    this.loading = false
	    this.$message.error(err || "网络错误");
	  });
	}

	//用户编辑的提交方法
	editHandler() {
	  if (this.loading) {
	    this.$message.error("禁止重复提交");
	    return;
	  }
	  if (this.addEditForm.positionName == '') {
	    this.$message.error("职位名称不能为空");
	    return;
	  }
    if (this.addEditForm.status == null || isNaN(this.addEditForm.status)) {
      this.$message.error("状态不能为空");
      return;
    }
	  this.loading = true
    editPosition(this.addEditForm).then(res => {
	    this.$message.success("编辑成功");
	    // 关闭模态框
	    this.editUserFlag = false;
	    setTimeout(() => {
	      this.loading = false
	    }, 100);
	    // 提交数据成功，重新获取一次数据进行渲染
	    this.searchQuery(this.pageNum);
	  }).catch(err => {
	    this.loading = false
	    this.$message.error(err || "网络错误");
	  });
	}

	delStroeFront(row) {
	  this.$confirm('此操作将永久删除职位, 是否继续?', '提示', {
	    confirmButtonText: '确定',
	    cancelButtonText: '取消',
	    type: 'warning'
	  }).then(() => {
	    const delParam = {
	      id: row.id,
	    }
      deletePosition(delParam).then(res => {
	      this.$message.success("删除成功");
	      // 关闭模态框
	      this.searchQuery(1)
	    })

	  }).catch(() => {
	    this.$message({
	      type: 'info',
	      message: '已取消删除'
	    });
	    this.searchQuery(1);
	  });
	}

	//获取列表（数据）
	searchQuery(pageNum: number) {
	  // console.log('执行了');
	  const form = this.dataForm;
	  const param = {
	    current: pageNum,
	    size: this.pageSize,
	    ...form
	  }

    pagePosition(param).then(res => {
	    // 仓库数据
	    this.dataList = res.data.list
	    // 显示数据条数
	    this.pageSize = res.data.size;
	    // 第几页
	    this.pageNum = res.data.current;

	    this.total = res.data.total;
	  })
	}

	//获取当前行需要修改的信息
	editBtnHandler(val: any) {
	  this.editUserFlag = true;
	  // this.EditwareList =    JSON.parse(JSON.stringify(([ val ])));
	  this.addEditForm = { ...val };
	}

	updateStatus(scope) {
	  console.log(scope);
	  let status = scope.status;
	  let id = scope.id;
	  let text = "";
	  if (status == 0) {
	    text = "是否启用职位模版?";
	  } else {
	    text = "是否停用用职位模版?";
	  }

	  this.$confirm(text, '提示', {
	    confirmButtonText: '确定',
	    cancelButtonText: '取消',
	    type: 'warning'
	  }).then(() => {
	    const updateParam = {
	      id: scope.id,
	    }
	    if (status == 0) {
	      updateParam.status = 1;
	    }
	    if (status == 1) {
	      updateParam.status = 0;
	    }

	    updateStatus(updateParam).then(res => {
	      this.$message({
	        type: 'success',
	        message: res.data
	      });
	      this.searchQuery(1);
	    })
	  }).catch(() => {
	    this.$message({
	      type: 'info',
	      message: '已取消'
	    });
	    this.searchQuery(1);
	  });

	}

	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
	  this.pageSize = val;
	  this.searchQuery(1);
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
	  this.pageNum = val;
	  this.searchQuery(val);
	}


}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.el-checkbox__inner {
	border-radius: 50% !important;
}
</style>
