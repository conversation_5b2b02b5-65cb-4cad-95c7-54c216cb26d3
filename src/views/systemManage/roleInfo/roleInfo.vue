<template>
    <div class="customer">
        <el-tabs>
            <div class="memberList">
                <div class="line"></div>
                <m-card class="form" :needToggle="true">
                    <el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
                        <el-row :gutter="40">
                            <el-col :span="10">
                                <el-form-item label="角色名称"><el-input v-model="dataForm.roleName" clearable
                                        placeholder="请输入角色名称" /></el-form-item>
                            </el-col>
                            <el-col :span="10">
                                <el-form-item label="角色编号"><el-input v-model="dataForm.roleCode" clearable
                                        placeholder="请输入角色编号" /></el-form-item>
                            </el-col>
                        </el-row>
                        <!-- <el-row :gutter="40">
                            <el-col :span="10">
                                <el-form-item label="消息类型">
                                    <el-select v-model="dataForm.msgType" multiple collapse-tags style="width: 100%;"
                                        placeholder="请选择">
                                        <el-option v-for="item in optionsType" :key="item.value" :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row> -->
                        <el-button type="primary" style="margin-left:100px" @click="searchQuery()">搜索</el-button>
                    </el-form>
                </m-card>
                <el-button type="primary" icon="el-icon-circle-plus-outline" @click="addRoleFlag = true"
                    v-if="isSupper || addButton" style="margin-bottom:20px; float:right; margin-right: 60px;">
                    新增
                </el-button>
                <!-- 新增角色 模态框 -->
                <el-dialog title="新增角色" :visible.sync="addRoleFlag" width="30%">
                    <el-form :model="addForm" ref="addForm" label-width="100px">
                        <el-form-item label="角色名称" prop="roleName" :rules="[
                            { required: true, message: '角色名称不能为空' }
                        ]">
                            <el-input v-model="addForm.roleName" autocomplete="off"></el-input>
                        </el-form-item>
                        <el-form-item label="角色编码" prop="roleCode" :rules="[
                            { required: true, message: '角色编码不能为空' }
                        ]">
                            <el-input v-model="addForm.roleCode"></el-input>
                        </el-form-item>

                        <!-- <el-form-item label="消息类型" prop="msgType" :rules="[
                            { required: true, message: '消息类型不能为空' }
                        ]">
                            <el-select v-model="addForm.msgType" multiple collapse-tags style="width: 100%;"
                                placeholder="请选择">
                                <el-option v-for="item in optionsType" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item> -->

                        <el-form-item label="备注" prop="remark">
                            <el-input type="textarea" :rows="2" v-model="addForm.remark"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="addRoleFlag = false;">取 消</el-button>
                        <el-button type="primary" @click="addHandler()">确 定</el-button>
                    </span>
                </el-dialog>
                <!-- 编辑角色模块 -->
                <el-dialog title="编辑角色" :visible.sync="editRoleFlag" width="30%">
                    <el-form :model="editForm" ref="editForm" label-width="100px">
                        <el-form-item label="角色名称" prop="roleName" :rules="[
                            { required: true, message: '角色名称不能为空' }
                        ]">
                            <el-input v-model="editForm.roleName" autocomplete="off"></el-input>
                        </el-form-item>
                        <el-form-item label="角色编码" prop="roleCode" :rules="[
                            { required: true, message: '角色编码不能为空' }
                        ]">
                            <el-input v-model="editForm.roleCode"></el-input>
                        </el-form-item>
                        <!-- <el-form-item label="消息类型" prop="msgType" :rules="[
                            { required: true, message: '消息类型不能为空' }
                        ]">
                            <el-select v-model="editForm.msgType" multiple collapse-tags style="width: 100%;"
                                placeholder="请选择">
                                <el-option v-for="item in optionsType" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
                        <el-form-item label="备注" prop="remark">
                            <el-input type="textarea" :rows="2" v-model="editForm.remark"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="editRoleFlag = false;">取 消</el-button>
                        <el-button type="primary" @click="editHandler()">确 定</el-button>
                    </span>
                </el-dialog>
                <!--角色授权-->
                <el-drawer title="角色授权" :visible.sync="empowerFlag" :direction="direction" :before-close="handleClose">
                    <div class="xin-main">
                        <div class="xin-content" style="height: 100%;padding-bottom: 50px;">
                            <el-tree ref="tree" :data="dataList" show-checkbox :props="defaultProps" node-key="id"
                                :default-checked-keys="chooseId" default-expand-all :check-strictly="true">
                            </el-tree>
                        </div>
                        <div class="footer">
                            <div class="footer-content" style="z-index: 999;">
                                <el-button @click="handleClose()">取 消</el-button>
                                <el-button type="primary" @click="empowerHandler()">确 定</el-button>
                            </div>
                        </div>

                    </div>
                </el-drawer>

                <!-- 授权角色消息类型 -->
                <el-dialog title="授权角色消息类型" :visible.sync="msgTypeFlag" width="30%" :close-on-click-modal="false">
                    <el-form :model="msgTypeForm" ref="msgTypeForm" label-width="100px">

                        <el-form-item label="消息类型" prop="msgType" :rules="[
                            { required: true, message: '消息类型不能为空' }
                        ]">
                            <el-select v-model="msgTypeForm.msgType" multiple style="width: 100%;"
                                placeholder="请选择">
                                <el-option v-for="item in optionsType" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="msgTypeFlag = false;">取 消</el-button>
                        <el-button type="primary" @click="msgTypeSure()">确 定</el-button>
                    </span>
                </el-dialog>

                <!--角色列表-->
                <template>
                    <el-table :data="roleList" style="width: 100%" border>
                        <el-table-column prop="roleName" label="角色名称" align="center">
                        </el-table-column>
                        <el-table-column prop="roleCode" label="角色编号" align="center">
                        </el-table-column>
                        <el-table-column prop="remark" label="备注" align="center">
                        </el-table-column>
                        <el-table-column label="操作"  align="center">
                            <template slot-scope="scope">
                                <el-tooltip effect="dark" content="编辑" placement="top" v-if="isSupper || editButton">
                                    <el-button type="text" size="medium" icon="el-icon-edit"
                                        @click="editBtnHandler(scope.row)"></el-button>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="删除" placement="top" v-if="isSupper || deleteButton">
                                    <el-button type="text" size="medium" icon="el-icon-delete-solid"
                                        @click="delWareBtn(scope.row.id)"></el-button>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="授权" placement="top" v-if="isSupper || empowerButton">
                                    <el-button type="text" size="medium" icon="el-icon-user"
                                        @click="empowerBtn(scope.row.id)"></el-button>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="消息" placement="top" v-if="isSupper || editButton">
                                    <el-button type="text" size="medium" icon="el-icon-chat-dot-round"
                                        @click="msgTypeHandler(scope.row)"></el-button>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
                <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
                    @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />

            </div>
        </el-tabs>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import { CustomerListState, ApiCustomerList, CustomerTagList, CustomerRank } from './customerListType';
import { addRoleInfo, pageList, deleteRoleInfo, editRoleInfo, addAuthRoleMenu, getMenuIds, getMessageTypeByRoleId, authRoleMessage } from "@/api/roleInfo/roleInfo";
import { getList } from "@/api/menuInfo/menuInfo";
import PageManage from '@/components/PageManage.vue';
@Component({
    components: {
        PageManage
    }
})
export default class roleInfo extends Vue implements CustomerListState {
    // 数据表单  组成数据用于向后端提交数据
    dataForm = {
        roleName: '',
        roleCode: '',
        msgType: []
    };
    //角色新增弹出框
    addRoleFlag = false;
    msgTypeFlag = false;
    //新增弹出框
    addForm = {
        roleName: '',
        roleCode: '',
        remark: '',
        msgType: []
    }
    //消息类型
    msgTypeForm = {
        roleId: '',
        msgType: [],
    }
    optionsType = [{
        value: 1,
        label: '订单消息'
    }, {
        value: 2,
        label: '售后消息'
    },
    //   {
    //     value: 3,
    //     label: '用户消息'
    // }, {
    //     value: 4,
    //     label: '营销活动'
    // },
      {
        value: 5,
        label: '代发货消息'
    }, {
        value: 6,
        label: '提现消息'
    }, {
        value: 7,
        label: '预约单消息'
    }]

    //编辑弹出框
    editForm = {
        id: '',
        roleName: '',
        roleCode: '',
        remark: '',
        msgType: []
    }

    /** 分页条数 */
    pageSize = 10;

    /** 分页页码 */
    pageNum = 1;

    /** 数据长度 */
    total = 0;

    // 角色数据
    roleList = [];
    //菜单数据
    dataList = [];
    //选中的菜单id
    menuIds = [];
    //角色id
    roleId = "";

    //角色编辑弹出框
    editRoleFlag = false;
    //角色授权弹出框
    empowerFlag = false;

    drawer = false;
    direction = 'rtl';

    defaultProps = {
        children: 'children',
        label: 'label'
    }
    chooseId = [];

    menuName = "角色管理";

    buttonList = [];

    isSupper = 0;

    addButtonCode = "roleInfo.add";
    addButton = false;

    deleteButtonCode = "roleInfo.delete";
    deleteButton = false;

    editButtonCode = "roleInfo.edit";
    editButton = false;

    empowerButtonCode = "roleInfo.empower";
    empowerButton = false;


    mounted() {
        this.searchQuery(1);
        this.getAllList();
        this.buttonAuth();
    }

    buttonAuth() {
        this.isSupper = this.$STORE.userStore.userInfo.isSupper
        let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

        let buttonList = [];

        authMenuButtonVos.forEach(element => {
            buttonList.push(element.buttonCode);
        });

        this.buttonList = buttonList

        var addButtonData = buttonList.find(e => e == this.addButtonCode);

        if (addButtonData != null && addButtonData != undefined) {
            this.addButton = true;
        }

        var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);

        if (deleteButtonData != null && deleteButtonData != undefined) {
            this.deleteButton = true;
        }

        var editButtonData = buttonList.find(e => e == this.editButtonCode);

        if (editButtonData != null && editButtonData != undefined) {
            this.editButton = true;
        }

        var empowerButtonData = buttonList.find(e => e == this.empowerButtonCode);

        if (empowerButtonData != null && empowerButtonData != undefined) {
            this.empowerButton = true;
        }
    }
    getAllList() {
        getList({}).then(res => {
            const list = res.data;
            let dataList = [];
            list.forEach((item, index) => {
                let data = {};
                data.id = item.id;
                data.label = item.menuName
                data.parentId = item.menuPid
                data.type = 0;
                let authMenuInfoSecondVos = item.authMenuInfoSecondVos;
                if (authMenuInfoSecondVos.length > 0) {
                    let children = [];
                    authMenuInfoSecondVos.forEach(i => {
                        let childData = {};
                        childData.id = i.id;
                        childData.label = i.menuName
                        childData.parentId = i.menuPid
                        childData.type = 0;
                        let authMenuButtonVos = i.authMenuButtonVos;
                        if (authMenuButtonVos.length > 0) {
                            let buttonList = [];
                            authMenuButtonVos.forEach(k => {
                                let button = {};
                                button.id = k.id;
                                button.label = k.buttonName;
                                button.parentId = k.menuId;
                                button.type = 1;
                                buttonList.push(button);
                            });
                            childData.children = buttonList
                        }
                        children.push(childData);
                    });
                    data.children = children
                }

                dataList.push(data);
            });
            console.log(this.dataList);
            this.dataList = dataList;
        });
    }
    //获取用户列表（数据）
    searchQuery(pageNum: number) {
        // console.log('执行了');
        const form = this.dataForm;
        const param = {
            current: pageNum,
            size: this.pageSize,
            ...form
        }
        // 调用接口
        pageList(param).then(res => {
            // 仓库数据
            this.roleList = res.data.list
            // 显示数据条数
            this.pageSize = res.data.size;
            // 第几页
            this.pageNum = res.data.current;

            this.total = res.data.total;
        })
    }
    //用户新增的提交方法
    addHandler() {
        console.log("ress=", this.addForm.msgType)
        addRoleInfo(this.addForm).then(res => {
            this.$message.success("新增成功");
            // 关闭模态框
            this.addRoleFlag = false;
            //重置弹出框的数据为空
            this.addForm = {
                roleName: '',
                roleCode: '',
                remark: ''
            }
            // 提交数据成功，重新获取一次数据进行渲染
            this.searchQuery(1);
        }).catch(err => {
            this.$message.error(err || "网络错误");
        });
    }
    /**
 * @method handleSizeChange
 * @description 每页 条
 */
    handleSizeChange(val: number) {
        this.pageSize = val;
        this.searchQuery();
    }
    /**
 * @method handleCurrentChange
 * @description 当前页
 */
    handleCurrentChange(val: number) {
        this.pageNum = val;
        this.searchQuery(val);
    }
    delWareBtn(id: string) {
        this.$confirm("确定要删除选中角色吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
        }).then(() => {
            let param = { id: id };
            deleteRoleInfo(param)
                .then(res => {
                    if (res.code === 200) {
                        this.$message.success("删除成功");
                        this.searchQuery(1);
                    }
                })
                .catch(err => {
                    this.$message.error(err);
                });
        });
    }

    //获取当前行需要修改的信息
    editBtnHandler(val: any) {
        this.editRoleFlag = true;
        this.editForm = { ...val };
    }
    //编辑角色
    editHandler() {
        editRoleInfo(this.editForm).then(res => {
            this.$message.success("编辑成功");
            // 关闭模态框
            this.editRoleFlag = false;
            //重置弹出框的数据为空
            this.editForm = {
                id: '',
                roleName: '',
                roleCode: '',
                remark: ''
            }
            // 提交数据成功，重新获取一次数据进行渲染
            this.searchQuery(1);
        }).catch(err => {
            this.$message.error(err || "网络错误");
        });
    }
    handleClose() {
        this.empowerFlag = false
        this.$refs.tree.setCheckedKeys([]);
    }
    empowerBtn(id: string) {
        this.chooseId = []
        // 调用接口
        getMenuIds(id, {}).then(res => {

            this.roleId = id;
            this.empowerFlag = true;
            this.chooseId = res.data
        })
    }
    msgTypeHandler(row: any) {
        getMessageTypeByRoleId({ roleId: row.id }).then(res => {
            this.msgTypeFlag = true;
            // this.msgTypeForm.msgType = ['5','1'];
            this.msgTypeForm.msgType = res.data;
            this.msgTypeForm.roleId = row.id;
        }).catch(err => {
            this.$message.error(err)
        })
    }
    msgTypeSure() {
        const param = {
            "roleId": this.msgTypeForm.roleId,
            "messageTypes": this.msgTypeForm.msgType
        }

        authRoleMessage(param).then(res => {
            this.msgTypeFlag = false;
            this.$message.success('授权成功')
        }).catch(err => {
            this.$message.error(err)
        })
    }
    empowerHandler() {
        let menuParent = [];
        let menus = [];
        let buttonList = [];
        let data = this.$refs.tree.getCheckedNodes(false, true);
        data.forEach(element => {
            if (element.parentId == 0) {
                let menu = {};
                menu.id = element.id;
                menu.parentId = element.parentId;
                menu.type = element.type;
                menuParent.push(menu);
            } else {
                if (element.type == 0) {
                    let menu = {};
                    menu.id = element.id;
                    menu.parentId = element.parentId;
                    menu.type = element.type;
                    menus.push(menu);
                } else {
                    let button = {};
                    button.id = element.id;
                    button.parentId = element.parentId;
                    button.type = element.type;
                    buttonList.push(button);
                }

            }
        });
        if (menuParent != null && menuParent.length > 0) {
            let param = {};
            param.roleId = this.roleId;
            param.menuParent = menuParent;
            param.menu = menus;
            param.button = buttonList;
            addAuthRoleMenu(param)
                .then(res => {
                    if (res.code === 200) {
                        this.empowerFlag = false;
                        this.roleId = "";
                        this.$message.success("授权成功");
                        this.$refs.tree.setCheckedKeys([]);
                        this.searchQuery(1);
                    }
                })
                .catch(err => {
                    this.$message.error(err);
                });
        } else {
            this.$message.error("请选择菜单！");
        }

    }
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.xin-main {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.xin-content {
    overflow-y: scroll;
    box-sizing: border-box;
    overflow-x: hidden;
}

.footer {
    height: var(--footer-height);
}

.footer-content {
    position: fixed;
    bottom: 0;
    width: 100%;
    line-height: var(--footer-height);
    background: #fff;
    color: #fff;
}
</style>