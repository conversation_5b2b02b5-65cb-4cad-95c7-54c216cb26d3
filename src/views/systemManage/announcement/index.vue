<!--
 * 系统公告管理页面
 -->
<template>
  <div class="announcement">
    <el-tabs>
      <Search ref="searchRef" :value="query" @search="getList" />
      <div style="margin-bottom: 20px"></div>
      
      <div class="operation-bar">
        <el-button 
          type="primary" 
          icon="el-icon-plus" 
          @click="handleAdd"
          v-if="hasPermission('announcement.add')"
        >
          新增公告
        </el-button>
      </div>

      <AnnouncementList
        :data="dataList"
        :loading="listLoading"
        @view-detail="handleViewDetail"
        @edit="handleEdit"
        @delete="handleDelete"
      />

      <PageManage
        :pageSize="query.size"
        :pageNum="query.current"
        :total="query.total"
        class="PageManage"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handlePageChange"
      />

      <!-- 新增/编辑/查看详情对话框 -->
      <AnnouncementFormComponent
        :visible.sync="dialogVisible"
        :editData="editData"
        :detailData="detailData"
        @submit="handleSubmit"
        @close="handleDialogClose"
      />
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import Search from "./components/Search.vue";
import AnnouncementList from "./components/AnnouncementList.vue";
import AnnouncementFormComponent from "./components/AnnouncementForm.vue";
import PageManage from '@/components/PageManage.vue';
import { 
  getAnnouncementList, 
  addAnnouncement, 
  updateAnnouncement, 
  deleteAnnouncement
} from "@/api/announcement/announcement";
import { AnnouncementItem, AnnouncementForm } from "./types";

@Component({
  components: {
    PageManage,
    Search,
    AnnouncementList,
    AnnouncementFormComponent,
  },
})
export default class Announcement extends Vue {
  // 查询参数
  query: any = {
    size: 10,
    current: 1,
    total: 0,
    title: "",
    startDate: "",
    endDate: ""
  };

  dataList: AnnouncementItem[] = [];
  listLoading = false;

  // 对话框相关
  dialogVisible = false;
  editData: AnnouncementItem | null = null;
  detailData: AnnouncementItem | null = null;

  created() {
    this.getList();
  }

  // 获取Search组件的form
  getSearchForm() {
    const ref: any = this.$refs.searchRef;
    return ref && ref.form ? ref.form : { ...this.query };
  }

  // 获取公告列表
  async getList() {
    this.listLoading = true;
    this.query = this.getSearchForm();
    try {
      const res = await getAnnouncementList(this.query);
      if (res && res.data) {
        this.dataList = res.data.list || [];
        this.query.total = res.data.total || 0;
        this.query.current = res.data.current || 1;
        this.query.size = res.data.size || 10;
      }
    } catch (error) {
      this.dataList = [];
      this.query.total = 0;
      this.$message.error('获取公告列表失败');
    } finally {
      this.listLoading = false;
    }
  }

  // 新增公告
  handleAdd() {
    this.editData = null;
    this.detailData = null;
    this.dialogVisible = true;
  }

  // 编辑公告
  handleEdit(item: AnnouncementItem) {
    this.editData = item;
    this.detailData = null;
    this.dialogVisible = true;
  }

  // 查看详情
  handleViewDetail(item: AnnouncementItem) {
    this.detailData = item;
    this.editData = null;
    this.dialogVisible = true;
  }

  // 删除公告
  async handleDelete(item: AnnouncementItem) {
    try {
      await deleteAnnouncement({ id: item.id });
      this.$message.success('删除成功');
      this.getList();
    } catch (error) {
      this.$message.error('删除失败');
    }
  }

  // 提交表单
  async handleSubmit(form: AnnouncementForm) {
    try {
      if (form.id) {
        // 编辑
        await updateAnnouncement(form);
        this.$message.success('更新成功');
      } else {
        // 新增
        await addAnnouncement(form);
        this.$message.success('新增成功');
      }
      this.dialogVisible = false;
      this.getList();
    } catch (error) {
      this.$message.error(form.id ? '更新失败' : '新增失败');
    }
  }

  // 关闭对话框
  handleDialogClose() {

    this.editData = null;
    this.dialogVisible = false;
  }

  // 分页相关
  handlePageChange(page: number) {
    this.query.current = page;
    this.getList();
  }

  handleSizeChange(size: number) {
    this.query.size = size;
    this.query.current = 1;
    this.getList();
  }

  // 权限检查（可以根据实际权限系统调整）
  hasPermission(permission: string): boolean {
    // 这里应该根据实际的权限系统来判断
    return true;
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';
.announcement {
  .operation-bar {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .PageManage {
    margin-top: 20px;
  }
}
</style> 