/**
 * 公告状态枚举
 */
export enum AnnouncementStatus {
  DRAFT = 0,           // 草稿
  PENDING = 100,       // 待审核
  APPROVED = 101,      // 审核通过
  REJECTED = 200,      // 审核不通过
  EXPIRED = 300,       // 失效
  STOPPED = 400        // 停止
}

/**
 * 公告状态映射
 */
export const STATUS_MAP: Record<number, { label: string; color: string }> = {
  [AnnouncementStatus.DRAFT]: { label: '草稿', color: 'info' },
  [AnnouncementStatus.PENDING]: { label: '待审核', color: 'warning' },
  [AnnouncementStatus.APPROVED]: { label: '审核通过', color: 'success' },
  [AnnouncementStatus.REJECTED]: { label: '审核不通过', color: 'danger' },
  [AnnouncementStatus.EXPIRED]: { label: '失效', color: 'info' },
  [AnnouncementStatus.STOPPED]: { label: '停止', color: 'danger' }
};

/**
 * 公告查询参数接口
 */
export interface AnnouncementQueryParams {
  id?: number;
  title?: string;
  startDate?: string;
  endDate?: string;
  current?: number;
  size?: number;
}





/**
 * 公告数据
 */
export interface AnnouncementItem {
  id: number;
  title: string;
  content: string;
  effectiveStartDate: string;
  effectiveEndDate: string;
  createTime: string;
  updateTime: string;
}

/**
 * 新增/编辑公告表单
 */
export interface AnnouncementForm {
  id?: number;
  title: string;
  content: string;
  effectiveStartDate: string;
  effectiveEndDate: string;
} 