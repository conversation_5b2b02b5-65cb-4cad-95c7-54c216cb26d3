<!--
 * 系统公告新增/编辑/查看详情对话框组件
 -->
<template>
  <el-dialog 
    :title="dialogTitle" 
    :visible.sync="visible" 
    width="60%" 
    @close="handleClose"
  >
  
    <el-form :model="form" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="公告标题">
            <el-input 
              v-model="form.title" 
              placeholder="请输入公告标题" 
              maxlength="100" 
              show-word-limit
              :disabled="isView"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="生效开始日期">
            <el-date-picker
              v-model="form.effectiveStartDate"
              type="date"
              placeholder="选择生效开始日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              :disabled="isView"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效结束日期">
            <el-date-picker
              v-model="form.effectiveEndDate"
              type="date"
              placeholder="选择生效结束日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              :disabled="isView"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="isView">
        <el-col :span="12">
          <el-form-item label="创建时间">
            <el-input v-model="form.createTime" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="更新时间">
            <el-input v-model="form.updateTime" disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="公告内容">
            <RichEditor 
              v-if="!isView"
              :text="form.content" 
              ref="richEditor" 
            />
            <div v-if="isView" v-html="form.content" class="content-preview"></div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
      <el-button v-if="!isView" type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop, Ref } from "vue-property-decorator";
import { AnnouncementForm, AnnouncementItem } from "../types";
import RichEditor from "@/components/RichEditor.vue";

@Component({
  components: {
    RichEditor
  }
})
export default class AnnouncementFormComponent extends Vue {
  @Prop({ type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: Object, default: () => null }) editData!: AnnouncementItem | null;
  @Prop({ type: Object, default: () => null }) detailData!: AnnouncementItem | null;
  @Ref() richEditor!: any;

  submitLoading = false;

  get isEdit(): boolean {
    return !!this.editData && !this.detailData;
  }

  get isView(): boolean {
    return !!this.detailData;
  }

  get dialogTitle(): string {
    if (this.isView) return '公告详情';
    return this.isEdit ? '编辑公告' : '新增公告';
  }

  get form(): AnnouncementForm & { createTime?: string; updateTime?: string } {
    if (this.isView && this.detailData) {
      // 查看详情
      return {
        id: this.detailData.id,
        title: this.detailData.title || "",
        content: this.detailData.content || "",
        effectiveStartDate: this.detailData.effectiveStartDate || "",
        effectiveEndDate: this.detailData.effectiveEndDate || "",
        createTime: this.formatDateTime(this.detailData.createTime),
        updateTime: this.formatDateTime(this.detailData.updateTime)
      };
    } else if (this.isEdit && this.editData) {
      // 编辑
      return {
        id: this.editData.id,
        title: this.editData.title || "",
        content: this.editData.content || "",
        effectiveStartDate: this.editData.effectiveStartDate || "",
        effectiveEndDate: this.editData.effectiveEndDate || ""
      };
    } else {
      // 新增
      return {
        title: "",
        content: "",
        effectiveStartDate: "",
        effectiveEndDate: ""
      };
    }
  }

  async handleSubmit() {
    try {
      const currentForm = this.form;
      
      // 验证时间
      if (new Date(currentForm.effectiveStartDate) >= new Date(currentForm.effectiveEndDate)) {
        this.$message.error('生效结束时间必须大于生效开始时间');
        return;
      }
      
      // 获取富文本编辑器内容
      if (this.richEditor) {
        currentForm.content = this.richEditor.getHtml();
      }
      
      // 验证必填字段
      if (!currentForm.title.trim()) {
        this.$message.error('请输入公告标题');
        return;
      }
      if (!currentForm.effectiveStartDate) {
        this.$message.error('请选择生效开始时间');
        return;
      }
      if (!currentForm.effectiveEndDate) {
        this.$message.error('请选择生效结束时间');
        return;
      }
      if (!currentForm.content.trim()) {
        this.$message.error('请输入公告内容');
        return;
      }

      this.submitLoading = true;
      this.$emit('submit', currentForm);
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      this.submitLoading = false;
    }
  }

  handleClose() {
    this.$emit('close');
    this.$emit('update:visible', false);
  }

  formatDateTime(dateTime: string): string {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleDateString('zh-CN');
  }
}
</script>

<style lang="scss" scoped>
.content-preview {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  min-height: 200px;
  background-color: #fafafa;
}
</style> 