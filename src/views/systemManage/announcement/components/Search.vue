<!--
 * 系统公告搜索组件
 -->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="90px">
      <el-row>
        <el-col :span="10">
          <el-form-item label="公告标题">
            <el-input v-model="form.title" placeholder="请输入公告标题" ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="生效时间">
            <el-date-picker 
              v-model="form.dateRange" 
              type="daterange" 
              range-separator="-" 
              start-placeholder="开始时间" 
              end-placeholder="结束时间" 
              value-format="yyyy-MM-dd"
              style="width: 100%;" 
              @change="chooseTimes" 
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </m-card>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";

@Component
export default class Search extends Vue {
  @Prop({ default: () => ({}) }) value!: any;

  form: any = {
    title: "",
    dateRange: [],
    startDate: "",
    endDate: "",
    size: 10,
    current: 1,
    total: 0,
  };

  mounted() {
    this.syncFromValue();
  }

  @Watch('value', { immediate: true, deep: true })
  onValueChange() {
    this.syncFromValue();
  }

  syncFromValue() {
    if (this.value) {
      Object.keys(this.form).forEach(key => {
        if (this.value[key] !== undefined) {
          this.form[key] = this.value[key];
        }
      });
    }
  }

  handleFieldChange() {
    this.$emit('search');
  }

  chooseTimes() {
    if (this.form.dateRange && this.form.dateRange.length === 2) {
      this.form.startDate = this.form.dateRange[0];
      this.form.endDate = this.form.dateRange[1];
    } else {
      this.form.startDate = "";
      this.form.endDate = "";
    }
    this.handleFieldChange();
  }

  handleSearch() {
    this.$emit('search');
  }

  handleReset() {
    this.form = {
      title: "",
      dateRange: [],
      startDate: "",
      endDate: "",
      size: 10,
      current: 1,
      total: 0,
    };
    this.$emit('search');
  }
}
</script>

<style lang="scss" scoped>
.form {
  margin-bottom: 20px;
}
</style> 