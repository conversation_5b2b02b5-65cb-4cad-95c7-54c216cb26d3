<!--
 * 系统公告列表组件
 -->
<template>
  <div>
    <el-table :data="data" style="width: 100%" v-loading="loading">
      <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
      <el-table-column prop="title" label="公告标题" min-width="250" align="left">
        <template slot-scope="scope">
          <el-button type="text" @click="viewDetail(scope.row)">
            {{ scope.row.title }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="effectiveStartDate" label="有效期" width="250" align="center">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.effectiveStartDate) }} - {{ formatDateTime(scope.row.effectiveEndDate) }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="effectiveEndDate" label="生效结束时间" width="180" align="center">
        <template slot-scope="scope">
         
        </template>
      </el-table-column>
   -->
      <el-table-column label="操作" width="150" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="editAnnouncement(scope.row)">
            编辑
          </el-button>
          <el-button type="text" @click="deleteAnnouncement(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { AnnouncementItem } from "../types";

@Component
export default class AnnouncementList extends Vue {
  @Prop({ type: Array, default: () => [] }) data!: AnnouncementItem[];
  @Prop({ type: Boolean, default: false }) loading!: boolean;

  formatDateTime(dateTime: string): string {
    if (!dateTime) return '-';
    return new Date(dateTime).toLocaleDateString('zh-CN');
  }

  viewDetail(item: AnnouncementItem) {
    this.$emit('view-detail', item);
  }

  editAnnouncement(item: AnnouncementItem) {
    this.$emit('edit', item);
  }

  deleteAnnouncement(item: AnnouncementItem) {
    this.$confirm('确定要删除这条公告吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      this.$emit('delete', item);
    }).catch(() => {});
  }
}
</script>

<style lang="scss" scoped>
.announcement-list {
  .el-button--text {
    padding: 0 5px;
  }
}
</style> 