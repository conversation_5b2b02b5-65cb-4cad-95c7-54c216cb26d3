<!--
 * SSE连接测试页面 - 简化版本
 -->
<template>
  <div class="sse-test">
    <el-card class="connection-card">
      <div slot="header">
        <span>SSE事件监听</span>
      </div>
      
      <!-- 连接控制区域 -->
      <div class="connection-controls">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="clientId"
              placeholder="请输入客户端ID"
              clearable
            >
              <template slot="prepend">客户端ID</template>
            </el-input>
          </el-col>
          <el-col :span="8">
            <el-input
              v-model="sseUrl"
              placeholder="SSE连接地址"
              clearable
            >
              <template slot="prepend">连接地址</template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-button
              type="primary"
              :loading="connecting"
              @click="connectSSE"
              :disabled="!clientId || !sseUrl"
            >
              {{ isConnected ? '关闭连接' : '连接SSE' }}
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button
              type="danger"
              @click="closeSSE"
              :disabled="!isConnected"
            >
              关闭连接
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 连接状态 -->
      <div class="connection-status">
        <el-alert
          :title="connectionStatusText"
          :type="connectionStatusType"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 事件显示区域 -->
      <div class="events-display">
        <el-divider content-position="left">
          SSE事件日志
          <el-button
            size="mini"
            type="text"
            @click="clearEvents"
            style="margin-left: 10px;"
          >
            清空日志
          </el-button>
        </el-divider>
        
        <div class="events-list">
          <div
            v-for="(event, index) in events"
            :key="index"
            class="event-item"
            :class="event.type"
          >
            <div class="event-time">{{ event.time }}</div>
            <div class="event-message">{{ event.message }}</div>
            <div v-if="event.data" class="event-data">
              <pre>{{ JSON.stringify(event.data, null, 2) }}</pre>
            </div>
          </div>
          
          <div v-if="events.length === 0" class="no-events">
            <el-empty description="暂无事件日志" />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";

interface SSEEvent {
  time: string;
  type: 'info' | 'success' | 'error' | 'warning';
  message: string;
  data?: any;
}

@Component
export default class SSETest extends Vue {
  // 连接相关
  clientId = 'client_' + Math.random().toString(36).substr(2, 8);
  sseUrl = '/order-open/mini/sse/connect';
  isConnected = false;
  connecting = false;
  eventSource: EventSource | null = null;

  // 事件相关
  events: SSEEvent[] = [];

  created() {
    // 设置默认连接地址
    const baseUrl = process.env.VUE_APP_BASEURL || '';
    this.sseUrl = baseUrl + '/order-open/sse/connect';
  }

  // 连接SSE
  connectSSE() {
    if (this.isConnected) {
      this.closeSSE();
      return;
    }

    this.connecting = true;
    
    try {
      // 获取token
      const token = this.$STORE.userStore.token || localStorage.getItem('token');
      const url = token 
        ? `${this.sseUrl}?clientId=${this.clientId}&token=${token}`
        : `${this.sseUrl}?clientId=${this.clientId}`;
      
      this.eventSource = new EventSource(url);
      
      this.eventSource.onopen = (e) => {
        this.isConnected = true;
        this.connecting = false;
        this.logEvent('连接已建立', 'success', e);
      };
      
      // 通用事件处理器
      this.eventSource.onmessage = (e) => {
        this.logEvent('收到消息: ' + e.data, 'success', e);
      };
      
      // 特定事件处理器
      this.eventSource.addEventListener('order-pay', (e) => {
        this.logEvent('收到订单支付事件: ' + e.data, 'success', e);
      });
      
      this.eventSource.addEventListener('order-create', (e) => {
        this.logEvent('收到订单创建事件: ' + e.data, 'success', e);
      });
      
      this.eventSource.addEventListener('customEvent', (e) => {
        this.logEvent('收到自定义事件: ' + e.data, 'info', e);
      });
      
      this.eventSource.onerror = (e) => {
        this.logEvent('连接错误', 'error', e);
        if (e.eventPhase === EventSource.CLOSED) {
          this.logEvent('连接已关闭', 'warning');
        }
        this.isConnected = false;
        this.connecting = false;
      };
      
    } catch (error: any) {
      this.connecting = false;
      this.logEvent('连接失败: ' + error.toString(), 'error');
    }
  }

  // 关闭SSE连接
  closeSSE() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
    this.logEvent('主动关闭连接', 'info');
  }

  // 记录事件
  logEvent(message: string, type: SSEEvent['type'] = 'info', event?: Event) {
    const eventData = event ? {
      eventPhase: event.eventPhase,
      type: event.type,
      target: event.target ? 'EventSource' : undefined
    } : undefined;

    this.events.unshift({
      time: new Date().toLocaleTimeString(),
      type,
      message,
      data: eventData
    });

    // 限制事件数量，最多显示50条
    if (this.events.length > 50) {
      this.events = this.events.slice(0, 50);
    }
  }

  // 清空事件
  clearEvents() {
    this.$confirm('确定要清空所有事件日志吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      this.events = [];
      this.$message.success('事件日志已清空');
    }).catch(() => {
      // 取消操作
    });
  }

  // 计算连接状态文本
  get connectionStatusText(): string {
    if (this.connecting) {
      return '正在连接SSE...';
    }
    if (this.isConnected) {
      return 'SSE连接已建立，正在监听事件';
    }
    return 'SSE连接未建立，请点击"连接SSE"开始监听';
  }

  // 计算连接状态类型
  get connectionStatusType(): 'info' | 'success' | 'error' | 'warning' {
    if (this.connecting) {
      return 'info';
    }
    if (this.isConnected) {
      return 'success';
    }
    return 'warning';
  }

  // 组件销毁时关闭连接
  beforeDestroy() {
    this.closeSSE();
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.sse-test {
  padding: 20px;

  .connection-card {
    .connection-controls {
      margin-bottom: 20px;
    }

    .connection-status {
      margin-bottom: 20px;
    }

    .events-display {
      margin-bottom: 20px;

      .events-list {
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 10px;

        .event-item {
          margin-bottom: 15px;
          padding: 10px;
          border-radius: 4px;
          border-left: 4px solid;
          background-color: #fafafa;

          &.info {
            border-left-color: #409eff;
            background-color: #f0f9ff;
          }

          &.success {
            border-left-color: #67c23a;
            background-color: #f0f9ff;
          }

          &.warning {
            border-left-color: #e6a23c;
            background-color: #fdf6ec;
          }

          &.error {
            border-left-color: #f56c6c;
            background-color: #fef0f0;
          }

          .event-time {
            font-size: 12px;
            color: #909399;
            margin-bottom: 5px;
          }

          .event-message {
            font-size: 14px;
            color: #303133;
            margin-bottom: 5px;
          }

          .event-data {
            pre {
              margin: 5px 0 0 0;
              font-size: 12px;
              line-height: 1.4;
              white-space: pre-wrap;
              word-break: break-all;
              background-color: #f5f7fa;
              padding: 5px;
              border-radius: 3px;
              border: 1px solid #e4e7ed;
            }
          }
        }

        .no-events {
          text-align: center;
          padding: 40px 0;
        }
      }
    }
  }
}
</style> 