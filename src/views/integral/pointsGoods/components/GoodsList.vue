<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
-->
<template>
    <!-- 商品列表 -->
    <div style="margin-top: 20px">
      <m-table
        :data.sync="goodList"
        :selection="true"
        :checked-item.sync="tableCheckedItem"
        slot="content"
        class="imgView"


      >
        <m-table-column
          prop="activityName"
          label="方案名称"
          :showsSlection="true"


        >
          <template v-slot="{ row }">
            <div class="goodList">
                {{ row.activityName }}
            </div>
          </template>
        </m-table-column>
        <m-table-column prop="userName" label="创建时间">
          <template v-slot="{ row }">
            <span>{{ row.createTime }}</span>
          </template>
        </m-table-column>
        <m-table-column prop="startTime" label="开始时间">
          <template v-slot="{ row }">
            <span>{{ row.startTime }}</span>
          </template>
        </m-table-column>
        <m-table-column prop="endTime" label="结束时间">
          <template v-slot="{ row }">
            <span>{{ row.endTime }}</span>
          </template>
        </m-table-column>
        <m-table-column prop="projectStatus" label="方案状态">
          <template v-slot="{ row }">
            <!-- 方案状态：0.待发布；1.进行中；2.已完成；3.已停用 -->
            <span v-if="row.projectStatus==0">待发布</span>
            <span v-else-if="row.projectStatus==1" style="color: #23c910;">进行中</span>
            <span v-else-if="row.projectStatus==2" style="color: #e80bdc;">已完成</span>
            <span v-else-if="row.projectStatus==3" style="color: #ff0000;">已停用</span>
            <!-- <span v-else-if="row.projectStatus==5">进行中</span> -->
          </template>
        </m-table-column>
        <m-table-column prop="userName" label="操作">
          <template v-slot="{ row }">
            <div class="center">
              <set-drop v-if="isSupper||editButton"
                setName="编辑"
                :dropdownList="itemDropList(row)"
                @setClick="edit(row)"
                @command="getDropdown($event, row)"
              />

              <set-drop v-else
                setName=""
                :dropdownList="itemDropList(row)"
                @command="getDropdown($event, row)"
              />
            </div>
          </template>
        </m-table-column>
      </m-table>
    </div>
  </template>

  <script lang="ts">
  import { Vue, Component, Watch, Prop } from "vue-property-decorator";
  import SetDrop from "@/views/customer/common/SetDrop.vue";
  import { GoodListState } from "./goodListType";
  import { SearchKeyType } from "./searchType";
  import {pageIntegralActivity,copyIntegral,deactivateIntegral} from "@/api/integralApi/integralApi"
  // import { Loading } from "element-ui";
  import {GoodDetailInfo } from "../goodType";
  import { ApiSpecArea } from "../marketType";

  @Component({
    components: {
      SetDrop
    }
  })
  export default class GoodsList extends Vue implements GoodListState {
    name="GoodsList"
    @Prop({})
    changeId!: string;

    @Watch("changeId")
    getSaleMode() {
        this.searchType.projectStatus = this.changeId;
        console.log('bbbbbbbb', this.searchType, this.changeId);
        this.searchType = {
      current: 1,
      size: 10
    }
    this.inif()

        // this.getProduct();
    }
    // @Watch("goodIds")
    // onGoodIdsChanged(val: number[]) {
    //   this.$emit("goodId", val);
    // }

    saleMode = "";

    goodList: Array<GoodDetailInfo> = [];

    hasList = false;

    cateFlag = false;

    loading = false;

    checkAll = false;

    isIndeterminate = false;

    goodIds: Array<number> = [];

    tableCheckedItem: Array<GoodDetailInfo> = [];



    currentGood: GoodDetailInfo | null = null;

    get itemDropList() {
      return (row: GoodDetailInfo) => {
        return [
        {
            text: "复制",
            command: "copy",
            show: row.status != 5&&(this.isSupper||this.copyButton),
            disabled: false
          },
        {
            text: "明细",
            command: "detail",
            show: row.status != 5,
            disabled: false
          },
          {
            text: "停用",
            command: "deactivate",
            show: row.projectStatus!=3&&(this.isSupper||this.stopButton),
            disabled: false
          }
        ];
      };
    }

    total = 0;

    fold = true;

    skuDisplay = true;

    meberLive = [];

    searchType = {
      current: 1,
      size: 10
    } as SearchKeyType;

    radio = 0;

    menuName = "积分商品";	
    buttonList = [];
    isSupper = 0;

    editButtonCode = "integralGoods.edit";
    editButton = false;

    copyButtonCode = "integralGoods.copy";
    copyButton = false;

    stopButtonCode = "integralGoods.stop";
    stopButton = false;

    mounted() {
      this.inif()

    }
    inif(){
      console.log("进入到goodlist");
      this.searchType.projectStatus = this.changeId;

      //  加载搜索缓存
      var cache = JSON.parse(
        localStorage.getItem("cache_integral_search_form") || "{}"
      );
      console.log('获取商品列表查询参数121', this.searchType,cache);
      this.searchType = Object.assign(this.searchType,cache) as SearchKeyType;

      this.getProduct();

      this.buttonAuth();
    }

    buttonAuth() {
  
      this.isSupper = this.$STORE.userStore.userInfo.isSupper
      let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter((i:any)=> i.menuName == this.menuName)

      let buttonList = [] as any;

      authMenuButtonVos.forEach((element:any) => {
        buttonList.push(element.buttonCode);
      });

      this.buttonList = buttonList

      var editButtonData = buttonList.find((e:any) => e == this.editButtonCode);

      if (editButtonData != null && editButtonData != undefined) {
        this.editButton = true;
      }

      var copyButtonData = buttonList.find((e:any) => e == this.copyButtonCode);

      if (copyButtonData != null && copyButtonData != undefined) {
        this.copyButton = true;
      }

      var stopButtonData = buttonList.find((e:any) => e == this.stopButtonCode);
      if (stopButtonData != null && stopButtonData != undefined) {
        this.stopButton = true;
      }

    }


  /**
     * 获取商品列表查询参数
     */
     getProListParams() {
       // 删除请求链接里面的空值
       for (const key in this.searchType) {
            console.log(',,,,',key, this.searchType);
            if (!this.searchType[key]) {
                this.$delete(this.searchType, key)
            }
        }
      // this.searchType.saleMode = this.saleMode;
      return this.searchType;
    }
    /**
     * 获取商品列表
     */
    async getProduct() {
      const param = this.getProListParams();
      this.loading = true;
      try {
        const res = await pageIntegralActivity(param);
        const goodList = res.data.list;
        this.total = res.data.total;
        console.log('res获取商品列表',res);
        this.goodList = goodList;
        // this.checkAll = false;
      } catch (error) {
        console.log(error);
      }
      this.loading = false;
      this.$emit("getShowProList", this.goodList);
    }
    /**
     * 跳转到积分商品明细列表
     */
    pointsTab(row:any){
      console.log('跳转到积分商品明细列表',row);
      this.$router.push({
        name: "PointsGoodsDetailsList",
        query: {
          id: row.id
        },
        params: {
          id: row.id
        }
      });

    }


    /**
     * 获取下拉框
     */
    getDropdown(val: string | number, row: GoodDetailInfo) {
      // if (Number(val) > 9) {
        console.log('获取下拉框',val,',',row);
        if(val=='copy'){
           this.getCopyIntegral(row.id)
        }
        if(val=='deactivate'){
          this.getDeactivateIntegral(row.id)
        }
        if(val=='detail'){
          this.pointsTab(row)
        }

    }
    /**
 * 复制积分方案
 * @param data
 */
 getCopyIntegral(id:any){
  copyIntegral({"id":id}).then((res)=>{
    console.log('复制积分方案',res);
    this.$message('积分方案复制成功')
    this.getProduct()
  }).catch((err)=>{
    this.$message(err)
  })
 }
 /**停用积分方案 */
 getDeactivateIntegral(id:any){
  deactivateIntegral({"id":id}).then((res)=>{
    console.log('停用积分方案',res);
    this.$message('停用积分方案成功')
    this.getProduct()
  }).catch((err)=>{
    this.$message(err)
  })
 }

    /**
     * 编辑商品
     */
    edit(item: { id: string; saleMode: string }) {
      this.$router.push({
        name: "AddPointsGoods",
        query: {
          id: item.id,
          saleMode: item.saleMode
        },
        params: {
          id: item.id,
          saleMode: item.saleMode
        }
      });
    }
  }
  </script>

  <style lang="scss" scoped>

  .goodList {
    width: 250px;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-right: 20px;
    overflow: hidden;




  }

  .center {
    display: flex;
    justify-content: center;
  }

  .digTitle {
    font-size: 17px;
    font-weight: bold;
  }
  </style>
