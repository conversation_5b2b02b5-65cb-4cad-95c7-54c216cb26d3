<template>
	<div class="device_err">
		<!-- 设置积分规则页面 -->
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane v-for="(item, index) in list" :key="index" :label="item.modeName" :name="item.modeName"
				:id="item.id"></el-tab-pane>
		</el-tabs>

		<el-table class-name="el-table-lable-box" ref="multipleTable" :data="tableData"
			style="width: 100%;position: relative;" @selection-change="handleSelectionChange" @select="selectzhong" @select-all="selectzhongAll"
			:header-cell-style="{ backgroundColor: '#E6E6E6', color: '#101010', 'font-size': '16px', 'font-weight': '400' }">
			<el-table-column type="selection" width="45">
			</el-table-column>
			<el-table-column label="积分类型" width="170">
				<template slot-scope="scope">
					<span style="font-weight:400;color:#000;font-size: 16px;font-family: PingFangSC;"> {{ scope.row.ruleName
						}}</span>

				</template>
			</el-table-column>
			<el-table-column label="可获积分" width="420">
				<template slot-scope="scope">
					<div v-if="scope.row.ruleType == 3 || scope.row.ruleType == 4 || scope.row.ruleType == 6" class="integral">
						<el-input v-model="scope.row.integral" style="width:80px"></el-input>
						<div v-if="scope.row.ruleType == 6">积分=单</div>
						<div v-else>积分</div>
					</div>
					<div v-else-if="scope.row.ruleType == 2" class="integral" style="width:550px;">
						<div>一级</div> <el-input v-model="scope.row.parentIntegral" style="width:80px"></el-input>
						<div>积分</div>
						<div style="margin-left: 30px;">二级</div>
						<el-input v-model="scope.row.aboveParentIntegral" style="width:80px"></el-input>
						<div>积分</div>
					</div>

					<div v-else-if="scope.row.ruleType == 5 || scope.row.ruleType == 1" class="integral">
						<!-- <div v-if="scope.row.integral"> -->
						<el-input v-model="scope.row.integral" style="width:80px"></el-input>
						<div>积分</div>
						<div>=</div>
						<el-input v-model="scope.row.amount" style="width:80px"></el-input>
						<div>元</div>
					</div>
					<div v-else-if="scope.row.ruleType == 0" style="height: 400px;">
						<div class="integral richEditor" style="width:650px;">
							<RichEditor :text="descriptionName" ref="wEditor" style="width:540px;min-height: 50px;"
								onchange="changeValue"></RichEditor>
							<!-- <div style="background: red;" v-html="scope.row.description?scope.row.description:'暂无积分规则说明'">

							</div> -->
							
						</div>
					</div>


				</template>

			</el-table-column>
			<el-table-column prop="description" label="积分说明" >
				<template slot-scope="scope">
					<div v-if="scope.row.ruleType != 0" class="earnPoints" style="flex-wrap: wrap;"
						v-html="scope.row.description ? scope.row.description : '暂无积分规则说明'">

					</div>
					<div v-else class="yang earnPoints">
						
						填写积分规则说明内容
					
					</div>
				</template>
			</el-table-column>
		</el-table>
		<div style="margin-top: 60px;text-align:center">
			<el-button v-if="isSupper||saveButton" type="primary" @click="submit" round style="width:90px">提交</el-button>
		</div>
		<!-- <RichEditor :text="detailText" ref="wEditor" onchange="changeValue"></RichEditor> -->
		<!-- <div style="margin-top: 20px">
            <el-button @click="toggleSelection([tableData[1], tableData[2]])">切换第二、第三行的选中状态</el-button>
            <el-button @click="toggleSelection()">取消选择</el-button>
        </div> -->

	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from "vue-property-decorator";
// import pointsListApart from "./components/pointsDetalis/pointsListApart.vue";
import { ruleType } from "./ruleType";
import RichEditor from "@/components/RichEditor.vue";
import { getIntegralRule, setIntegralRule } from "@/api/integralApi/integralApi";

/**
 * 编辑商品信息
 */
@Component({
	components: {
		RichEditor
	},
})
export default class ruleSettings extends Vue {
	@Ref()
	readonly wEditor: HTMLFormElement;
	//   @Ref()
	//   readonly ListApart?: HTMLFormElement;
	detailText = "设置积分规则";
	activeName = '设置积分规则'
	chooseId: string | number = "108";
	list = [{ modeName: '设置积分规则', id: 108 }]
	tableData: Array<ruleType> = [
		{
			"ruleType": 1,
			"ruleName": "消费获积分",
			"integral": 0,
			"parentIntegral": null,
			"aboveParentIntegral": null,
			"amount": 0,
			"enableStatus": 0,
			"description": '积分对应的实付金额是多少，勾选生效。'
		},
		{
			"ruleType": 2,
			"ruleName": "发展下级",
			"integral": null,
			"parentIntegral": 0,
			"aboveParentIntegral": 0,
			"amount": null,
			"enableStatus": 0,
			"description": '下级分销每个可获积分数，勾选生效。'
		},
		{
			"ruleType": 3,
			"ruleName": "新用户注册获取积分",
			"integral": 0,
			"parentIntegral": null,
			"aboveParentIntegral": null,
			"amount": null,
			"enableStatus": 0,
			"description": '新用户注册可获积分数，勾选生效。'
		},
		{
			"ruleType": 4,
			"ruleName": "每天登录获取积分",
			"integral": 0,
			"parentIntegral": null,
			"aboveParentIntegral": null,
			"amount": null,
			"enableStatus": 0,
			"description": '每天登录可获积分数，勾选生效。'
		},
		{
			"ruleType": 5,
			"ruleName": "购买通惠证获取积分",
			"integral": 0,
			"parentIntegral": null,
			"aboveParentIntegral": null,
			"amount": 0,
			"enableStatus": 0,
			"description": '积分对应的实付金额是多少，勾选生效。'
		},
		{
			"ruleType": 6,
			"ruleName": "下级下单获积分",
			"integral": 0,
			"parentIntegral": null,
			"aboveParentIntegral": null,
			"amount": 0,
			"enableStatus": 0,
			"description": '下级所有客户在下单时，上级获得积分，勾选生效。'
		},
		{
			"ruleType": 0,
			"ruleName": "积分规则说明",
			"integral": null,
			"parentIntegral": null,
			"aboveParentIntegral": null,
			"amount": null,
			"enableStatus": 0,
			"description": '填写积分规则说明内容'
		}
	]
	multipleSelection = []
	descriptionName = ''

	menuName = "积分规则";

	buttonList = [];

	isSupper = 0;

	saveButtonCode = "integralRules.save";

	saveButton = false;




	/**
   * 顶部专区选择
   */
	handleClick(tab: { index: number }) {
		this.chooseId = this.list[tab.index].id || "";

	}
	mounted() {
		this.getPoritRule()
		// this.toggleSelection([this.tableData[2]])
		// this.toggleSelection()
		this.buttonAuth();

	}

	buttonAuth() {

		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var saveButtonData = buttonList.find(e => e == this.saveButtonCode);
		console.log(saveButtonData);
		if (saveButtonData != null && saveButtonData != undefined) {
			this.saveButton = true;
		}

	}
	changeValue(val: any) {
		console.log("sss", val);
	}
	/**获取积分规则*/
	async getPoritRule() {
		let res = await getIntegralRule({});
		console.log('wwwwwwwww', res.data);
		if (!res.data.length) {
			return
		}
		res.data.forEach((item) => {
			if (item.ruleType == 0) {
				this.descriptionName = item.description
			}
			if (item.ruleType == 1&&!item.description) {
				item.description = '积分对应的实付金额是多少，勾选生效。'
			}
			if (item.ruleType == 2&&!item.description) {
				item.description = '下级分销每个可获积分数，勾选生效。'
			}
			if (item.ruleType == 3&&!item.description) {
				item.description = '新用户注册可获积分数，勾选生效。'
			}
			if (item.ruleType == 4&&!item.description) {
				item.description = '每天登录可获积分数，勾选生效。'
			}
			if (item.ruleType == 5&&!item.description) {
				item.description = '积分对应的实付金额是多少，勾选生效。'
			}

		})
		this.tableData = res.data;
		this.toggleSelection()
	}
	toggleSelection() {
		this.$nextTick(() => {
			for (const item of this.tableData) {
				if (item.enableStatus == 1) {
					console.log('qqqqq', item);
					this.$refs.multipleTable.toggleRowSelection(item, true);
				} else {
					// this.$refs.multipleTable.clearSelection();
				}
			}
		})



	}
	handleSelectionChange(val) {
		this.multipleSelection = val;

	}
	selectzhong(selection, row: any) {
		console.log('222222', row);

		this.tableData.forEach((items: any) => {
			if (row.id == items.id) {
				if (row.enableStatus == 1) {
					items.enableStatus = 0;
				} else {
					items.enableStatus = 1;
					// console.log('222222', row.id);
				}

			}
		})
	}
	selectzhongAll(val){
		console.log('222222全选', this.tableData,val);
		if(val.length>=1){
            this.tableData.forEach((items: any) => {
				if (items.enableStatus != 1) {
					items.enableStatus = 1;
				}
		   })
		}else{
			this.tableData.forEach((items: any) => {
				if (items.enableStatus == 1) {
					items.enableStatus = 0;
				}
		   })

		}
	
	}
	getDetailHtml() {
		return this.wEditor.getHtml();
		// return (this.$refs.ue as any).getUEContent();
	}
	// 添加积分规则
	submit() {
		this.tableData.forEach((items: any) => {
			if (items.ruleType == 0) {
				items.description = this.getDetailHtml()
			}
		})
		console.log('添加积分规则', this.tableData, this.getDetailHtml());
		setIntegralRule(this.tableData).then((res) => {
			console.log('wwwwwwwww', res.data);
			let msg = res.msg as string
			this.$message.success('操作成功')
			// this.ruleForm=res.data;
		}).catch((err) => {
			this.$message(err)
		})
		// console.log('1111111122222222',this.tableData, this.multipleSelection);

	}

}
</script>

<style lang="scss" scoped>
.integral {
	display: flex;
	align-items: center;

	div {
		margin-left: 5px;
		color: #101010;
		font-size: 16px;
		font-weight: 400;
	}
}

.earnPoints {
	color: #bbbbbb;
	font-size: 14px;
}

.richEditor {
	position: absolute;
	top: 20px;
	// height: 200px;
}

.yang {
	width: 195px;
	margin-left: 138px;
	// height: 300px;
	white-space: pre-wrap;
	overflow-wrap: break-word;
	word-break: break-all;
	// flex-wrap: wrap;
	// word-break: break-all;
	// word-wrap: break-word;
}

.popup__notification {
	width: 195px;
	// height: 330px;
	margin-left: 138px;
	// background: #b80c0c;
	border-radius: 10px;
	// white-space: pre-wrap;
	// overflow-wrap: break-word;
	// word-break: break-all;

	.center {
		display: flex;
		justify-content: center;
		margin: 10px 0;

		.sidebar-item {
			font-size: 14px;
			color: #101010;
			width: 195px;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.sidebar {
				background: rgba(249, 120, 3, 1);
				width: 3px;
				height: 10px;
				margin-right: 5px;
			}
		}
	
	}

	.center__top {
		color: #737171;
		font-size: 12px;
		// margin: 0 20px;
		.name{
				width:200px;
				// background-color: aqua;
				// white-space: pre-wrap;
	overflow-wrap: break-word;
	word-break: break-all;
			}
	}
}</style>
