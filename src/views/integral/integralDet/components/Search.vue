<template>
    <m-card class="form" :needToggle="true">
        <el-form ref="form" :model="searchType" label-width="90px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="用户名称">
                        <el-input v-model="searchType.nikeName" placeholder="请输入用户名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="用户电话">
                        <el-input v-model="searchType.phone" placeholder="请输入用户电话"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="变更时间">
                        <el-date-picker v-model="value1" type="daterange" range-separator="-" start-placeholder="开始时间"
                            end-placeholder="结束时间" style="width: 330px;" @change="chooseOrderDateTimes">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="变动类型">
                        <el-select v-model="searchType.source" placeholder="请选择">
                            <el-option v-for="item in sourceOption" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="会员等级">
                        <el-select v-model="searchType.memberLevelId" placeholder="请选择">
                            <el-option v-for="item in memberLevelOption" :key="item.id" :label="item.memberLevel"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="primary" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>
    </m-card>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { SearchState, SearchKeyType } from "./searchType";
import DateUtil from "@/store/modules/date";
import {
    getMemberLevelList
} from "@/api/customer/customer";
@Component
export default class Search extends Vue implements SearchState {
    name = "Search";
    searchType = {
        startTime: '',//开始时间
        endTime: '',//结束时间
        phone: '',//用户电话
        nikeName: '',//用户名称
        source: -1,//变动类型
        memberLevelId: '',//会员等级id
    } as SearchKeyType;
    sourceOption = [{
        value: -1,
        label: '全部'
    }, {
        value: 0,
        label: '自动'
    }, {
        value: 1,
        label: '手动'
    }];
    memberLevelOption = [];
    value1 = ''
    created() {
        this.getMemberLevelList();
    }
    /**
 * 选择单据日期
 * @param data 
 */
    chooseOrderDateTimes(data: any) {
        this.searchType.startTime = data ? this.dateConversion(data[0]) + " 00:00:00" : "";
        this.searchType.endTime = data ? this.dateConversion(data[1]) + " 23:59:59" : "";
        console.log("this.searchType", this.searchType);
    }
    getMemberLevelList() {
        getMemberLevelList({}).then((res) => {
            this.memberLevelOption = res.data
        }).catch((err) => {
            this.$message.error(err)
        })
    }
    dateConversion(value: Date) {
        const date = new DateUtil("").getYMDs(value);
        return date;
    }
    search() {
        this.$emit("searchBy", this.searchType);
    }
    reset() {
        this.value1 = ''
        this.searchType = {
            startTime: '',//开始时间
            endTime: '',//结束时间
            phone: '',//用户电话
            nikeName: '',//用户名称
            source: -1,//变动类型
            memberLevelId: '',//会员等级id
        }
        this.$emit("searchBy", this.searchType);
    }
}
</script>
<style lang="scss" scoped>
.el-form-item .el-input {
    width: 224px;
}

.el-form-item .el-button {
    width: 90px;
}

@include b(form) {
    transform-origin: left top;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease 0s;

    &.show {
        height: 260px;
        margin-bottom: 20px;
    }

    &.hide {
        margin-bottom: 20px;
        height: 50px;

        .form__btn {
            width: 940px;
            height: 50px;
            background: #f9f9f9;
            line-height: 50px;
            // margin-top: 20px
        }
    }

    @include e(btn) {
        width: 100%;
        position: absolute;
        bottom: 0;
        text-align: center;
        padding-bottom: 20px;

        span {
            cursor: pointer;
        }
    }
}

.page {
    // height: 270px;
    background-color: #f9f9f9;
    margin-bottom: 20px;
}

@include b(search) {
    display: flex;
    flex-wrap: wrap;

    @include e(item) {
        padding: 20px 40px 10px 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @include m(text) {
            width: 60px;
        }
    }

    @include e(icon) {
        width: 40px;
        text-align: center;
        border-left: 1px solid #dcdfe6;
        cursor: pointer;
        vertical-align: middle;
    }
}

@include b(searchButton) {
    margin: 20px 30px;
}
</style>