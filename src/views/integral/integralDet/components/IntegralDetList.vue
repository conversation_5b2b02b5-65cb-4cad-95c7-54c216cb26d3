<template>
    <div style="margin-top: 20px">
        <el-table :data="goodList" style="width: 100%">
            <el-table-column label="序号" type="index" width="60">
            </el-table-column>
            <el-table-column prop="nikeName" label="用户名称" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="phone" label="用户电话" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="memberLevel" label="会员等级" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="firstLoginTime" label="注册时间" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="useDays" label="使用时长（天）" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="typeText" label="积分类型" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="lastIntegral" label="变动前积分" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="integral" label="增减数" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="totalIntegral" label="变动后积分" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="changeUser" label="变更人" show-overflow-tooltip>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { searchMiniAccountIntegralManage } from '@/api/reward/reward';
import { SearchKeyType } from "./searchType";
@Component({
    components: {

    }
})
export default class IntegralDetList extends Vue {
    name = "IntegralDetList"
    goodList = [];
    searchType = {
        current: 1,
        size: 10
    } as SearchKeyType;
    total = 0;
    created() {
        this.getIntegralDetListt();
    }
    async getIntegralDetListt() {
        const param = this.searchType;
        try {
            const res = await searchMiniAccountIntegralManage(param);
            const goodList = res.data.list;
            this.total = res.data.total;
            this.goodList = goodList;
        } catch (error) {
            console.log(error);
        }
        this.$emit("getShowProList", this.goodList);
    }
}
</script>

<style lang="scss" scoped>
.goodList {
    width: 250px;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-right: 20px;
    overflow: hidden;
}

.center {
    display: flex;
    justify-content: center;
}

.digTitle {
    font-size: 17px;
    font-weight: bold;
}
</style>