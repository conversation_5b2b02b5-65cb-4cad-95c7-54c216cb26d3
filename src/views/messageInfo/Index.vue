<!-- 
2025.5.28有改动的页面 
-->
<template>
    <div class="customer">
        <el-tabs>
            <div class="memberList">
                <div style="margin-bottom: 20px">
                    <el-button @click="batchOperations">批量已读</el-button>
                    <el-button @click="bulkDeletion">批量删除</el-button>
                </div>
                <div class="line"></div>
                <m-card class="form" :needToggle="false">
                    <template>
                        <el-table :data.sync="messageList" style="width: 100%" class="imgView" border
                            @selection-change="handleSelectionChange">
                            <el-table-column type="selection" width="55">
                            </el-table-column>
                            <el-table-column label="序号" type="index" width="50">
                            </el-table-column>
                            <el-table-column prop="title" label="标题" width="100">
                            </el-table-column>
                            <el-table-column prop="content" label="内容" width="400">
                            </el-table-column>
                            <el-table-column prop="createTime" label="创建时间" width="150">
                            </el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template slot-scope="scope">
                                    <template v-if="scope.row.status == 0">
                                        <span style=" display: block; width: 30px; text-align: center; ">未读</span>
                                    </template>
                                    <template v-if="scope.row.status == 1">
                                        <span style="display: block;  width: 30px; text-align: center; ">已读</span>
                                    </template>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="150">
                                <template slot-scope="scope">
                                    <el-button type="text" size="medium" @click="detailButton(scope.row)">详情
                                    </el-button>
                                    <el-button type="text" size="medium" @click="delBtn(scope.row.id)">删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                    <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
                        @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
                </m-card>
            </div>
        </el-tabs>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import { pageList, deleteMessageInfo, updateStatus, batchOperation, batchDelete } from "@/api/message/message";
import { forEach } from 'lodash';



@Component({
    components: {
        PageManage
    }
})


export default class roleInfo extends Vue implements CustomerListState {
    /** 分页条数 */
    pageSize = 10;

    /** 分页页码 */
    pageNum = 1;

    /** 数据长度 */
    total = 0;

    // 消息数据
    messageList = {};

    //需要批量操作的数据
    multipleSelection = {
     
    }

mounted() {
    this.getDataList(1);
}
delBtn(id: string) {
    this.$confirm("确定要删除选中消息吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    }).then(() => {
        let param = { id: id };
        deleteMessageInfo(param)
            .then(res => {
                if (res.code === 200) {
                    this.$message.success("删除成功");
                    this.getDataList(1);
                }
            })
            .catch(err => {
                this.$message.error(err);
            });
    });
}
getDataList(pageNum: number) {
    const param = {
        current: pageNum,
        size: this.pageSize,
    }
    // 调用接口
    pageList(param).then(res => {
        // 消息数据
        this.messageList = res.data.list
        // 显示数据条数
        this.pageSize = res.data.size;
        // 第几页
        this.pageNum = res.data.current;

        this.total = res.data.total;
    })
}
detailButton(row) {
    // 1-订单消息、2-售后消息、3-用户消息、4-营销活动、5-代发货消息、6-提现消息、7-预约单消息

    if (row.messageType == 1) {
        let param = { id: row.id };
        updateStatus(param)
            .then(res => {
                if (res.code === 200) {
                    this.$router.push({ path: '/order/delivery', query: { orderId: row.orderId, t: Date.now() } }).catch(() => { })
                }
            })
            .catch(err => {
                this.$message.error(err);
            });
    }
    if (row.messageType == 2) {
        this.$router.push({ path: '/order/afterSale', query: {  } }).catch(() => { })     
    }
    if (row.messageType == 3) {
        this.$router.push({ path: '/customer/list', query: {  } }).catch(() => { })     
    }
    if (row.messageType == 4) {
        this.$router.push({ path: '/certificate/certificateList', query: {  } }).catch(() => { })     
    }
    if (row.messageType == 5) {
        let param = { id: row.id };
        updateStatus(param)
            .then(res => {
                if (res.code === 200) {
                    this.$router.push({ path: '/order/dropShipping', query: { orderId: row.orderId, t: Date.now(), orderStatus: '1' } }).catch(() => { })
                }
            })
            .catch(err => {
                this.$message.error(err);
            });   
    }
    if (row.messageType == 6) {
        this.$router.push({ path: '/commission/list', query: {  } }).catch(() => { })     
    }
    if (row.messageType == 7) {
        this.$router.push({ path: '/store/storeInfo', query: {  } }).catch(() => { })     
    }
    console.log('messageType=',row.messageType);
    
}
/**
* @method handleCurrentChange
* @description 当前页
*/
handleCurrentChange(val: number) {
    this.pageNum = val;
    this.getDataList(val);
}
/**
* @method handleSizeChange
* @description 每页 条
*/
handleSizeChange(val: number) {
    this.pageSize = val;
    this.getDataList(1);
}

/**
 * 选择数据
 */
handleSelectionChange(val: any) {
    //因为是使用push方法 所以要将选择过的数据清空
    this.multipleSelection.ids = []
    let idsArry = []
    forEach(val, (item: any) => {
        idsArry.push(item.id)
    })
    this.multipleSelection.ids = idsArry.join(",")
}

/**
 * 批量操作
 */
batchOperations() {
    batchOperation(this.multipleSelection).then(res => {
        this.getDataList(1);
        this.$message.success("批量操作成功");
    }).catch(err => {
        this.$message.error("批量操作失败");
    });
  
}

/**
 * 批量删除
 */
bulkDeletion() {
    batchDelete(this.multipleSelection).then(res => {
        this.getDataList(1);
        this.$message.success("批量删除成功");
    }).catch(err => {
        this.$message.error("批量删除失败");
    });

}
}
</script>