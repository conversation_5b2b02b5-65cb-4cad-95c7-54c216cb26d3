<!--
 * @description: 商品库存页面
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:09
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-12 13:17:51
-->
<template>
  <div class="customer">
    <el-tabs>
      <div class="memberList">
        <div class="line"></div>
        <m-card class="form" :needToggle="true">
          <el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
            <el-row :gutter="40">
              <el-col :span="10">
                <el-form-item label="商品名称/编码">
                  <el-input v-model="dataForm.storehouseNumber" clearable placeholder="请输入商品名称/编码" />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="商品规格"><el-input v-model="dataForm.storehouseName" clearable
                    placeholder="请输入商品规格" /></el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="40">
              <el-col :span="10">
                <el-form-item label="关联商品名称"><el-input v-model="dataForm.linkGoodsName" clearable
                    placeholder="请输入关联商品名称" /></el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="商品规格2"><el-input v-model="dataForm.specs2" clearable
                    placeholder="请输入商品规格2" /></el-form-item>
              </el-col>

            </el-row>
            <el-row :gutter="40">
              <el-col :span="10">
                <el-form-item label="库存状态">
                  <el-select v-model="optvalue" placeholder="请选择">
                    <el-option v-for="item in options" :key="item.optvalue" :label="item.label" :value="item.optvalue">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="商品状态">
                  <el-select v-model="value" placeholder="请选择">
                    <el-option v-for="itemtwo in commodity" :key="itemtwo.value" :label="itemtwo.label"
                      :value="itemtwo.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="40">
              <el-col :span="10">
                <el-form-item label="分类">
                  <!-- <template v-for="itemone in classification"> -->
                  <el-select v-model="showCategoryId" multiple placeholder="请选择">
                    <el-option-group v-for="itemone in classification" :key="itemone.saleMode" :label="itemone.name">
                      <el-option v-for="itemtwo in itemone.showCategoryVos" :key="itemtwo.showCategoryId"
                        :label="itemtwo.name" :value="itemtwo.showCategoryId">
                      </el-option>
                    </el-option-group>
                  </el-select>
                  <!-- </template> -->
                  <!-- 商品库存列表 -->
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="仓库">
                  <el-select v-model="warehouseDadaList" multiple placeholder="请选择">
                    <el-option v-for="itemtwo in warehouseList" :key="itemtwo.id" :label="itemtwo.warehouseFullName"
                      :value="itemtwo.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="40">
              <el-button type="primary" style="margin-left: 100px" @click="warehouseItem(1)">搜索</el-button>
            </el-row>
            <el-row :gutter="40">
              <el-col :span="10">
                <el-form-item label=""> </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </m-card>

        <div class="topLine">
          <div class="topLine__left" style="margin-left: 30px;">
            <el-button type="primary" @click="exportData" >导出列表</el-button>
          </div>

        </div>

        <template>
          <el-table :data="wareList" style="width: 100%" border max-height="100%">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="goodsCode" label="商品编码" width="160">
            </el-table-column>
            <el-table-column prop="goodsName" label="商品名称" width="160">
            </el-table-column>
            <el-table-column prop="specs" label="商品规格" width="160">
            </el-table-column>
            <el-table-column prop="specs2" label="商品规格2" width="160">
            </el-table-column>
            <el-table-column prop="linkGoodsName" label="关联商品名称" width="160">
            </el-table-column>
            <el-table-column prop="unit" label="单位" width="160">
            </el-table-column>
            <el-table-column prop="warehouseFullName" label="所属仓库" width="160">
            </el-table-column>
            <el-table-column prop="stock" label="可用库存量" width="140">
            </el-table-column>
            <el-table-column
              prop="reallyOutStock"
              label="实际出库数"
              width="110">
            </el-table-column>
            <el-table-column prop="upperLimit" label="库存上限" width="160">
            </el-table-column>
            <el-table-column prop="lowerLimit" label="库存下限" width="160">
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="60">
              <template slot-scope="scope">
                <el-button type="text" size="medium" icon="el-icon-edit" v-if="editButton || isSupper"
                  @click="EditWareBtn(scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 编辑仓库模块 -->
        <el-dialog :visible.sync="tEditWarehouse" width="50%">
          <div slot="title" class="diaTitle" style="font-size: 15px">
            库存设置
          </div>
          <el-table :data="EditwareList" :header-cell-style="{ background: '#F2F4F7', color: '#606266' }" height="100%"
            size="30" :header-row-style="{ height: '50' }">
            <el-table-column label="库存上限">
              <template slot-scope="scope">
                <el-input size="mini" v-model="scope.row.upperLimit" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="库存下限">
              <template slot-scope="scope">
                <el-input size="mini" v-model="scope.row.lowerLimit" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
          </el-table>
          <span slot="footer" class="dialog-footer">
            <el-button @click="tEditWarehouse = false">取 消</el-button>
            <el-button type="primary" @click="houseEditSub()">确 定</el-button>
          </span>
        </el-dialog>

        <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
          @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
        <black-list ref="blackListDialogRef" v-if="blackListVisible" @refreshDataList="warehouseItem" />
        <set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList" @refreshDataList="warehouseItem(1)" />
        <set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList" @refreshDataList="warehouseItem(1)" />
      </div>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import BlackList, {
  NewCustomerTagList,
} from "@/views/customer/common/SetBlackList.vue";
import SetDrop from "@/views/customer/common/SetDrop.vue";
import SetLabel from "@/views/customer/list/components/dialog/SetTags.vue";

import {
  CustomerListState,
  ApiCustomerList,
  CustomerTagList,
  CustomerRank,
} from "./customerListType";
import { ElForm } from "element-ui/types/form";

import {
  warehouseList,
  AddWarehouse,
  editWarehouse,
  delWarehouse,
  stateWarehouse,
  productWarehouse,
  selectWarehouse,
  getShowCate,
  UpperAndLowerEdit,
  exportProductStock,
} from "@/api/warehouse/warehouse";
import { ElTable } from "element-ui/types/table";

@Component({
  components: {
    PageManage,
    BlackList,
    SetDrop,
  },
})
export default class Index extends Vue implements CustomerListState {
  @Ref()
  readonly dataFormRef!: ElForm;

  @Ref()
  readonly customerListRef!: ElTable;

  @Ref()
  readonly blackListDialogRef!: BlackList;

  @Ref()
  readonly labelDialogRef!: SetLabel;

  dataList: Array<ApiCustomerList> = [];

  allTagList: Array<CustomerTagList> = [];

  selectionList: Array<ApiCustomerList | NewCustomerTagList> = [];

  multipleSelection: Array<ApiCustomerList | NewCustomerTagList> = [];

  managerVisible = false;

  blackListVisible = false;

  labelVisible = false;

  visible = true;

  //仓库编辑弹出框
  tEditWarehouse = false;

  dataForm = {
    storehouseNumber: "",
    storehouseName: "",
    storehouseAddress: "",
    memberNumber: null,
    rankCode: null,
    sortType: 1,
    tagId: "",
    specs2: "",
    linkGoodsName: "",
  };

  //弹出框新建内容
  numberValidateForm = {
    houseNumber: "",
    houseName: "",
    houseadd: "",
  };

  houseList = {};

  //搜索模块里的下拉框模块
  options = [
    {
      optvalue: "1",
      label: "大于上限",
    },
    {
      optvalue: "2",
      label: "小于下限",
    },
    {
      optvalue: "3",
      label: "无",
    },
  ];

  optvalue = "";

  commodity = [
    {
      value: "-1",
      label: "全部",
    },
    {
      value: "0",
      label: "下架（仓库中）",
    },
    {
      value: "1",
      label: "上架",
    },
  ];
  value = "-1";

  //搜索模块里的多选框模块
  warehouseList = [];
  values: []; // 存储value的数组
  labels: []; // 存储label的数组
  checkList = [];

  //分类搜索模块
  warehouseDadaList = [];

  //分类搜索模块
  classification = [];
  showCategoryId = [];
  defaultProps = {
    children: "showCategoryVos",
    label: "name",
  };
  demoString = [];

  closeTree = false;

  rankOptions: Array<CustomerRank> = [];

  /** 分页条数 */
  pageSize = 10;

  /** 分页页码 */
  pageNum = 1;

  /** 数据长度 */
  total = 0;

  /** 赠送优惠券下拉菜单 */
  couponDropList: any[] = [
    {
      command: "加入黑名单",
      disabled: false,
      show: true,
      text: "加入黑名单",
    },
  ];

  wareList = [];

  Disableid = "";

  Disablestate = "";

  warehouseList = {
    warehouseNumber: "",
    warehouseAddress: "",
    warehouseFullName: "",
  };

  EditwareList = [];

  editButtonCode = "commodityStock.edit";

  menuName = "商品库存";

  editButton = false;

  buttonList = [];

  isSupper = 0;

  mounted() {
    this.warehouseItem(1);
    this.selectWar();
    this.ShowCate();
    this.buttonAuth();
  }

  buttonAuth() {
    this.isSupper = this.$STORE.userStore.userInfo.isSupper;
    let authMenuButtonVos =
      this.$STORE.userStore.userInfo.authMenuButtonVos.filter(
        (i) => i.menuName == this.menuName,
      );

    let buttonList = [];

    authMenuButtonVos.forEach((element) => {
      buttonList.push(element.buttonCode);
    });

    this.buttonList = buttonList;

    var editButtonData = buttonList.find((e) => e == this.editButtonCode);

    if (editButtonData != null && editButtonData != undefined) {
      this.editButton = true;
    }
  }
  getMemberList(data) {
    console.log(data);
  }

  batchCouponClick() {
    if (this.multipleSelection.length > 0) {
      this.labelVisible = true;
      this.$nextTick(() => {
        this.labelDialogRef.init(this.multipleSelection as ApiCustomerList[]);
      });
    } else {
      this.$message.warning("至少选择一个客户");
    }
  }

  batchCouponCommand(command: string) {
    if (this.multipleSelection.length > 0) {
      switch (command) {
        case "加入黑名单":
          this.blackListVisible = true;
          this.$nextTick(() => {
            this.blackListDialogRef.init(
              this.multipleSelection as ApiCustomerList[],
              1,
            );
          });
          break;
      }
    } else {
      this.$message.info("请至少选择一个客户");
    }
  }

  itemClick(row: ApiCustomerList) {
    this.blackListVisible = true;
    this.$nextTick(() => {
      this.blackListDialogRef.init([row], 1);
    });
  }

  itemCommand(command: string, row: ApiCustomerList) {
    switch (command) {
      case "加入黑名单":
        this.blackListVisible = true;
        this.$nextTick(() => {
          this.blackListDialogRef.init([row], 1);
        });
        break;
      case "3":
        this.memberUpgrade();
        break;
      case "5":
        this.memberRenews();
        break;
    }
  }

  //获取商品库存列表
  warehouseItem(pageNum: number) {
    const form = this.dataForm;
    console.log(form);
    console.log(this.value);
    console.log(form.storehouseName);
    console.log(this.showCategoryId);
    const param = {
      status: this.value,
      current: pageNum,
      size: this.pageSize,
      keyword: form.storehouseNumber,
      specs: form.storehouseName,
      specs2: form.specs2,
      linkGoodsName: form.linkGoodsName,
      stockStatus: this.optvalue,
      warehouseIdList: this.warehouseDadaList,
      showCategoryIdList: this.showCategoryId,
    };
    productWarehouse(param).then((res) => {
      this.wareList = res.data.list;
      this.pageSize = res.data.size;
      this.pageNum = res.data.current;
      this.total = res.data.total;
      console.log(res.data.total);
    });
  }

  //获取搜索框的仓库列表
  selectWar(pageNum: number) {
    const param = {
      Current: 1,
      size: 100,
    };
    selectWarehouse(param).then((res) => {
      this.warehouseList = res.data;
      //console.log(this.select)
      // this.select = res.data.list
    });
  }
  ShowCate(pageNum: number) {
    const paramen = {
      Current: pageNum,
      size: this.pageSize,
    };
    getShowCate(paramen).then((res) => {
      this.classification = res.data.list;
      console.log(this.classification);
    });
  }

  //仓库选择模块
  //   selectBox(val){
  // 	  	console.log("val",val);
  // 	  	// 首先要初始化这两个数组，因为当取消checkbox的时候，数组中就不能有当前取消的值。
  // 	    this.values = [];
  // 	    this.labels= [];
  // 		val.forEach(item=>{
  // 		  const value = item.split(':')[0];
  // 		  const label= item.split(':')[1];
  // 		  this.values.push(value);
  // 		  this.labels.push(label);
  // 		});
  // 		console.log("this.values",this.values);
  // 		console.log("this.labels",this.labels);
  // 	  }

  //获取当前行需要修改的信息
  EditWareBtn(val) {
    this.tEditWarehouse = true;
    this.EditwareList = [val];
    console.log(this.EditwareList);
  }

  steWarehouse() {
    const statusList = {
      id: this.Disableid,
      state: this.Disablestate,
    };
    stateWarehouse(statusList)
      .then((res) => {
        this.$message.success(res.data);
      })
      .catch((err) => {
        this.$message.error(err || "网络错误");
      });
  }

  setLabel(row: ApiCustomerList) {
    this.labelVisible = true;
    this.$nextTick(() => {
      this.labelDialogRef.init([row]);
    });
  }

  //仓库编辑的提交方法
  houseEditSub() {
    // console.log(this.EditwareList[0].id)
    // console.log(this.EditwareList[0].lowerLimit)
    // console.log(this.EditwareList[0].upperLimit)
    const houseEdit = {
      id: this.EditwareList[0].id,
      warehouseFullName: this.EditwareList[0].warehouseNumber,
      lowerLimit: this.EditwareList[0].lowerLimit,
      upperLimit: this.EditwareList[0].upperLimit,
    };
    if (this.EditwareList[0].upperLimit > this.EditwareList[0].lowerLimit) {
      this.tEditWarehouse = false;
      UpperAndLowerEdit(houseEdit)
        .then((res) => {
          this.$message({
            message: res.data,
            type: "success",
          });
        })
        .catch((err) => {
          this.$message.error(err || res.data);
        });
    } else {
      this.$message({
        showClose: true,
        message: "库存上限需要大于库存下限",
        type: "error",
      });
    }
    this.warehouseItem(1);
  }

  /**
   * @method handleSizeChange
   * @description 每页 条
   */
  handleSizeChange(val: number) {
    this.pageSize = val;
    this.warehouseItem(1);
  }

  /**
   * @method handleCurrentChange
   * @description 当前页
   */
  handleCurrentChange(val: number) {
    this.pageNum = val;
    this.warehouseItem(val);
  }

  // 导出数据
  async exportData() {
    this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const params = {
        status: this.value,
        keyword: this.dataForm.storehouseNumber,
        specs: this.dataForm.storehouseName,
        specs2: this.dataForm.specs2,
        linkGoodsName: this.dataForm.linkGoodsName,
        stockStatus: this.optvalue,
        warehouseIdList: this.warehouseDadaList,
        showCategoryIdList: this.showCategoryId,
      };

      exportProductStock(params).then((res) => {
        var blob = new Blob([res.data], {
          type: "application/x-msdownload;charset=UTF-8",
        });
        // 创建一个blob的对象链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        // 把获得的blob的对象链接赋值给新创建的这个 a 链接
        let now = new Date();
        let timestamp = now.getTime();
        link.setAttribute('download', '商品库存列表_' + timestamp + '.xls'); // 设置下载文件名
        document.body.appendChild(link);

        // 触发下载
        link.click();
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$message.success('导出成功');

      }).catch((err) => {
        this.$message.error("导出失败");
      });
    }).catch(() => {
      // 用户取消导出
    });
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.topLine {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  &__left {
    display: flex;
  }

  &__right {
    width: 100px;
    display: flex;
    justify-content: space-around;
  }
}
</style>
