<!--
 * @description: 抽离开源版本
 * @Author: chuy<PERSON><PERSON>
 * @Date: 2021-08-20 17:29:09
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-12 13:17:51
-->
<template>
	<div class="customer">
		<el-tabs>
			<div class="memberList">
				<div class="line"></div>
				<m-card class="form" :needToggle="true">
					<el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="仓库编号"><el-input v-model="dataForm.storehouseNumber" clearable
										placeholder="请输入仓库编号" /></el-form-item>
							</el-col>
							<el-col :span="10">
								<el-form-item label="仓库名称"><el-input v-model="dataForm.storehouseName" clearable
										placeholder="请输入仓库名称" /></el-form-item>
							</el-col>
							<!-- <el-col :span="12">
									<el-form-item label="标  签">
										<el-select v-model="dataForm.tagId" placeholder="请选择标签" style="width: 256px" clearable>
											<el-option label="全部" :value="null" />
											<el-option v-for="tag in allTagList" :key="tag.tagId" :label="tag.tagName" :value="tag.tagId" />
										</el-select>
									</el-form-item>
								</el-col> -->
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="仓库地址"><el-input v-model="dataForm.storehouseAddress" clearable
										placeholder="请输入仓库地址" /></el-form-item>
							</el-col>
							<!-- <el-form-item label="上次交易时间">
										<el-date-picker
											v-model="dataForm.orderSuccessTime"
											:default-time="['00:00:00', '23:59:59']"
											value-format="yyyy-MM-dd HH:mm:ss"
											style="width: 256px"
											type="daterange"
											range-separator="-"
											start-placeholder="开始日期"
											end-placeholder="结束日期"
										></el-date-picker>
									</el-form-item> -->
						</el-row>
						<el-button type="primary" style="margin-left:100px" @click="warehouseItem()">搜索</el-button>
					</el-form>
				</m-card>
				<el-button v-if="addButton || isSupper" type="primary" icon="el-icon-circle-plus-outline"
					@click="newWarehouse = true" style="margin-bottom:20px; float:right; margin-right: 60px;">
					新增
				</el-button>
				<!-- 新增仓库模块 模态框 -->
				<el-dialog title="新建仓库" :visible.sync="newWarehouse" width="30%">
					<!-- <template v-for="item in wareList" :key="item.tagId"> -->
					<el-form :model="numberValidateForm" ref="numberValidateForm" label-width="100px">
						<el-form-item label="仓库编号" prop="houseNumber" :rules="[
							{ required: true, message: '仓库编号不能为空' }
						]">
							<el-input v-model="numberValidateForm.houseNumber"></el-input>
						</el-form-item>
						<el-form-item label="仓库名称" prop="houseName" :rules="[
							{ required: true, message: '仓库名称不能为空' }
						]">
							<el-input type="age" v-model="numberValidateForm.houseName" autocomplete="off"></el-input>
						</el-form-item>
						<el-form-item label="仓库地址">
							<el-input v-model="numberValidateForm.houseadd"></el-input>
						</el-form-item>
					</el-form>
					<!-- </template> -->
					<span slot="footer" class="dialog-footer">
						<el-button @click="newWarehouse = false;">取 消</el-button>
						<el-button type="primary" @click="newWarehouseBtn()">确 定</el-button>
					</span>
				</el-dialog>
				<!-- 编辑仓库模块 -->
				<el-dialog :visible.sync="tEditWarehouse" width="50%">
					<div slot="title" class="diaTitle" style="font-size: 15px;">编辑仓库</div>
					<el-table :data="EditwareList" :header-cell-style="{ background: '#F2F4F7', color: '#606266' }"
						height="100%" size="30" :header-row-style="{ height: '50' }">
						<el-table-column label="仓库编号">
							<template slot-scope="scope">
								<el-input size="mini" v-model.lazy="scope.row.warehouseNumber"
									placeholder="请输入"></el-input>
							</template>
						</el-table-column>
						<el-table-column label="仓库全称">
							<template slot-scope="scope">
								<el-input size="mini" v-model.lazy="scope.row.warehouseFullName"
									placeholder="请输入"></el-input>
							</template>
						</el-table-column>
						<el-table-column label="仓库地址">
							<template slot-scope="scope">
								<el-input size="mini" v-model.lazy="scope.row.warehouseAddress"
									placeholder="请输入"></el-input>
							</template>
						</el-table-column>
					</el-table>
					<span slot="footer" class="dialog-footer">
						<el-button @click="tEditWarehouse = false">取 消</el-button>
						<el-button type="primary" @click="houseEditSub()">确 定</el-button>
					</span>
				</el-dialog>
				<!-- 仓库列表 -->
				<template>
					<el-table :data="wareList" style="width: 100%" border max-height="100%">
						<el-table-column type="selection" width="55" align="center">
						</el-table-column>
						<el-table-column prop="warehouseNumber" label="仓库编号" align="center">
						</el-table-column>
						<el-table-column prop="warehouseFullName" label="仓库全称" align="center">
						</el-table-column>
						<el-table-column prop="warehouseAddress" label="仓库地址" align="center">
						</el-table-column>
						<el-table-column label="默认仓库" align="center">
							<template slot-scope="scope">
								<div style="display: flex;justify-content: center;align-items: center;">
									<div v-if="scope.row.stockFlag == 1"
										style="width: 16px;height: 16px;border-radius: 50%;background: #409EFF;display: flex;justify-content: center;align-items: center;">
										<div style="width: 6px;height: 6px;border-radius: 50%;background: #fff;"></div>
									</div>
									<div @click="moWare(scope.row)" v-else
										style="width: 16px;height:16px;border-radius: 50%;border: 1px solid #DCDFE6;"></div>

								</div>
							</template>
						</el-table-column>
						<!--状态栏-->
						<el-table-column label="状态" align="center">
							<template slot-scope="scope">
								<template v-if="scope.row.state == 0">
									<span style=" display: block; width: 30px; text-align: center; ">停用</span>
								</template>
								<template v-if="scope.row.state == 1">
									<span style="display: block;  width: 30px; text-align: center; ">启用</span>
								</template>
							</template>
						</el-table-column>
						<el-table-column fixed="right" label="操作" align="center">
							<template slot-scope="scope">
								<el-tooltip content="编辑">
									<!-- 编辑-->
									<el-button v-if="editButton || isSupper" type="text" size="medium"
										icon="el-icon-edit" @click="EditWareBtn(scope.row)"></el-button>
								</el-tooltip>
								<el-tooltip content="删除">
									<!-- 删除-->
									<el-button v-if="deleteButton || isSupper" type="text" size="medium"
										icon="el-icon-delete-solid" @click="delWareBtn(scope.row)"></el-button>
								</el-tooltip>

								<!--启用/删除-->
								<template v-if="scope.row.state == 0 && (updateStateButton || isSupper)">
									<el-tooltip content="启用">
										<el-button type="text" icon="el-icon-close"
											@click="deactivate(scope.row)"></el-button>
									</el-tooltip>
								</template>
								<template v-if="scope.row.state == 1 && (updateStateButton || isSupper)">
									<el-tooltip content="停用">
										<el-button type="text" icon="el-icon-check"
											@click="deactivate(scope.row)"></el-button>
									</el-tooltip>

								</template>




							</template>
						</el-table-column>
					</el-table>
				</template>
				<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
				<black-list ref="blackListDialogRef" v-if="blackListVisible" @refreshDataList="warehouseItem" />
				<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList"
					@refreshDataList="warehouseItem()" />
				<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList"
					@refreshDataList="warehouseItem()" />
			</div>
		</el-tabs>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import BlackList, { NewCustomerTagList } from '@/views/customer/common/SetBlackList.vue';
import SetDrop from '@/views/customer/common/SetDrop.vue';
import SetLabel from '@/views/customer/list/components/dialog/SetTags.vue';

import { CustomerListState, ApiCustomerList, CustomerTagList, CustomerRank } from './customerListType';
import { ElForm } from 'element-ui/types/form';

import { warehouseList, AddWarehouse, editWarehouse, delWarehouse, stateWarehouse, updateStock } from '@/api/warehouse/warehouse';
import { ElTable } from 'element-ui/types/table';

@Component({
	components: {
		PageManage,
		BlackList,
		SetDrop,
	}
})
export default class Index extends Vue implements CustomerListState {
	@Ref()
	readonly dataFormRef!: ElForm;

	@Ref()
	readonly customerListRef!: ElTable;

	@Ref()
	readonly blackListDialogRef!: BlackList;

	@Ref()
	readonly labelDialogRef!: SetLabel;

	// 数据表单  组成数据用于向后端提交数据
	dataForm = {
		storehouseNumber: '',
		storehouseName: '',
		storehouseAddress: '',
		memberNumber: null,
		rankCode: null,
		sortType: 1,
		tagId: ''
	};

	dataList: Array<ApiCustomerList> = [];

	allTagList: Array<CustomerTagList> = [];

	selectionList: Array<ApiCustomerList | NewCustomerTagList> = [];

	multipleSelection: Array<ApiCustomerList | NewCustomerTagList> = [];

	managerVisible = false;

	blackListVisible = false;

	labelVisible = false;

	visible = true;

	//仓库新增弹出框
	newWarehouse = false;

	//仓库编辑弹出框
	tEditWarehouse = false;

	//弹出框新建内容
	numberValidateForm = {
		houseNumber: '',
		houseName: '',
		houseadd: ''
	}

	houseList = {}

	rankOptions: Array<CustomerRank> = [];

	/** 分页条数 */
	pageSize = 10;

	/** 分页页码 */
	pageNum = 1;

	/** 数据长度 */
	total = 0;

	/** 赠送优惠券下拉菜单 */
	couponDropList: any[] = [
		{
			command: '加入黑名单',
			disabled: false,
			show: true,
			text: '加入黑名单'
		}
	];

	// 存放全部仓库数据
	wareList = [];

	Disableid = '';

	Disablestate = '';

	warehouseList = {
		warehouseNumber: '',
		warehouseAddress: '',
		warehouseFullName: ''
	};
	// 存放编辑后获取到的数据
	EditwareList = [];
	EditwareList1 = [];

	addButtonCode = "warehouse.add";

	deleteButtonCode = "warehouse.delete";

	editButtonCode = "warehouse.edit";

	updateStateButtonCode = "warehouse.updateState";

	menuName = "仓库管理";

	addButton = false;

	deleteButton = false;

	editButton = false;

	updateStateButton = false;

	buttonList = [];

	isSupper = 0;

	mounted() {
		this.warehouseItem(1);
		this.buttonAuth();
	}

	buttonAuth() {

		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var addButtonData = buttonList.find(e => e == this.addButtonCode);

		if (addButtonData != null && addButtonData != undefined) {
			this.addButton = true;
		}

		var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);

		if (deleteButtonData != null && deleteButtonData != undefined) {
			this.deleteButton = true;
		}

		var editButtonData = buttonList.find(e => e == this.editButtonCode);

		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}

		var updateStateButtonData = buttonList.find(e => e == this.updateStateButtonCode);

		if (updateStateButtonData != null && updateStateButtonData != undefined) {
			this.updateStateButton = true;
		}



	}
	getMemberList(data) {
		console.log(data)
	}

	batchCouponClick() {
		if (this.multipleSelection.length > 0) {
			this.labelVisible = true;
			this.$nextTick(() => {
				this.labelDialogRef.init(this.multipleSelection as ApiCustomerList[]);
			});
		} else {
			this.$message.warning('至少选择一个客户');
		}
	}

	batchCouponCommand(command: string) {
		if (this.multipleSelection.length > 0) {
			switch (command) {
				case '加入黑名单':
					this.blackListVisible = true;
					this.$nextTick(() => {
						this.blackListDialogRef.init(this.multipleSelection as ApiCustomerList[], 1);
					});
					break;
			}
		} else {
			this.$message.info('请至少选择一个客户');
		}
	}

	itemClick(row: ApiCustomerList) {
		this.blackListVisible = true;
		this.$nextTick(() => {
			this.blackListDialogRef.init([row], 1);
		});
	}

	itemCommand(command: string, row: ApiCustomerList) {
		switch (command) {
			case '加入黑名单':
				this.blackListVisible = true;
				this.$nextTick(() => {
					this.blackListDialogRef.init([row], 1);
				});
				break;
			case "3":
				this.memberUpgrade();
				break;
			case "5":
				this.memberRenews();
				break;
		}
	}

	//获取仓库列表（数据）
	warehouseItem(pageNum: number) {
		// console.log('执行了');
		const form = this.dataForm;
		const param = {
			current: pageNum,
			warehouseFullName: form.storehouseName,
			warehouseNumber: form.storehouseNumber,
			warehouseAddress: form.storehouseAddress,
			size: this.pageSize,
		}
		// 调用接口
		warehouseList(param).then(res => {
			// 仓库数据
			this.wareList = res.data.list
			// 显示数据条数
			this.pageSize = res.data.size;
			// 第几页
			this.pageNum = res.data.current;

			this.total = res.data.total;

			console.log(res.data);
			console.log('存放数据');
			// console.log(this.wareList);
			// console.log(this.wareList.length);
		})
	}

	//获取当前行需要修改的信息
	EditWareBtn(val: any) {
		// this.tEditWarehouse = true;
		// this.EditwareList = [ val ];
		// this.EditwareList1 = JSON.parse(JSON.stringify((this.EditwareList)));
		this.tEditWarehouse = true;
		this.EditwareList = JSON.parse(JSON.stringify(([val])));

		console.log(this.EditwareList, '=================this.EditwareList==============');
	}
	//删除按钮功能模块
	delWareBtn(scope) {
		this.$confirm('此操作将永久删除该仓库, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const delParam = {
				id: scope.id,
			}
			delWarehouse(delParam).then(res => {
				this.$message({
					type: 'success',
					message: res.data
				});
				// console.log('删除成功重新获取一次数据');
				// console.log(this.wareList);
				// console.log(this.wareList.length);
				this.warehouseItem(1);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消删除'
			});
			this.warehouseItem(1);
		});
	}

	//仓库模块的禁用启用
	deactivate(scope) {
		console.log(scope.state)
		console.log(scope.id)
		// this.Disableid = scope.id
		// this.Disablestate = scope.state
		this.$confirm('更改启用状态将停用/启用该仓库，确定继续更改吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			if (scope.state == 0) {
				scope.state = 1
			} else if (scope.state == 1) {
				scope.state = 0
			}
			const statusList = {
				id: scope.id,
				state: scope.state
			};
			stateWarehouse(statusList)
				.then((res) => {
					this.$message.success(res.data);
				})
				.catch(err => {
					this.$message.error(err || "网络错误");
				});
		}).catch(err => {
			this.$message({
				type: 'info',
				message: '已取消操作'
			});
		})
	}

	// steWarehouse(){
	// 	const statusList = {
	// 	id : this.Disableid,
	// 	state : this.Disablestate
	// 	};
	// 	stateWarehouse(statusList)
	// 	.then((res) => {
	// 	  this.$message.success(res.data);
	// 	})
	// 	.catch(err => {
	// 	  this.$message.error(err || "网络错误");
	// 	});
	// }


	setLabel(row: ApiCustomerList) {
		this.labelVisible = true;
		this.$nextTick(() => {
			this.labelDialogRef.init([row]);
		});
	}

	//仓库新增的提交方法
	newWarehouseBtn() {
		const paramNum = {
			warehouseFullName: this.numberValidateForm.houseName,
			warehouseNumber: this.numberValidateForm.houseNumber,
			warehouseAddress: this.numberValidateForm.houseadd
		}
		AddWarehouse(paramNum).then(res => {
			this.$message.success(res.data);
			// 关闭模态框
			this.newWarehouse = false;
			//重置弹出框的数据为空
			this.numberValidateForm = {
				houseNumber: '',
				houseName: '',
				houseadd: ''
			}
			// 提交数据成功，重新获取一次数据进行渲染
			this.warehouseItem(1);
		}).catch(err => {
			this.$message.error(err || "网络错误");
			this.newWarehouse = false;
		});
	}

	//仓库编辑的提交方法
	houseEditSub() {
		console.log(45645);
		const houseEdit = {
			id: this.EditwareList[0].id,
			warehouseNumber: this.EditwareList[0].warehouseNumber,
			warehouseFullName: this.EditwareList[0].warehouseFullName,
			warehouseAddress: this.EditwareList[0].warehouseAddress
		}
		// const houseEdit1 = JSON.parse(JSON.stringify(houseEdit));
		editWarehouse(houseEdit).then(res => {
			this.$message({
				message: res.data,
				type: 'success'
			});
			// 关闭模态框
			this.tEditWarehouse = false;
			this.warehouseItem(this.pageNum);
			// this.warehouseItem(1);
		}).catch(err => {
			// 提交失败返回提示
			this.$message.error(err || res.data);
			this.tEditWarehouse = false;
		});

	}
	// cancelEditSub(){
	//   editWarehouse(this.EditwareList[0]).then(res => {
	//     this.$message({
	//       message: res.data,
	//       type: 'success'
	//     });
	//     // 关闭模态框
	//     this.tEditWarehouse = false;
	//     // this.warehouseItem(1);
	//   }).catch(err => {
	//     // 提交失败返回提示
	//     this.$message.error(err || res.data);
	//     this.tEditWarehouse = false;
	//   });
	//   this.warehouseItem(1);
	// }



	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		console.log(this.total);
		this.pageSize = val;
		this.warehouseItem();
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		console.log(val);
		this.pageNum = val;
		this.warehouseItem(val);
	}
	moWare(row: any) {
		updateStock({ id: row.id, stockFlag: 1 }).then((res) => {
			this.warehouseItem(1);
			// if(bol){
			this.$message.success(`已选择${row.warehouseFullName}为默认仓库`);
			//  }else{
			// 	this.$message.success(`已取消${row.warehouseFullName}为默认仓库`);
			//  }
		}).catch((err) => {
			this.$message.error(err);

		})

	}


}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';
</style>
