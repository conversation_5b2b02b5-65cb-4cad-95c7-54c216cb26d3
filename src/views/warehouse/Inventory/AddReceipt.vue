<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:09
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-12 13:17:51
-->
<template>
  <div class="customer">
    <el-tabs>
      <div class="memberList">
        <div class="line"></div>
        <span style="font-weight:number:400; margin: 10px -110px 0px 90px; display:inline">仓库</span>
        <el-select v-model="id" filterable remote :remote-method="surveyHouse" placeholder="请选择" size="small"
          style="margin: 20px 120px 20px 120px ;">
          <el-option v-for="(item, key) in surveyHouseList" :key="key" :label="item.warehouseFullName" :value="item.id">
          </el-option>
        </el-select>

        <span class="demonstration" style="margin-right: -80px;">入库日期</span>
        <div class="block" style="font-weight:number:400; margin: 10px -110px 0px 90px; display:inline">
          <el-date-picker v-model="TimeAdDate" type="datetime" placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss ">
          </el-date-picker>
        </div>

        <!--          添加模态框-->
        <template>
          <el-table :data="tableData" style="width: 100%" border ref="multipleSelection" max-height="100%">
            <el-table-column type="index" width="50">
            </el-table-column>
            <el-table-column label="" width="80">
              <template slot-scope="scope">
                <el-button @click="handleClick(scope.$index, scope.row)" type="text" size="medium"
                  icon="el-icon-plus"></el-button>
                <el-button type="text" size="medium" @click.prevent="removeDomain(scope.$index)"
                  icon="el-icon-minus"></el-button>
              </template>
            </el-table-column>
            <el-table-column label="商品编码" width="200">
              <template slot-scope="scope">
                <el-select v-model="tableData[scope.row.arrKey].goodsCode" @change="selectInspectType(tableData[scope.row.arrKey].goodsCode,scope.row)" placeholder="请选择" filterable remote
                  :remote-method="dataFilter">
                  <!-- <el-option v-for="(item, key) in library" @click.native="handleSelect(item, scope.row)" :key="key"
                    :label="`${item.goodsCode}.${item.name}.${item.skuStock}`" :value="item.skuId">
                  </el-option> -->
                  <el-option v-for="(item, key) in library" :key="key"
                    :label="`${item.goodsCode}.${item.name}.${item.skuStock}.${item.specs2 || ''}`" :value="item.skuId">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="商品名称" width="180">

            </el-table-column>

            <el-table-column label="入库数量" width="180">
              <template slot-scope="scope">
                <el-input v-model="scope.row.instoreQty" @input="calculationOne(scope.row)"
                  placeholder="请输入数量"></el-input>
              </template>
            </el-table-column>
            <el-table-column  label="实际出库数"  prop="reallyOutStock" width="180">
             
            </el-table-column>
            <el-table-column label="单价" width="180">
              <template slot-scope="scope">
                <el-input v-model="scope.row.skuPrice" @input="calculTwo(scope.row)" placeholder="请输入单价"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="TotalAmount" label="总价" width="180">
            </el-table-column>

            <el-table-column prop="skuStock" label="商品规格" width="180">

            </el-table-column>
            <el-table-column prop="specs2" label="商品规格2" width="180">

</el-table-column>
            <el-table-column label="	单位" width="160">

              <template slot-scope="scope">
                <el-select v-model="tableData[scope.row.arrKey].inUnitId" placeholder="请选择">
                  <el-option v-for="(item, key) in scope.row.productSecUnits" :key="key" :label="item.unit"
                    :value="item.secUnitId">
                  </el-option>
                </el-select>
              </template>

              <!--							</el-input>-->
            </el-table-column>

            <el-table-column prop="address" label="备注" width="160">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remarks" @input="calculThree(scope.row)" placeholder="请输入内容"></el-input>
              </template>
            </el-table-column>
            <!--						<el-table-column-->
            <!--						  prop="address"-->
            <!--						  label="库存下限"-->
            <!--						  width="160">-->
            <!--						</el-table-column>-->
            <!--				    <el-table-column-->
            <!--							prop="address"-->
            <!--				      label="可用库存量"-->
            <!--				      width="140">-->
            <!--				    </el-table-column> -->
          </el-table>
        </template>

        <!-- 入库单号模块 -->
        <el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px"
          style="margin-top: 50px;">
          <el-row>
            <el-col :span="8">
              <el-form-item label="入库单号" prop="ReceiptNo">
                <el-input v-model="ReceiptNo" placeholder="请输入内容"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="入库类型">
                <el-select v-model="itemValue" placeholder="请选择">
                  <el-option v-for="item in WarehousingType" :key="item.itemValue" :label="item.itemText"
                    :value="item.itemValue">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

          </el-row>

          <!-- 入库类型模块 -->
          <el-row>

            <el-col :span="8">
              <el-form-item label="供应商">
                <template slot-scope="scope">
                  <el-select v-model="supplierData" placeholder="请选择" filterable :filter-method="getsupplier">
                    <el-option v-for="item in supplierList" :key="item.language" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </template>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="经办人">
                <template slot-scope="scope">
                  <el-select v-model="Handler" placeholder="请选择" filterable :filter-method="getAccountInfo">
                    <el-option v-for="item in Handlerlist" :key="item.language" :label="item.nikeName" :value="item.id">
                    </el-option>
                  </el-select>
                </template>
              </el-form-item>
            </el-col>

          </el-row>

          <!-- 经办人模块 -->
          <el-row>

            <el-col :span="8">
              <el-form-item label="制单人">
                <el-input v-model="preparer" :disabled="true" placeholder="请输入内容"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="总数量" prop="instoreQtySum">
                <el-input v-model="instoreQtySum" disabled></el-input>
              </el-form-item>
            </el-col>

          </el-row>

          <!-- 数量模块 -->
          <el-row>
            <el-col :span="8">
              <el-form-item label="总金额数量" prop="instoreAmount">
                <el-input v-model="instoreAmountSum" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="16">
              <el-form-item label="备注">
                <el-input type="textarea" placeholder="请输入内容" v-model="remarks" maxlength="200" show-word-limit>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
        <el-button :disabled="disableCommit" type="primary" @click="Submit"
          style="margin: 60px 40px 60px 800px;">提交</el-button>
      </div>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import BlackList, { NewCustomerTagList } from '@/views/customer/common/SetBlackList.vue';
import SetDrop from '@/views/customer/common/SetDrop.vue';
import SetLabel from '@/views/customer/list/components/dialog/SetTags.vue';

import { CustomerListState, ApiCustomerList, CustomerTagList, CustomerRank } from './customerListType';
import { ElForm } from 'element-ui/types/form';

import { UpperAndLowerEdit, ByNameOrNumber, surveyWarehouse, BuyType, ReceiptNoAndUser, getSubmit, getAccountInfo, getsupplier } from '@/api/warehouse/warehouse';
import { ElTable } from 'element-ui/types/table';

Component.registerHooks(["beforeRouteLeave"]);
@Component({
  components: {
    PageManage,
    BlackList,
    SetDrop,
  }
})
export default class Index extends Vue implements CustomerListState {
  @Ref()
  readonly dataFormRef!: ElForm;

  @Ref()
  readonly customerListRef!: ElTable;

  @Ref()
  readonly blackListDialogRef!: BlackList;

  @Ref()
  readonly labelDialogRef!: SetLabel;

  // 新增提交数据表单
  dataForm = {
    storehouseNumber: '',
    storehouseName: '',
    storehouseAddress: '',
    memberNumber: null,
    rankCode: null,
    sortType: 1,
    tagId: ''
  };

  dataList: Array<ApiCustomerList> = [];

  allTagList: Array<CustomerTagList> = [];

  selectionList: Array<ApiCustomerList | NewCustomerTagList> = [];

  multipleSelection: Array<ApiCustomerList | NewCustomerTagList> = [];

  managerVisible = false;

  blackListVisible = false;

  labelVisible = false;

  visible = true;

  //弹出框新建内容  存放数据
  numberValidateForm = {
    houseNumber: '',
    houseName: '',
    houseadd: ''
  };

  //新增模块的头部仓库下拉搜索框的数据
  surveyHouseList = [];

  //新增必填项
  tableData = [{
    arrKey: 0,//dakai
    goodsCode: '',
    name: '',
    skuStock: '',
    skuId: '',
    productId: '',
    instoreQty: '',
    unit: '',
    unitId: '',
    instorePrice: '',
    instoreAmount: '',
    upperLimit: '',
    lowerLimit: '',
    remarks: '',
    productSecUnits: [],
    inUnitId: '',
  }];
  arrKey = 0;

  dynamicItem = [];

  //商品编码的下拉框模块
  library = [];

  // goodsCode = '';
  id = '';
  //日期时间空间获取数据
  TimeAdDate = '';
  //入库单号
  ReceiptNo = '';
  //入库类型
  WarehousingType = [];
  itemValue = '';
  //经办人
  Handlerlist = [];
  Handler = '';
  //备注
  remarks = '';
  //供应商
  supplierList = [];
  supplierData = ''
  //制单人
  preparer = '';
  preparerNumber = '';
  //入库总数量
  instoreQtySum = 0;
  //入库总金额
  instoreAmountSum = 0;



  houseList = {}

  closeTree = false;

  rankOptions: Array<CustomerRank> = [];

  /** 分页条数 */
  pageSize = 10;

  /** 分页页码 */
  pageNum = 1;

  /** 数据长度 */
  total = 0;

  /** 赠送优惠券下拉菜单 */
  couponDropList: any[] = [
    {
      command: '加入黑名单',
      disabled: false,
      show: true,
      text: '加入黑名单'
    }
  ];

  disableCommit = false; // 提交按钮状态
  isOkLeave = false; // 正常提交后的跳转，不再弹出提醒

  mounted() {
    this.surveyHouse();
    this.dataFilter();
    this.buyType();
    this.receiptNoAndUser();
    this.getAccountInfo();
    this.getsupplier();
  }

  beforeRouteLeave(_to: any, _from: any, next: () => void) {
    if (this.isOkLeave) {
      next();
      return;
    }
    this.$confirm(
      `确定退出新增入库页面?退出后，未保存的信息将不会保留!`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    ).then(() => {
      next();
    });
  }
  // 商品编码回车点击事件
  selectInspectType(inspectType,scope){
            console.log("当前巡检类型 ：" + inspectType,this.library,scope);
  let item = this.library.find(items=>inspectType==items.skuId)
            for (let i = 0; i < this.tableData.length; i++) {
      if (item.skuId == this.tableData[i].skuId) {
        this.tableData[scope.arrKey].goodsCode = '';
        this.$message.error("已经存在该商品规格！");
        return
      }
    }
    this.tableData[scope.arrKey].goodsCode = item.goodsCode
    this.tableData[scope.arrKey].name = item.name
    this.tableData[scope.arrKey].skuStock = item.skuStock
    this.tableData[scope.arrKey].specs2 = item.specs2
    this.tableData[scope.arrKey].unit = item.unit
    this.tableData[scope.arrKey].unitId = item.unitId
    this.tableData[scope.arrKey].upperLimit = item.upperLimit
    this.tableData[scope.arrKey].skuId = item.skuId
    // this.tableData[scope.arrKey].instorePrice = item.skuPrice;
    // 我加的s
    this.tableData[scope.arrKey].remarks = item.remarks;
    this.tableData[scope.arrKey].productId = item.productId;

    this.tableData[scope.arrKey].productSecUnits = item.productSecUnits;
    this.tableData[scope.arrKey].reallyOutStock = item.reallyOutStock;
    console.log('商品表单数据');
    // console.log(item.skuPrice);
    console.log('this.tableData[scope.arrKey].instorePrice', this.tableData[scope.arrKey].instorePrice);
    // console.log(scope.arrKey);


     
        }
   // 商品编码点击事件作废
  handleSelect(item, scope) {
    // console.log("这里");
    console.log('============item================',item,scope);
    // console.log(scope);
    for (let i = 0; i < this.tableData.length; i++) {
      if (item.skuId == this.tableData[i].skuId) {
        this.tableData[scope.arrKey].goodsCode = '';
        this.$message.error("已经存在该商品规格！");
        return
      }
    }
    this.tableData[scope.arrKey].goodsCode = item.goodsCode
    this.tableData[scope.arrKey].name = item.name
    this.tableData[scope.arrKey].skuStock = item.skuStock
    this.tableData[scope.arrKey].unit = item.unit
    this.tableData[scope.arrKey].unitId = item.unitId
    this.tableData[scope.arrKey].upperLimit = item.upperLimit
    this.tableData[scope.arrKey].skuId = item.skuId
    // this.tableData[scope.arrKey].instorePrice = item.skuPrice;
    // 我加的s
    this.tableData[scope.arrKey].remarks = item.remarks;
    this.tableData[scope.arrKey].productId = item.productId;

    this.tableData[scope.arrKey].productSecUnits = item.productSecUnits;
    console.log('商品表单数据');
    // console.log(item.skuPrice);
    console.log('this.tableData[scope.arrKey].instorePrice', this.tableData[scope.arrKey].instorePrice);
    // console.log(scope.arrKey);
    // console.log(this.tableDableData[scope.arrKey]);ta);
    // console.log(this.ta
    // console.log(this.tableData[scope.arrKey].skuId);
    //
    // if(this.tableData[scope.arrKey].instorePrice != null){
    //   console.log('单价存在');
    // 		this.tableData[scope.arrKey].instoreAmount  = this.tableData[scope.arrKey].instoreQty * this.tableData[scope.arrKey].instorePrice;
    //   console.log(this.tableData[scope.arrKey].instoreAmount);
    //   console.log('总价诞生');
    // }
    // console.log(this.tableData);
  }

  //商品编码搜索框
  dataFilter(pageNum: string) {
    const param = {
      keyword: pageNum,
      current: "1",
      size: this.pageSize,
    }
    ByNameOrNumber(param).then(res => {
      this.library = res.data.list
    })
    console.log(this.library);
  }
  //仓库下拉框
  surveyHouse(pageNum: number) {
    const param = {
      keyword: pageNum,
      current: "1",
      size: 100,
    }
    surveyWarehouse(param).then(res => {
      // console.log('获取成功！！！！！');
      // console.log(res.data);
      // console.log('res.data.list',res.data.list);
      this.surveyHouseList = res.data.list;
      // console.log(this.surveyHouseList[0].warehouseFullName);
    }).catch(err => {
      // console.log(15465465456465465455);
      console.log(err);
    })
  }

  //入库类型下拉框
  buyType() {
    BuyType().then(res => {
      this.WarehousingType = res.data;
    })
  }

  //获取入库单号和制单人
  receiptNoAndUser() {
    ReceiptNoAndUser().then(res => {
      this.preparer = res.data.preparerName
      this.preparerNumber = res.data.preparerId
      this.ReceiptNo = res.data.buyNo
    })
  }

  //获取经办人
  getAccountInfo(pageNum: number) {
    const param = {
      keyword: pageNum,
      current: "1",
      size: this.pageSize,
    }
    getAccountInfo(param).then(res => {
      this.Handlerlist = res.data.list
    })
  }

  //获取供应商
  getsupplier(pageNum: number) {
    const param = {
      keyword: pageNum,
    }
    getsupplier(param).then(res => {
      this.supplierList = res.data.list
    });
  }

  //计算总价
  calculationOne(scope) {

    let reg = /[0-9]$/;
    //点出现的次数
    let spotNumber = 0;
    //点后面的小数
    let spotNum = 0;
    let bool = "float";
    // let reg = /^((?!0)\d{1,8}|*********)$/;
    let negative = false;
    if (scope.instoreQty.charAt(0) == '-') {
      negative = true;
    }
    let strArray = negative ? (scope.instoreQty.replace("-", "")).split("") : scope.instoreQty.split("");
    console.log(strArray);
    // alert(strArray)
    let newStrs = "";
    let strs = "";
    let strsNumber = 0;
    for (let i = 0; i < strArray.length; i++) {
      //限制输入的为八位数，小数点后可以为4位数
      strs = strs + strArray[i];
      strsNumber = parseInt(strs);
      if (bool == "true" && strs.indexOf(".")[0] > *********) {
        //console.log(newStrs,'====rag============111===========',strs)
        scope.instoreQty = newStrs;
        break
      } else if (strsNumber > *********) {
        //console.log(newStrs,'====rag============222222===========',strs)
        scope.instoreQty = newStrs;
        break
      }
      //当有小数点时，进行判断。用于限制小数点后的数量
      if (reg.test(strArray[i])) {
        console.log('=========fen===========', strArray[i]);
        if (strArray[i] === ".") {
          spotNumber = spotNumber + 1
          if (spotNumber > 1) {
            scope.instoreQty = newStrs;
            break
          }
          bool = "true"
        }
        newStrs += strArray[i];
      } else if (i > 0 && strArray[i] === "0") {
        newStrs += strArray[i];
      }
      //小数点后数量为4位后不给添加
      if (bool == "true") {
        spotNum = spotNum + 1
        if (spotNum > 4) {
          scope.instoreQty = newStrs;
          break
        }
      }
    }
    scope.instoreQty = negative ? ('-' + newStrs) : newStrs
    this.tableData[scope.arrKey].instoreQty = scope.instoreQty;

    // console.log(scope.skuPrice)
    // console.log(scope.instoreQty)
    // console.log(scope.TotalAmount)
    scope.TotalAmount = scope.instoreQty * scope.skuPrice;
    this.tableData[scope.arrKey].instoreAmount = scope.TotalAmount
    this.tableData[scope.arrKey].instoreQty = scope.instoreQty
    this.tableData[scope.arrKey].instorePrice = scope.skuPrice
    let qtySum = 0
    let amountSum = 0
    let num1 = 0
    let num2
    for (let i = 0; i < this.tableData.length; i++) {
      num1 = Number(this.tableData[i].instoreQty)
      num2 = Number(this.tableData[i].instoreAmount)
      qtySum += num1
      amountSum += num2
    }
    this.instoreQtySum = qtySum
    this.instoreAmountSum = amountSum
  }
  //单价
  calculTwo(scope) {
    let reg = /[0-9]|\.$/;
    //点出现的次数
    let spotNumber = 0;
    //点后面的小数
    let spotNum = 0;
    let bool = "float";
    // let reg = /^((?!0)\d{1,8}|*********)$/;
    let strArray = scope.skuPrice.split("");

    // alert(strArray)
    let newStrs = "";
    let strs = "";
    let strsNumber = 0;
    for (let i = 0; i < strArray.length; i++) {
      //限制输入的为八位数，小数点后可以为4位数
      strs = strs + strArray[i];
      strsNumber = parseInt(strs);
      if (bool == "true" && strs.indexOf(".")[0] > *********) {
        //console.log(newStrs,'====rag============111===========',strs)
        scope.skuPrice = newStrs;
        break
      } else if (strsNumber > *********) {
        //console.log(newStrs,'====rag============222222===========',strs)
        scope.skuPrice = newStrs;
        break
      }
      //当有小数点时，进行判断。用于限制小数点后的数量
      if (reg.test(strArray[i])) {
        console.log('=========fen===========', strArray[i]);
        if (strArray[i] === ".") {
          spotNumber = spotNumber + 1
          if (spotNumber > 1) {
            scope.skuPrice = newStrs;
            break
          }
          bool = "true"
        }
        newStrs += strArray[i];
      } else if (i > 0 && strArray[i] === "0") {
        newStrs += strArray[i];
      }
      //小数点后数量为4位后不给添加
      if (bool == "true") {
        spotNum = spotNum + 1
        if (spotNum > 4) {
          scope.skuPrice = newStrs;
          break
        }
      }
    }
    scope.skuPrice = newStrs
    this.tableData[scope.arrKey].instorePrice = scope.skuPrice;
    // console.log('单价',scope.skuPrice)
    // console.log('数量',scope.instoreQty)
    scope.TotalAmount = scope.instoreQty * scope.skuPrice;
    this.tableData[scope.arrKey].instoreAmount = scope.TotalAmount
    this.tableData[scope.arrKey].instoreQty = scope.instoreQty
    this.tableData[scope.arrKey].instorePrice = scope.skuPrice
    // console.log('总价',scope.TotalAmount);
    let qtySum = 0
    let amountSum = 0
    let num1 = 0
    let num2
    for (let i = 0; i < this.tableData.length; i++) {
      num1 = Number(this.tableData[i].instoreQty)
      num2 = Number(this.tableData[i].instoreAmount)
      qtySum += num1
      amountSum += num2
    }
    this.instoreQtySum = qtySum
    this.instoreAmountSum = amountSum
  }
  calculThree(scope) {
    console.log(scope);
  }

  //动态增加 新增模态框的行
  handleClick(scope) {
    // console.log(111);
    // console.log(scope);
    // this.tableData.push({
    // 	goodsCode: '',
    //   key: Date.now()
    //   });
    this.arrKey++
    this.tableData.push({
      arrKey: this.arrKey,
      goodsCode: '',
      name: '',
      skuStock: '',
      instoreQty: '1',
      unit: '',
      skuPrice: '',
      upperLimit: '',
      lowerLimit: '',
      remarks: '',
      inUnitId: '',
    });
    // console.log('动态添加！！！！！！！！1111111111111111111');
    // console.log(this.tableData);
  }
  //动态删除
  removeDomain(index) {
    //当只有一行数据的时候不允许删除
    if (this.tableData.length == 1) {
      return
    }
    this.arrKey--//你找找有没有方法，删除对应下标的元素不改变下标的方法 只能说百度了
    console.log("删除：" + index)
    for (var i = index + 1; i < this.tableData.length; i++) {
      this.tableData[i].arrKey--;
      console.log(i)
    }
    this.tableData.splice(index, 1)

    console.log(this.tableData)

    // console.log('总价',scope.TotalAmount);
    let qtySum = 0
    let amountSum = 0
    let num1 = 0
    let num2
    for (let i = 0; i < this.tableData.length; i++) {
      num1 = Number(this.tableData[i].instoreQty)
      num2 = Number(this.tableData[i].instoreAmount)
      qtySum += num1
      amountSum += num2
    }
    this.instoreQtySum = qtySum
    this.instoreAmountSum = amountSum
  }

  //提交模块
  Submit() {
    const param = {
      warehouseId: this.id,
      buyDate: this.TimeAdDate,
      buyNo: this.ReceiptNo,
      buyType: this.itemValue,
      handlerId: this.Handler,
      preparerId: this.preparerNumber,
      supplierId: this.supplierData,
      remarks: this.remarks,
      productBuyInItemList: this.tableData,
      buyQuantity: this.instoreQtySum,
      buyAmount: this.instoreAmountSum
    }
    console.log('dfsdfsdf===============', param.productBuyInItemList);
    for (let i = 0; i < param.productBuyInItemList.length; i++) {
      if (param.productBuyInItemList[i].skuId == '' || param.productBuyInItemList[i].skuId == '0') {
        this.$message.error("还未选择商品规格！");
        return
      }
      if (param.productBuyInItemList[i].instorePrice == '') {
        this.$message.error("商品单价不能为空！");
        return
      }
      if (param.productBuyInItemList[i].instoreQty == '' || param.productBuyInItemList[i].instoreQty == '0') {
        this.$message.error("商品数量不能为空或者为0！");
        return
      }

    }
    if (this.id == '') {
      this.$message.error("仓库不能为空！");
      return
    } else if (this.TimeAdDate == '') {
      this.$message.error("入库日期不能为空！");
      return
    } else if (this.ReceiptNo == '') {
      this.$message.error("入库单号不能为空！");
      return
    } else if (this.itemValue == '') {
      this.$message.error("入库类型不能为空！");
      return
    } else if (this.Handler == '') {
      this.$message.error("经办人不能为空！");
      return
    } else if (this.preparerNumber == '') {
      this.$message.error("制单人不能为空！");
      return
    } else if (this.supplierData == '') {
      this.$message.error("供应商不能为空！");
      return
    }

    this.disableCommit = true

    console.log('提交表单数据');
    // console.log(param.productBuyInItemList.skuId);
    console.log('this.tableDate', this.tableData);
    getSubmit(param).then(res => {
      this.$message({
        message: res.data,
        type: 'success'
      });
      this.isOkLeave = true
      this.newly()
    }).catch(err => {
      this.$message.error(err || "网络错误");
      this.disableCommit = false
    });
    // console.log(this.tableData)
    // console.log(this.id)
    // console.log(this.TimeAdDate)
    // console.log(this.ReceiptNo)
    // console.log(this.remarks)
    // console.log(this.itemValue)
    // console.log(this.Handler)
    // console.log(this.preparerNumber)
    // console.log(this.supplierData)

  }
  //跳轉查詢
  newly() {
    this.$router.push({
      name: "inventory",
    });
  }

  /**
   * @method handleSizeChange
   * @description 每页 条
   */
  handleSizeChange(val: number) {
    this.pageSize = val;
    // this.warehouseItem(1);
  }

  /**
   * @method handleCurrentChange
   * @description 当前页
   */
  handleCurrentChange(val: number) {
    this.pageNum = val;
    // this.warehouseItem(val);
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';
</style>
