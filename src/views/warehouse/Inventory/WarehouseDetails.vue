<!--
 * @description: 购货入库列表
 * @Author: chuy<PERSON><PERSON>
 * @Date: 2021-08-20 17:29:09
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-12 13:17:51
-->
<template>
	<div class="customer">
		<el-tabs>
			<div class="memberList">
				<div class="line"></div>
				<m-card class="form2" :needToggle="true">
					<el-form class="customer__dataForm" ref="dataFormRef" :model="paramDetailed" label-width="100px">
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="入库单号"><el-input v-model="paramDetailed.buyNo" clearable
										placeholder="请输入入库单号" /></el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="入库日期">
									<el-date-picker v-model="paramDetailed.date" style="width: 100%;" type="daterange"
										range-separator="~" start-placeholder="开始日期" end-placeholder="结束日期"
										value-format="yyyy-MM-dd">
									</el-date-picker>
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="仓库名称">
									<el-input v-model="paramDetailed.warehouseName" clearable placeholder="请输入仓库名称" />
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="入库类型">
									<el-select v-model="paramDetailed.buyType" clearable placeholder="请选择"
										style="width: 100%;">
										<el-option v-for="itemtwo in category" :key="itemtwo.itemValue"
											:label="itemtwo.description" :value="itemtwo.itemValue">
										</el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<!-- <el-form-item label="上次交易时间">
										<el-date-picker
											v-model="dataForm.orderSuccessTime"
											:default-time="['00:00:00', '23:59:59']"
											value-format="yyyy-MM-dd HH:mm:ss"
											style="width: 256px"
											type="daterange"
											range-separator="-"
											start-placeholder="开始日期"
											end-placeholder="结束日期"
										></el-date-picker>
									</el-form-item> -->
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="商品名称">
									<el-input v-model="paramDetailed.productName" clearable placeholder="请输入商品名称" />
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="规格">
									<el-input v-model="paramDetailed.specs" clearable placeholder="请输入规格" />
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="制单人">
									<el-input v-model="paramDetailed.createUserName" clearable placeholder="请输入制单人" />
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="供应商名">
									<el-input v-model="paramDetailed.supplierName" clearable placeholder="请输入供应商名" />
								</el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="备注">
									<el-input type="textarea" :rows="2" v-model="paramDetailed.remarks"
										placeholder="请输入备注" />
								</el-form-item>
							</el-col>
						</el-row>
						<el-button type="primary" style="margin-left:100px" @click="warehouseItem2">搜索</el-button>
					</el-form>
				</m-card>

				<div class="topLine">
					<div class="topLine__left" style="margin-left: 30px;">
						<el-button type="primary" @click="exportData">导出列表</el-button>
					</div>
				</div>
				<!-- <el-button v-if="addButton || isSupper" type="primary" icon="el-icon-circle-plus-outline" @click="newly" style="margin-bottom:20px; float:right; margin-right: 60px;">
						新增
						</el-button> -->
				<!-- 新增仓库模块 -->
				<!-- 						<el-dialog
						  title="新建信息"
						  :visible.sync="newWarehouse"
						  width="30%"> -->
				<!-- <template v-for="item in wareList" :key="item.tagId"> -->
				<!-- 						  <el-form :model="numberValidateForm" ref="numberValidateForm" label-width="100px">
								<el-form-item
											label="仓库编号"
											prop="houseNumber"
											:rules="[
								      { required: true, message: '仓库编号不能为空'}
												]">
								    <el-input v-model="numberValidateForm.houseNumber"></el-input>
								  </el-form-item>
								<el-form-item
								    label="仓库名称"
								    prop="houseName"
								    :rules="[
								      { required: true, message: '仓库名称不能为空'}
												]"
									>
									<el-input type="age" v-model="numberValidateForm.houseName" autocomplete="off"></el-input>
								</el-form-item>
									<el-form-item label="仓库地址" >
									    <el-input v-model="numberValidateForm.houseadd"></el-input>
									  </el-form-item>
								</el-form> -->
				<!-- </template> -->
				<!-- <span slot="footer" class="dialog-footer">
						    <el-button @click="newWarehouse = false">取 消</el-button>
						    <el-button type="primary" @click="newWarehouseBtn()">确 定</el-button>
						  </span>
						</el-dialog> -->
				<!-- 仓库列表 -->
				<template>
					<el-table :data="wareList2" style="width: 100%" border max-height="100%">
						<el-table-column type="selection" width="55">
						</el-table-column>
						<el-table-column prop="buyNo" label="单号" width="160">
						</el-table-column>
						<el-table-column prop="buyDate" label="入库日期" width="160">
						</el-table-column>
						<el-table-column prop="warehouse" label="所属仓库" width="120">
						</el-table-column>
						<el-table-column prop="buyTypeName" label="类型" width="120">
						</el-table-column>
						<el-table-column prop="preparerName" label="制单人" width="120">
						</el-table-column>
						<el-table-column prop="supplierName" label="供应商名称" width="120">
						</el-table-column>
						<!-- <el-table-column
              prop="instoreQty"
              label="入库数量"
              width="110">
            </el-table-column>
						<el-table-column
						  prop="instoreAmount"
						  label="入库金额"
						  width="120">
						</el-table-column> -->

						<el-table-column prop="productName" label="商品" width="220">
						</el-table-column>
						<el-table-column prop="skuSpecs" label="规格" width="110">
						</el-table-column>
						<el-table-column prop="specs2" label="商品规格2" width="110">
						</el-table-column>
						<el-table-column prop="unit" label="单位" width="110">
						</el-table-column>
						<el-table-column prop="instoreQty" label="入库数量" width="110">
						</el-table-column>
						<el-table-column prop="instorePrice" label="入库单价" width="110">
						</el-table-column>
						<el-table-column prop="instoreAmount" label="入库金额" width="110">
						</el-table-column>
						<el-table-column prop="itemRemarks" label="明细备注" width="240">
						</el-table-column>

						<el-table-column prop="remarks" label="备注" :show-overflow-tooltip="true" width="240">
						</el-table-column>

						<!-- <el-table-column
				      prop="productBuyInId"
				      label="编号"
				      width="160">
				    </el-table-column> -->


						<!-- 				    <el-table-column
				      label="状态"
				      width="140">
							<template slot-scope="scope">
							<template v-if="scope.row.state == 0">
								<span>启用</span>
							</template>
<template v-if="scope.row.state == 1">
								<span>停用</span>
							</template>
</template>
</el-table-column> -->
						<!-- <el-table-column
							fixed="right"
				      label="操作"
				      width="90">
				      <template slot-scope="scope">
				        <el-button type="text" size="medium" icon="el-icon-caret-right" @click="EditWareBtn(scope.row)"></el-button>
                                <el-button type="text" size="medium" icon="el-icon-delete-solid" @click="delWareBtn(scope.row)"></el-button>
				      </template>
				    </el-table-column> -->
					</el-table>
				</template>

				<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
				<!-- <black-list ref="blackListDialogRef" v-if="blackListVisible" @refreshDataList="warehouseItem" />
					<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList" @refreshDataList="warehouseItem(1)" />
					<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList" @refreshDataList="warehouseItem(1)" /> -->
			</div>

		</el-tabs>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import BlackList, { NewCustomerTagList } from '@/views/customer/common/SetBlackList.vue';
import SetDrop from '@/views/customer/common/SetDrop.vue';
import SetLabel from '@/views/customer/list/components/dialog/SetTags.vue';

import { CustomerListState, ApiCustomerList, CustomerTagList, CustomerRank, ApiMemberCardList } from '../customerListType';
import { ElForm } from 'element-ui/types/form';

import { AddWarehouse, editWarehouse, delWarehouse, stateWarehouse, GoodsWarehousing, lookupWarehouse, BuyType, delProductBuyIn, productBuyInDetailed, exportProductBuyInDetail } from '@/api/warehouse/warehouse';
import { ElTable } from 'element-ui/types/table';

@Component({
	components: {
		PageManage,
		BlackList,
		SetDrop,
	}
})
export default class Index extends Vue implements CustomerListState {
	@Ref()
	readonly dataFormRef!: ElForm;

	@Ref()
	readonly customerListRef!: ElTable;

	@Ref()
	readonly blackListDialogRef!: BlackList;

	@Ref()
	readonly labelDialogRef!: SetLabel;

	dataForm = {
		storehouseNumber: '',
		storehouseName: '',
		storehouseAddress: '',
		memberNumber: null,
		rankCode: null,
		sortType: 1,
		tagId: ''
	};

	dataList: Array<ApiCustomerList> = [];

	allTagList: Array<CustomerTagList> = [];

	selectionList: Array<ApiCustomerList | NewCustomerTagList> = [];

	multipleSelection: Array<ApiCustomerList | NewCustomerTagList> = [];

	managerVisible = false;

	blackListVisible = false;

	labelVisible = false;

	visible = true;

	//仓库新增弹出框
	newWarehouse = false;

	//弹出框新建内容
	numberValidateForm = {
		houseNumber: '',
		houseName: '',
		houseadd: ''
	}

	houseList = {}

	rankOptions: Array<CustomerRank> = [];

	/** 分页条数 */
	pageSize = 10;

	/** 分页页码 */
	pageNum = 1;

	/** 数据长度 */
	total = 0;

	/** 赠送优惠券下拉菜单 */
	couponDropList: any[] = [
		{
			command: '加入黑名单',
			disabled: false,
			show: true,
			text: '加入黑名单'
		}
	];

	//搜索模块
	wareList = [];

	Disableid = '';

	Disablestate = '';

	warehouseList = {
		warehouseNumber: '',
		warehouseAddress: '',
		warehouseFullName: ''
	};

	OddNumbers = '';

	value1 = '';

	//仓库查询
	library = [];
	id = '';
	//类型查询
	category = [];
	itemValue = '';

	startDate = '';

	endDate = '';

	EditwareList = [];

	addButtonCode = "Inventory.add";

	menuName = "入库明细";

	addButton = false;

	buttonList = [];

	isSupper = 0;

	wareList2 = [];

	paramDetailed: any = {
		productName: '',
		specs: '',
		buyNo: '',
		warehouseName: '',
		buyType: '',
		startDate: '',
		endDate: '',
		remarks: '',
		createUserName: '',
		supplierName: '',
		date: []
	}

	mounted() {
		this.warehouseItem(1);
		this.dataFilter();
		this.buyType();
		this.buttonAuth();

		this.productBuyInDetailed();
	}

	productBuyInDetailed() {
		const param = {
			current: this.pageNum,
			size: this.pageSize,
		}
		productBuyInDetailed(param).then(res => {
			this.wareList2 = res.data.list;
			this.total = res.data.total;
			console.log('this.wareList2=', this.wareList2);
		}).catch(err => {
			console.log(err);
		});
	}

	//获取/搜索入库列表
	warehouseItem2() {
		if (this.paramDetailed.date) {
			this.paramDetailed.startDate = this.paramDetailed.date[0];
			this.paramDetailed.endDate = this.paramDetailed.date[1];
		} else {
			this.paramDetailed.startDate = '';
			this.paramDetailed.endDate = '';
		}

		const param = {
			current: this.pageNum,
			size: this.pageSize,
			...this.paramDetailed
		}
		console.log("param=", param);
		productBuyInDetailed(param).then(res => {
			this.wareList2 = res.data.list;
			this.total = res.data.total;
			console.log('this.wareList2=', this.wareList2);
		}).catch(err => {
			console.log(err);
		});
	}

	buttonAuth() {
		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var addButtonData = buttonList.find(e => e == this.addButtonCode);

		if (addButtonData != null && addButtonData != undefined) {
			this.addButton = true;
		}
	}

	getMemberList(data) {
		console.log(data);
	}

	batchCouponClick() {
		if (this.multipleSelection.length > 0) {
			this.labelVisible = true;
			this.$nextTick(() => {
				this.labelDialogRef.init(this.multipleSelection as ApiCustomerList[]);
			});
		} else {
			this.$message.warning('至少选择一个客户');
		}
	}

	batchCouponCommand(command: string) {
		if (this.multipleSelection.length > 0) {
			switch (command) {
				case '加入黑名单':
					this.blackListVisible = true;
					this.$nextTick(() => {
						this.blackListDialogRef.init(this.multipleSelection as ApiCustomerList[], 1);
					});
					break;
			}
		} else {
			this.$message.info('请至少选择一个客户');
		}
	}

	itemClick(row: ApiCustomerList) {
		this.blackListVisible = true;
		this.$nextTick(() => {
			this.blackListDialogRef.init([row], 1);
		});
	}

	itemCommand(command: string, row: ApiCustomerList) {
		switch (command) {
			case '加入黑名单':
				this.blackListVisible = true;
				this.$nextTick(() => {
					this.blackListDialogRef.init([row], 1);
				});
				break;
			case "3":
				this.memberUpgrade();
				break;
			case "5":
				this.memberRenews();
				break;
		}
	}

	//获取/搜索入库列表
	warehouseItem(pageNum: number) {
		console.log(this.id)
		this.startDate = this.value1[0];
		this.endDate = this.value1[1];
		const form = this.dataForm;
		console.log(form);
		console.log(this.dataForm);
		const param = {
			current: pageNum,
			startDate: this.startDate,
			endDate: this.endDate,
			warehouseId: this.id,
			buyNo: this.OddNumbers,
			buyType: this.itemValue,
			size: this.pageSize,
		}

		GoodsWarehousing(param).then(res => {
			this.wareList = res.data.list;
			console.log("wareList=", this.wareList);
			this.pageSize = res.data.size;
			this.pageNum = res.data.current;
			this.total = res.data.total;
			console.log('库存========', res.data.total);

		}).catch(err => {
			console.log(err);
		})
	}

	//查询仓库
	dataFilter(pageNum: number) {
		const param = {
			keyword: pageNum,
			current: "1",
			size: this.pageSize,
		}
		lookupWarehouse(param).then(res => {
			console.log("===========res==============" + res);
			this.library = res.data
		})
		//console.log(this.library)
		//console.log(this.value)
	}

	//查询类型
	buyType() {
		BuyType().then(res => {
			this.category = res.data
			console.log(this.category)
		})

	}

	//获取当前行需要修改的信息
	EditWareBtn(val) {
		this.tEditWarehouse = true
		this.EditwareList = [val]
		console.log(this.EditwareList[0].id)
		this.$router.push({
			name: "Detailed",
			query: {
				id: this.EditwareList[0].id,
			},
		});
	}

	//删除按钮功能模块
	delWareBtn(scope: any) {
		this.$confirm('确定要删除此入库单吗, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const delParam = {
				id: scope.id,
			}
			delProductBuyIn(delParam).then(res => {
				this.$message({
					type: 'success',
					message: res.msg
				});
				this.warehouseItem(1);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消删除'
			});
		});
	}

	// steWarehouse(){
	// 	const statusList = {
	// 	id : this.Disableid,
	// 	state : this.Disablestate
	// 	};
	// 	stateWarehouse(statusList)
	// 	.then((res) => {
	// 	  this.$message.success(res.data);
	// 	})
	// 	.catch(err => {
	// 	  this.$message.error(err || "网络错误");
	// 	});
	// }


	setLabel(row: ApiCustomerList) {
		this.labelVisible = true;
		this.$nextTick(() => {
			this.labelDialogRef.init([row]);
		});
	}

	//仓库新增的提交方法
	newWarehouseBtn() {
		const paramNum = {
			warehouseFullName: this.numberValidateForm.houseNumber,
			warehouseNumber: this.numberValidateForm.houseName,
			warehouseAddress: this.numberValidateForm.houseadd
		}
		AddWarehouse(paramNum).then(res => {
			this.$message.success(res.data);
			// this.warehouseItem()
		}).catch(err => {
			this.$message.error(err || "网络错误");
		});
	}


	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.pageSize = val;
		// this.warehouseItem(1);
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		console.log(val);
		this.pageNum = val;
		this.productBuyInDetailed();
	}

	//新增跳转页面
	// newly(){
	// 	return this.$router.push({
	// 	  name: 'AddReceipt',
	// 	});
	// }
	newly() {
		this.$router.push({
			name: "AddReceipt",
		});
	}

	// 导出数据
	async exportData() {
		this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const params = {
				buyNo: this.paramDetailed.buyNo,
				warehouseName: this.paramDetailed.warehouseName,
				buyType: this.paramDetailed.buyType,
				productName: this.paramDetailed.productName,
				specs: this.paramDetailed.specs,
				createUserName: this.paramDetailed.createUserName,
				supplierName: this.paramDetailed.supplierName,
				remarks: this.paramDetailed.remarks,
				startDate: this.paramDetailed.startDate,
				endDate: this.paramDetailed.endDate,
			};

			exportProductBuyInDetail(params).then((res) => {
				var blob = new Blob([res.data], {
					type: "application/x-msdownload;charset=UTF-8",
				});
				// 创建一个blob的对象链接
				const url = window.URL.createObjectURL(blob);
				const link = document.createElement('a');
				link.href = url;
				// 把获得的blob的对象链接赋值给新创建的这个 a 链接
				let now = new Date();
				let timestamp = now.getTime();
				link.setAttribute('download', '入库明细列表_' + timestamp + '.xls'); // 设置下载文件名
				document.body.appendChild(link);

				// 触发下载
				link.click();
				// 清理
				document.body.removeChild(link);
				window.URL.revokeObjectURL(url);
				this.$message.success('导出成功');

			}).catch((err) => {
				this.$message.error("导出失败");
			});
		}).catch(() => {
			// 用户取消导出
		});
	}

}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.topLine {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;

	&__left {
		display: flex;
	}

	&__right {
		width: 100px;
		display: flex;
		justify-content: space-around;
		margin-left: 82%;
	}
}
</style>
