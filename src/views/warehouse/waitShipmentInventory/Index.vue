<template>
    <div class="customer">
        <el-tabs>
            <div class="memberList">
                <div class="line"></div>
                <m-card class="form2" :needToggle="true">
                    <el-form class="customer__dataForm" ref="dataFormRef" :model="search" label-width="100px">
                        <el-row :gutter="40">
                            <el-col :span="10">
                                <el-form-item label="商家名称">
                                    <el-input v-model="search.shopName" clearable placeholder="请输入商家名称" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="10">
                                <el-form-item label="客户名称">
                                    <el-input v-model="search.nikeName" clearable placeholder="请输入客户名称" />
                                </el-form-item>
                            </el-col>
                            <el-col :span="10">
                                <el-form-item label="商品名称">
                                    <el-input v-model="search.productName" clearable placeholder="请输入商品名称" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-button type="primary" style="margin-left:100px" @click="searchData">搜索</el-button>
                    </el-form>
                </m-card>
                <div class="topLine">
                    <div class="topLine__left" style="margin-left: 30px;">
                        <el-button type="primary" @click="exportData">导出列表</el-button>
                    </div>
                </div>
                <template>
                    <el-table :data="dataList" style="width: 100%" border max-height="100%" show-summary
                        :summary-method="getSummaries">
                        <el-table-column type="selection" width="55">
                        </el-table-column>
                        <el-table-column prop="shopName" label="商家名称" width="160">
                        </el-table-column>
                        <el-table-column prop="nikeName" label="客户名称" width="160">
                        </el-table-column>
                        <el-table-column prop="productName" label="商品名称" width="400">
                        </el-table-column>
                        <el-table-column prop="specs" label="商品规格" width="160">
                        </el-table-column>
                        <el-table-column prop="deliveryQuantity" label="已发货数量" width="160">
                        </el-table-column>
                        <el-table-column prop="unDeliveryQuantity" label="未发货数量" width="160">
                        </el-table-column>
                    </el-table>
                </template>
                <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
                    @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
            </div>
        </el-tabs>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import { getWaitShipmentInventory, exportWaitShipmentInventory } from '@/api/order/index';
@Component({
    components: {
        PageManage
    }
})
export default class Index extends Vue {

    /** 分页条数 */
    pageSize = 10;

    /** 分页页码 */
    pageNum = 1;

    /** 数据长度 */
    total = 0;

    search = {
        shopName: "",
        nikeName: "",
        productName: ""
    }
    dataList = []
    mounted() {
        this.getWaitShipmentInventory();
    }
    searchData() {
        this.pageNum = 1
        this.getWaitShipmentInventory()
    }
    /**
 * @method handleSizeChange
 * @description 每页 条
 */
    handleSizeChange(val: number) {
        this.pageSize = val;
        this.pageNum = 1
        this.getWaitShipmentInventory()
    }

    /**
     * @method handleCurrentChange
     * @description 当前页
     */
    handleCurrentChange(val: number) {
        this.pageNum = val;
        this.getWaitShipmentInventory();
    }
    exportData() {
        this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            // 获取搜索参数
            const searchParams = { ...this.search };

            exportWaitShipmentInventory(searchParams).then((res) => {
                var blob = new Blob([res.data], {
                    type: "application/x-msdownload;charset=UTF-8",
                });
                // 创建一个blob的对象链接
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                // 把获得的blob的对象链接赋值给新创建的这个 a 链接
                let now = new Date();
                let timestamp = now.getTime();
                link.setAttribute('download', '待发货库存_' + timestamp + '.xls'); // 设置下载文件名
                document.body.appendChild(link);

                // 触发下载
                link.click();
                // 清理
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                this.$message.success('导出成功');
            }).catch((err) => {
                this.$message.error("导出失败: " + err);
            });
        }).catch(() => {
            // 用户取消导出
        });
    }
    getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
            if (index === 0) {
                sums[index] = '合计';
                return;
            }
            //判断字段为payAmount或者productQuantity才进行合计 并且处理小数点位数
            if (column.property === 'deliveryQuantity' || column.property === 'unDeliveryQuantity') {
                const values = data.map(item => Number(item[column.property]));
                const total = values.reduce((prev, curr) => {
                    const value = Number(curr);
                    if (!isNaN(value)) {
                        return prev + curr;
                    }
                    return prev;
                }, 0);
                const totalStr = total.toFixed(2);
                sums[index] = total;
            }
        });

        return sums;
    }
    getWaitShipmentInventory() {
        const param = {
            current: this.pageNum,
            size: this.pageSize,
            ...this.search
        }
        getWaitShipmentInventory(param).then(res => {
            this.dataList = res.data.list;
            this.total = res.data.total;
        }).catch(err => {
            console.log(err);
        });
    }
}
</script>
<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.topLine {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    &__left {
        display: flex;
    }

    &__right {
        width: 100px;
        display: flex;
        justify-content: space-around;
        margin-left: 82%;
    }
}
</style>