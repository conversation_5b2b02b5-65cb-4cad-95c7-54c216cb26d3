<!--
 * @description: 平台用户管理
-->
<template>
	<div class="customer">
		<el-tabs>
			<div class="memberList">
				<div class="line"></div>
				<m-card class="form" :needToggle="true">
					<el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="门店编号"><el-input v-model="dataForm.storeNumber" clearable
										placeholder="请输入门店编号" /></el-form-item>
							</el-col>
							<el-col :span="10">
								<el-form-item label="门店全称"><el-input v-model="dataForm.storeFullName" clearable
										placeholder="请输入门店全称" /></el-form-item>
							</el-col>
						</el-row>

						<el-button type="primary" style="margin-left:100px" @click="searchQuery(1)">搜索</el-button>
					</el-form>
				</m-card>
				<el-button type="primary" icon="el-icon-circle-plus-outline" @click="openAdd()"
					v-if="isSupper || addButton" style="margin-bottom:20px; float:right; margin-right: 60px;">
					新增
				</el-button>
				<el-dialog title="新增门店" :visible.sync="addUserFlag" width="30%">
					<el-form :model="addForm" ref="addForm" label-width="100px">
						<el-form-item label="门店编号" prop="storeNumber" :rules="[
							{ required: true, message: '门店编号不能为空' }
						]">
							<el-input v-model="addForm.storeNumber" placeholder="请输入门店编号"></el-input>
						</el-form-item>
						<el-form-item label="门店全称" prop="storeFullName" :rules="[
							{ required: true, message: '门店全称不能为空' }
						]">
							<el-input v-model="addForm.storeFullName" placeholder="请输入门店全称"></el-input>
						</el-form-item>
						<el-form-item label="状态" prop="statusId" :rules="[
							{ required: true, message: '门店全称不能为空' }
						]">
							<el-select v-model="addForm.statusId" placeholder="请选择">
								<el-option v-for="item in statusList" :key="item.value" :label="item.key"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button @click="addUserFlag = false;">取 消</el-button>
						<el-button type="primary" @click="addHandler()" :loading="loading">确 定</el-button>
					</span>
				</el-dialog>
				<!-- 编辑用户模块 -->
				<el-dialog title="编辑用户" :visible.sync="editUserFlag" width="30%">
					<el-form :model="editForm" ref="editForm" label-width="100px">
						<el-form-item label="门店编号" prop="storeNumber" :rules="[
							{ required: true, message: '门店编号不能为空' }
						]">
							<el-input v-model="editForm.storeNumber" placeholder="请输入门店编号"></el-input>
						</el-form-item>
						<el-form-item label="门店全称" prop="storeFullName" :rules="[
							{ required: true, message: '门店全称不能为空' }
						]">
							<el-input v-model="editForm.storeFullName" placeholder="请输入门店全称"></el-input>
						</el-form-item>
						<el-form-item label="状态" prop="statusId" :rules="[
							{ required: true, message: '门店全称不能为空' }
						]">
							<el-select v-model="editForm.statusId" placeholder="请选择">
								<el-option v-for="item in statusList" :key="item.value" :label="item.key"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
					</el-form>
					<span slot="footer" class="dialog-footer">
						<el-button @click="editUserFlag = false;">取 消</el-button>
						<el-button type="primary" @click="editHandler()" :loading="loading">确 定</el-button>
					</span>
				</el-dialog>
				<!-- 部门列表 -->
				<template>
					<el-table :data="storeFrontList" style="width: 100%" border max-height="100%">
						<el-table-column type="selection" width="60">
						</el-table-column>
						<el-table-column prop="storeNumber" label="门店编号">
						</el-table-column>
						<el-table-column prop="accountName" label="关联用户" width="">
						</el-table-column>
						<el-table-column style="cursor: pointer;" prop="storeFullName" label="门店全称" width="">
							<template slot-scope="scope">
								<a style="color: #409EFF; cursor: pointer;" @click="showSubordinate(scope.row)">{{
									scope.row.storeFullName }}</a>
							</template>
						</el-table-column>

						<el-table-column prop="statusId" label="状态" width="">
							<template slot-scope="scope">
								<span v-if="scope.row.statusId == '0'">启用</span>
								<span v-if="scope.row.statusId == '-1'">停用</span>
							</template>
						</el-table-column>
						<el-table-column label="操作" width="300">
							<template slot-scope="scope">
								<!-- 删除-->
								<el-tooltip content="停用" placement="top">
									<el-button v-if="scope.row.statusId == '0' && (isSupper || editButton)" type="text"
										size="medium" @click="updateStatus(scope.row)">停用</el-button>
								</el-tooltip>

								<el-tooltip content="启用" placement="top">
									<el-button v-if="scope.row.statusId == '-1' && (isSupper || editButton)" type="text"
										size="medium" @click="updateStatus(scope.row)">启用</el-button>
								</el-tooltip>
								<!-- 编辑-->
								<el-tooltip content="编辑" placement="top">
									<el-button v-if="isSupper || editButton" type="text" size="medium"
										@click="editBtnHandler(scope.row)">编辑</el-button>
								</el-tooltip>
								<!-- 删除-->
								<el-tooltip content="删除" placement="top">
									<el-button v-if="isSupper || deleteButton" type="text" size="medium"
										@click="delStroeFront(scope.row)">删除</el-button>
								</el-tooltip>
								<el-tooltip content="下级门店" placement="top">
									<el-button v-if="scope.row.isCatalog == 1" type="text" size="medium"
										@click="showSubordinate(scope.row)" icon="el-icon-bottom-right">下级</el-button>
								</el-tooltip>
								<el-tooltip content="添加下级" placement="top">
									<el-button v-if="isSupper || addButton" type="text"
										size="medium" @click="openAddNext(scope.row)">添加下级</el-button>
								</el-tooltip>
							</template>
						</el-table-column>

					</el-table>
				</template>
				<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
			</div>
		</el-tabs>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import { ElForm } from 'element-ui/types/form';
import { userStore } from "@/store/modules/user";
import { pageStoreFront, updateStatusId, getStoreNumber, addStoreFront, deleteStoreFront, editStoreFront } from "@/api/storeFront/storeFront";
@Component({
	components: {
		PageManage
	}
})
export default class Index extends Vue {
	@Ref()
	readonly dataFormRef!: ElForm;

	// 数据表单  组成数据用于向后端提交数据
	dataForm = {
		storeNumber: '',
		storeFullName: '',
		storeArea: '',
		departmentName: '',
		employeeName: '',
		stockName: ''
	};
	addForm = {
		storeNumber: '',
		storeFullName: '',
		statusId: '',
		parentCode: ''
	};
	loading = false
	//编辑弹出框
	editForm = {
		id: '',
		storeNumber: '',
		storeFullName: '',
		statusId: '',
		parentCode: ''
	}
	statusList = [
		{ key: '启用', value: '0' },
		{ key: '停用', value: '-1' },
	];
	/** 分页条数 */
	pageSize = 10;

	/** 分页页码 */
	pageNum = 1;

	/** 数据长度 */
	total = 0;
	statusList = [
		{ key: '启用', value: '0' },
		{ key: '停用', value: '-1' },
	];

	// 存放全部仓库数据
	storeFrontList = [];

	menuName = "门店管理";
	buttonList = []
	addUserFlag = false
	editUserFlag = false;
	isSupper = 0;

	addButtonCode = "storeFront.add";
	addButton = false;

	editButtonCode = "storeFront.edit";
	editButton = false;

	deleteButtonCode = "storeFront.delete";
	deleteButton = false;

	parentCode = "";
	mounted() {
		this.searchQuery(1);
		this.buttonAuth();
	}

	buttonAuth() {
		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)
		let buttonList = [];
		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});
		this.buttonList = buttonList

		var addButtonData = buttonList.find(e => e == this.addButtonCode);
		if (addButtonData != null && addButtonData != undefined) {
			this.addButton = true;
		}

		var editButtonData = buttonList.find(e => e == this.editButtonCode);
		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}

		var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);
		if (deleteButtonData != null && deleteButtonData != undefined) {
			this.deleteButton = true;
		}
	}
	openAdd() {
		//重置弹出框的数据为空
		this.addForm = {
			storeNumber: '',
			storeFullName: '',
			statusId: '',
			parentCode: this.parentCode
		}
		let params = {}
		params.parentCode = this.parentCode
		getStoreNumber(params).then(res => {
			this.addForm.storeNumber = res.data
			this.addUserFlag = true
		})
	}
	openAddNext(row) {
		//重置弹出框的数据为空
		this.addForm = {
			storeNumber: '',
			storeFullName: '',
			statusId: '',
			parentCode: row.classCode
		}
		let params = {}
		params.parentCode = row.classCode
		getStoreNumber(params).then(res => {
			this.addForm.storeNumber = res.data
			this.addUserFlag = true
		})
	}


	//用户新增的提交方法
	addHandler() {
		if (this.loading) {
			this.$message.error("禁止重复提交");
			return;
		}
		if (this.addForm.storeNumber == '') {
			this.$message.error("门店编号不能为空");
			return;
		}
		if (this.addForm.storeFullName == '') {
			this.$message.error("门店名称不能为空");
			return;
		}
		if (this.addForm.statusId == '') {
			this.$message.error("状态不能为空");
			return;
		}
		this.loading = true
		addStoreFront(this.addForm).then(res => {
			this.$message.success("新增成功");
			// 关闭模态框
			this.addUserFlag = false;
			setTimeout(() => {
				this.loading = false
			}, 100);
			this.searchQuery(1)
		}).catch(err => {
			this.loading = false
			this.$message.error(err || "网络错误");
		});
	}
	//用户编辑的提交方法
	editHandler() {
		if (this.loading) {
			this.$message.error("禁止重复提交");
			return;
		}
		if (this.addForm.storeNumber == '') {
			this.$message.error("门店编号不能为空");
			return;
		}
		if (this.addForm.storeFullName == '') {
			this.$message.error("门店名称不能为空");
			return;
		}
		if (this.addForm.statusId == '') {
			this.$message.error("状态不能为空");
			return;
		}
		this.loading = true
		editStoreFront(this.editForm).then(res => {
			this.$message.success("编辑成功");
			// 关闭模态框
			this.editUserFlag = false;
			setTimeout(() => {
				this.loading = false
			}, 100);
			// 提交数据成功，重新获取一次数据进行渲染
			this.searchQuery(this.pageNum);
		}).catch(err => {
			this.loading = false
			this.$message.error(err || "网络错误");
		});
	}
	delStroeFront(row) {
		this.$confirm('此操作将永久删除该门店, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const delParam = {
				id: row.id,
			}
			deleteStoreFront(delParam).then(res => {
				this.$message.success("删除成功");
				// 关闭模态框
				this.searchQuery(1)
			})

		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消删除'
			});
			this.searchQuery(1);
		});
	}
	//获取用户列表（数据）
	searchQuery(pageNum: number) {
		// console.log('执行了');
		const form = this.dataForm;
		const param = {
			current: pageNum,
			size: this.pageSize,
			...form
		}

		pageStoreFront(param).then(res => {
			// 仓库数据
			this.storeFrontList = res.data.list
			// 显示数据条数
			this.pageSize = res.data.size;
			// 第几页
			this.pageNum = res.data.current;

			this.total = res.data.total;
		})
	}
	//获取当前行需要修改的信息
	editBtnHandler(val: any) {
		this.editUserFlag = true;
		// this.EditwareList =    JSON.parse(JSON.stringify(([ val ])));
		this.editForm = { ...val };
	}
	updateStatus(scope) {
		console.log(scope);
		let statusId = scope.statusId;
		let id = scope.id;
		let text = "";
		if (statusId == 0) {
			text = "是否停用门店?";
		} else {
			text = "是否启用门店?";
		}

		this.$confirm(text, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const updateParam = {
				id: scope.id,
			}
			if (statusId == 0) {
				updateParam.statusId = -1;
			}
			if (statusId == -1) {
				updateParam.statusId = 0;
			}

			updateStatusId(updateParam).then(res => {
				this.$message({
					type: 'success',
					message: res.data
				});
				this.searchQuery(1);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消'
			});
			this.searchQuery(1);
		});

	}
	//删除按钮功能模块
	delWareBtn(scope) {
		this.$confirm('此操作将永久删除该部门, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const delParam = {
				id: scope.id,
			}
			delDepartment(delParam).then(res => {
				this.$message({
					type: 'success',
					message: res.data
				});
				this.searchQuery(1);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消删除'
			});
			this.searchQuery(1);
		});
	}
	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.pageSize = val;
		this.searchQuery(1);
	}
	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.pageNum = val;
		this.searchQuery(val);
	}

	/**
	 * 
	 * @param row 点击行数据
	 * @param isCatalog 是否有下级门店
	 * @param classCode 传给后端的参数
	 * @param storeFullName 门店全称
	 */
	showSubordinate(row) {
		let { isCatalog, storeFullName, classCode } = row;
		//如果有下级门店才会跳转
		if (isCatalog == 1) {
			this.$router.push({
				path: 'storeFront/subordinateStore',
				query: {
					storeFullName: storeFullName,
					classCode: classCode
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.el-checkbox__inner {
	border-radius: 50% !important;
}
</style>
