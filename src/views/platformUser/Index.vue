<!--
 * @description: 平台用户管理
-->
<template>
	<div class="customer">
		<el-tabs>
			<div class="memberList">
				<div class="line"></div>
				<m-card class="form" :needToggle="true">
					<el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="用户名称"><el-input v-model="dataForm.nikeName" clearable
										placeholder="请输入用户名称" /></el-form-item>
							</el-col>
							<el-col :span="10">
								<el-form-item label="手机号"><el-input v-model="dataForm.phone" clearable
										placeholder="请输入用户手机号" /></el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="关联部门"><el-input v-model="dataForm.departmentName" clearable
										placeholder="请输入关联部门" /></el-form-item>
							</el-col>
							<el-col :span="10">
								<el-form-item label="关联职员"><el-input v-model="dataForm.employeeName" clearable
										placeholder="请输入关联职员" /></el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="关联仓库"><el-input v-model="dataForm.stockName" clearable
										placeholder="请输入关联仓库" /></el-form-item>
							</el-col>
							<el-col :span="10">
								<el-form-item label="关联门店"><el-input v-model="dataForm.storeFrontName" clearable
										placeholder="请输入关联门店" /></el-form-item>
							</el-col>
						</el-row>
						<el-row :gutter="40">
							<el-col :span="10">
								<el-form-item label="状  态">
									<el-select v-model="dataForm.forbidStatus" placeholder="请选择状态" style="width: 256px"
										clearable>
										<el-option label="全部" :value="null" />
										<el-option v-for="tag in statusList" :key="tag.value" :label="tag.key"
											:value="tag.value" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
						<el-button type="primary" style="margin-left:100px" @click="searchQuery()">搜索</el-button>
					</el-form>
				</m-card>
				<el-button type="primary" icon="el-icon-circle-plus-outline" @click="addUserFlag = true"
					v-if="isSupper || addButton" style="margin-bottom:20px; float:right; margin-right: 60px;">
					新增
				</el-button>
				<!-- 新增子用户 模态框 -->
				<el-dialog title="新增用户" :visible.sync="addUserFlag" width="30%">
					<!-- <template v-for="item in userList" :key="item.tagId"> -->
					<el-form :model="addForm" ref="addForm" label-width="100px">
						<el-form-item label="用户名称" prop="nikeName" :readonly="true">
							<el-input v-model="addForm.nikeName" autocomplete="off"></el-input>
						</el-form-item>
						<el-form-item label="手机号" prop="phone" :rules="[
							{ required: true, message: '手机号不能为空' }
						]">
							<el-input v-model="addForm.phone"></el-input>
						</el-form-item>

						<el-form-item label="密码" prop="passwd" :rules="[
							{ required: true, message: '密码不能为空' }
						]">
							<el-input type="password" v-model="addForm.passwd"></el-input>
						</el-form-item>

						<el-form-item label="状态">
							<el-select v-model="addForm.forbidStatus" placeholder="请选择">
								<el-option v-for="item in statusList" :key="item.value" :label="item.key"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="角色">
							<el-select v-model="addForm.roleIds" multiple placeholder="请选择">
								<el-option v-for="item in optionList" :key="item.value" :label="item.label"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>

					</el-form>
					<!-- </template> -->
					<span slot="footer" class="dialog-footer">
						<el-button @click="addUserFlag = false;">取 消</el-button>
						<el-button type="primary" @click="addHandler()">确 定</el-button>
					</span>
				</el-dialog>
				<!-- 编辑用户模块 -->
				<el-dialog title="编辑用户" :visible.sync="editUserFlag" width="30%">
					<!-- <template v-for="item in userList" :key="item.tagId"> -->
					<el-form :model="editForm" ref="editForm" label-width="100px">

						<el-form-item label="用户名称" prop="nikeName" :rules="[
							{ required: true, message: '用户名称不能为空' }

						]">
							<el-input @click.native="editName" v-model="editForm.nikeName"
								autocomplete="off"></el-input>

						</el-form-item>
						<el-form-item label="手机号" prop="phone" :rules="[
							{ required: true, message: '手机号不能为空' }
						]">
							<el-input v-model="editForm.phone"></el-input>
						</el-form-item>

						<el-form-item label="状态">
							<el-select v-model="editForm.forbidStatus" placeholder="请选择">
								<el-option v-for="item in statusList" :key="item.value" :label="item.key"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="角色">
							<el-select v-model="editForm.roleIds" multiple placeholder="请选择">
								<el-option v-for="item in optionList" :key="item.value" :label="item.label"
									:value="item.value">
								</el-option>
							</el-select>
						</el-form-item>
					</el-form>
					<!-- </template> -->
					<span slot="footer" class="dialog-footer">
						<el-button @click="editUserFlag = false;">取 消</el-button>
						<el-button type="primary" @click="editHandler()">确 定</el-button>
					</span>
				</el-dialog>

				<!-- 双击编辑用户模块的名字弹窗 -->
				<bindWare @sendRequest="handleRequest" ref="ware" />
				<!-- 绑定小程序用户弹窗 -->

				<!-- 重置用户密码 -->
				<el-dialog title="重置用户密码" :visible.sync="resetUserFlag" width="30%">
					<!-- <template v-for="item in userList" :key="item.tagId"> -->
					<el-form :model="resetForm" ref="resetForm" label-width="100px">

						<el-form-item label="密码" prop="passwd" :rules="[
							{ required: true, message: '密码不能为空' }
						]">
							<el-input type="password" v-model="resetForm.passwd"></el-input>
						</el-form-item>

					</el-form>
					<!-- </template> -->
					<span slot="footer" class="dialog-footer">
						<el-button @click="resetUserFlag = false;">取 消</el-button>
						<el-button type="primary" @click="resetHandler()">确 定</el-button>
					</span>
				</el-dialog>

				<!-- 用户列表 -->
				<template>
					<el-table :data="userList" style="width: 100%" border max-height="100%">

						<el-table-column type="selection" width="60">
						</el-table-column>
						<el-table-column prop="nikeName" label="用户名称" width="200">
						</el-table-column>
						<el-table-column prop="phone" label="手机号" width="120">
						</el-table-column>
						<el-table-column prop="roleName" label="角色" width="120">
						</el-table-column>
						<el-table-column prop="shopName" label="店铺" width="200">
						</el-table-column>
						<el-table-column prop="" label="关联部门" width="150">
							<template slot-scope="scope">
								<div v-if="isSupper || editButton">
									<a v-if="scope.row.departmentName" style="color: #409eff; cursor: pointer;"
										@click="bindDepartmentBtn(scope.row)">{{ scope.row.departmentName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;"
										@click="bindDepartmentBtn(scope.row)">关联部门</a>
								</div>
								<div v-else>
									<a v-if="scope.row.departmentName" style="color: #409eff; cursor: pointer;">{{
										scope.row.departmentName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;">关联部门</a>
								</div>

							</template>
						</el-table-column>
						<el-table-column prop="" label="关联职员" width="150">
							<template slot-scope="scope">
								<div v-if="isSupper || editButton">
									<a v-if="scope.row.employeeName" style="color: #409eff; cursor: pointer;"
										@click="bindDepartmentBtn(scope.row)">{{ scope.row.employeeName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;"
										@click="bindDepartmentBtn(scope.row)">关联职员</a>
								</div>
								<div v-else>
									<a v-if="scope.row.employeeName" style="color: #409eff; cursor: pointer;">{{
										scope.row.employeeName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;">关联职员</a>
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="stockName" label="关联仓库" width="150">
							<template slot-scope="scope">
								<div v-if="isSupper || editButton">
									<a @click="binWareBtn(scope.row)" v-if="scope.row.stockName"
										style="color: #409eff; cursor: pointer;">{{ scope.row.stockName }}</a>
									<a v-else @click="binWareBtn(scope.row)"
										style="color: #409eff; cursor: pointer;">关联仓库</a>
								</div>
								<div v-else>
									<a v-if="scope.row.stockName" style="color: #409eff; cursor: pointer;">{{
										scope.row.stockName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;">关联仓库</a>
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="" label="关联小程序用户" width="150">
							<template slot-scope="scope">
								<div v-if="isSupper || editButton">
									<a v-if="scope.row.miniAccountName" style="color: #409eff; cursor: pointer;"
										@click="bindUniappUser(scope.row)">{{ scope.row.miniAccountName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;"
										@click="bindUniappUser(scope.row)">关联小程序用户</a>
								</div>
								<div v-else>
									<a v-if="scope.row.miniAccountName" style="color: #409eff; cursor: pointer;">{{
										scope.row.miniAccountName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;">关联小程序用户</a>
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="" label="关联门店" width="150">
							<template slot-scope="scope">
								<div v-if="isSupper || editButton">
									<a v-if="scope.row.storeFrontName" style="color: #409eff; cursor: pointer;"
										@click="bindStoreFrontBtn(scope.row)">{{ scope.row.storeFrontName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;"
										@click="bindStoreFrontBtn(scope.row)">关联门店</a>
								</div>
								<div v-else>
									<a v-if="scope.row.storeFrontName" style="color: #409eff; cursor: pointer;">{{
										scope.row.storeFrontName }}</a>
									<a v-else style="color: #409eff; cursor: pointer;">关联门店</a>
								</div>
							</template>
						</el-table-column>

						<!--状态栏-->
						<el-table-column label="状态">
							<template slot-scope="scope">
								<template v-if="scope.row.forbidStatus == 0">
									<span style=" display: block; width: 30px; text-align: center; ">正常</span>
								</template>
								<template v-if="scope.row.forbidStatus == 1">
									<span style="display: block;  width: 30px; text-align: center; ">禁用</span>
								</template>
							</template>
						</el-table-column>
						<el-table-column label="操作" width="150">
							<template slot-scope="scope">
								<!-- 编辑-->
								<el-tooltip effect="dark" content="编辑" placement="top" v-if="isSupper || editButton">
									<el-button type="text" size="medium" icon="el-icon-edit"
										@click="editBtnHandler(scope.row)"></el-button>
								</el-tooltip>
								<!-- 重置密码-->
								<el-tooltip effect="dark" content="重置密码" placement="top"
									v-if="isSupper || resetPasswordButton">
									<el-button type="text" size="medium" icon="el-icon-attract"
										@click="resetBtnHandler(scope.row)"></el-button>
								</el-tooltip>
								<el-tooltip effect="dark" content="默认门店" placement="top">
									<el-button type="text" size="medium" icon="el-icon-house"
										v-if="isSupper || editButton" @click="setDefaultStore(scope.row)">
									</el-button>
								</el-tooltip>
								<el-tooltip effect="dark" content="默认部门" placement="top">
									<el-button type="text" size="medium" icon="el-icon-document-copy"
										v-if="isSupper || editButton" @click="setDefaultDepartment(scope.row)">
									</el-button>
								</el-tooltip>

								<!-- 删除-->
								<!--<el-button type="text" size="medium" icon="el-icon-delete-solid" @click="delWareBtn(scope.row)"></el-button>-->
								<!--启用/删除-->
								<!--<template v-if="scope.row.state == 0">
                <el-button type="text" icon="el-icon-close" @click="deactivate(scope.row)"></el-button>
                </template>
	<template v-if="scope.row.state == 1">
                <el-button type="text" icon="el-icon-check" @click="deactivate(scope.row)"></el-button>
                </template>-->
							</template>
						</el-table-column>
					</el-table>
				</template>
				<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
				<black-list ref="blackListDialogRef" v-if="blackListVisible" @refreshDataList="searchQuery" />
				<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList"
					@refreshDataList="searchQuery()" />
				<set-label ref="labelDialogRef" v-if="labelVisible" :allTags="allTagList"
					@refreshDataList="searchQuery()" />
			</div>
		</el-tabs>
		<uniappWare @load="searchQuery(pageNum)" ref="uniappWare" />

		<bindStore ref="bindStore" @load="searchQuery(pageNum)" />
		<!-- 绑定部门 -->
		<bindEmp ref="bindEmp" @searchQuery="searchQuery(pageNum)" />
		<!-- 绑定仓库 -->
		<userBindWare ref="bindWare" @searchQuery="searchQuery(pageNum)" />
	</div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import BlackList, { NewCustomerTagList } from '@/views/customer/common/SetBlackList.vue';
import SetDrop from '@/views/customer/common/SetDrop.vue';
import SetLabel from '@/views/customer/list/components/dialog/SetTags.vue';
import bindWare from './components/ware.vue'
import { CustomerListState, ApiCustomerList, CustomerTagList, CustomerRank } from './customerListType';
import { ElForm } from 'element-ui/types/form';
import uniappWare from './components/uniappWare.vue'
import { editWarehouse, delWarehouse, stateWarehouse } from '@/api/warehouse/warehouse';
import { ElTable } from 'element-ui/types/table';
import { pageList, addAccountInfo, editAccountInfo, resetUserPwd, getRoleList, pageUserInfoList, setDefaultShop, settingDefaultDepartment } from "@/api/platformUser/platformUser";
import { userStore } from "@/store/modules/user";
import bindStore from './components/storeFront.vue'
import bindEmp from './components/bindEmp.vue'
import userBindWare from './components/bindWare.vue'
@Component({
	components: {
		PageManage,
		BlackList,
		SetDrop,
		bindWare,
		uniappWare,
		bindStore,
		bindEmp,
		userBindWare
	}
})
export default class Index extends Vue implements CustomerListState {
	@Ref()
	readonly dataFormRef!: ElForm;

	@Ref()
	readonly customerListRef!: ElTable;

	@Ref()
	readonly blackListDialogRef!: BlackList;

	@Ref()
	readonly labelDialogRef!: SetLabel;

	// 数据表单  组成数据用于向后端提交数据
	dataForm = {
		nikeName: '',
		phone: '',
		departmentName: '',
		employeeName: '',
		storeFrontName: '',
		stockName: '',
		forbidStatus: null,
	};

	dataList: Array<ApiCustomerList> = [];

	allTagList: Array<CustomerTagList> = [];

	statusList = [
		{ key: '正常', value: 0 },
		{ key: '禁用', value: 1 },
	];

	selectionList: Array<ApiCustomerList | NewCustomerTagList> = [];

	multipleSelection: Array<ApiCustomerList | NewCustomerTagList> = [];

	managerVisible = false;

	blackListVisible = false;

	labelVisible = false;

	visible = true;




	//仓库新增弹出框
	addUserFlag = false;

	//仓库编辑弹出框
	editUserFlag = false;

	//重置密码弹出框
	resetUserFlag = false;
	//新增弹出框
	addForm = {
		nikeName: '',
		phone: '',
		passwd: '',
		forbidStatus: 0,
		roleIds: '',
		employeeId: '',

	}

	//编辑弹出框
	editForm = {
		id: '',
		nikeName: '',
		phone: '',
		passwd: '',
		forbidStatus: '',
		roleIds: '',
		employeeId: ''
	}

	//重置密码表单
	resetForm = {
		id: '',
		passwd: '',
	}

	houseList = {}


	rankOptions: Array<CustomerRank> = [];

	/** 分页条数 */
	pageSize = 10;

	/** 分页页码 */
	pageNum = 1;

	/** 数据长度 */
	total = 0;

	/** 赠送优惠券下拉菜单 */
	couponDropList: any[] = [
		{
			command: '加入黑名单',
			disabled: false,
			show: true,
			text: '加入黑名单'
		}
	];

	// 存放全部仓库数据
	userList = [];

	optionList = [];

	Disableid = '';

	Disablestate = '';

	warehouseList = {
		warehouseNumber: '',
		warehouseAddress: '',
		warehouseFullName: ''
	};
	// 存放编辑后获取到的数据
	EditwareList = [];
	EditwareList1 = [];

	menuName = "用户管理";
	buttonList = [];
	isSupper = 0;

	addButtonCode = "pageInfo.add";
	addButton = false;

	editButtonCode = "pageInfo.edit";
	editButton = false;

	resetPasswordButtonCode = "pageInfo.resetPassword";
	resetPasswordButton = false;

	mounted() {
		this.searchQuery(1);
		this.getRoleList();
		this.buttonAuth();
	}

	buttonAuth() {

		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var addButtonData = buttonList.find(e => e == this.addButtonCode);

		if (addButtonData != null && addButtonData != undefined) {
			this.addButton = true;
		}

		var editButtonData = buttonList.find(e => e == this.editButtonCode);

		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}

		var resetPasswordButtonData = buttonList.find(e => e == this.resetPasswordButtonCode);

		if (resetPasswordButtonData != null && resetPasswordButtonData != undefined) {
			this.resetPasswordButton = true;
		}

	}


	getMemberList(data) {
		console.log(data)
	}

	batchCouponClick() {
		if (this.multipleSelection.length > 0) {
			this.labelVisible = true;
			this.$nextTick(() => {
				this.labelDialogRef.init(this.multipleSelection as ApiCustomerList[]);
			});
		} else {
			this.$message.warning('至少选择一个客户');
		}
	}

	batchCouponCommand(command: string) {
		if (this.multipleSelection.length > 0) {
			switch (command) {
				case '加入黑名单':
					this.blackListVisible = true;
					this.$nextTick(() => {
						this.blackListDialogRef.init(this.multipleSelection as ApiCustomerList[], 1);
					});
					break;
			}
		} else {
			this.$message.info('请至少选择一个客户');
		}
	}

	itemClick(row: ApiCustomerList) {
		this.blackListVisible = true;
		this.$nextTick(() => {
			this.blackListDialogRef.init([row], 1);
		});
	}

	itemCommand(command: string, row: ApiCustomerList) {
		switch (command) {
			case '加入黑名单':
				this.blackListVisible = true;
				this.$nextTick(() => {
					this.blackListDialogRef.init([row], 1);
				});
				break;
			case "3":
				this.memberUpgrade();
				break;
			case "5":
				this.memberRenews();
				break;
		}
	}

	//获取用户列表（数据）
	searchQuery(pageNum: number) {
		const form = this.dataForm;
		const param = {
			current: pageNum,
			size: this.pageSize,
			...form
		}

		pageUserInfoList(param).then(res => {
			// 仓库数据
			this.userList = res.data.list

			this.userList.forEach(element => {
				let roleVos = element.roleVos;
				let roleName = "";
				let roleIds = [];
				if (roleVos != null && roleVos.length > 0) {
					roleVos.forEach(i => {
						if (roleName.length > 0) {
							roleName += "，";
						}
						roleName += i.roleName;
						roleIds.push(i.roleId);
					});
				}
				element.roleName = roleName
				element.roleIds = roleIds
			});
			// 显示数据条数
			this.pageSize = res.data.size;
			// 第几页
			this.pageNum = res.data.current;

			this.total = res.data.total;
		})
	}

	//获取当前行需要修改的信息
	editBtnHandler(val: any) {

		this.editUserFlag = true;
		// this.EditwareList =    JSON.parse(JSON.stringify(([ val ])));
		this.editForm = { ...val };
	}

	getRoleList() {
		getRoleList({}).then(res => {
			let optionList = [];
			res.data.forEach(element => {
				let option = {};
				option.value = element.id
				option.label = element.roleName
				optionList.push(option);
			});
			this.optionList = optionList
		})
	}
	//重置当前行密码需要的信息
	resetBtnHandler(val: any) {
		this.resetUserFlag = true;
		// this.EditwareList =    JSON.parse(JSON.stringify(([ val ])));
		this.resetForm = { ...val };

	}
	//删除按钮功能模块
	delWareBtn(scope) {
		this.$confirm('此操作将永久删除该仓库, 是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const delParam = {
				id: scope.id,
			}
			delWarehouse(delParam).then(res => {
				this.$message({
					type: 'success',
					message: res.data
				});
				this.searchQuery(1);
			})
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消删除'
			});
			this.searchQuery(1);
		});
	}

	//仓库模块的禁用启用
	deactivate(scope) {
		// this.Disableid = scope.id
		// this.Disablestate = scope.state
		this.$confirm('更改启用状态将停用/启用该仓库，确定继续更改吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			if (scope.state == 0) {
				scope.state = 1
			} else if (scope.state == 1) {
				scope.state = 0
			}
			const statusList = {
				id: scope.id,
				state: scope.state
			};
			stateWarehouse(statusList)
				.then((res) => {
					this.$message.success(res.data);
				})
				.catch(err => {
					this.$message.error(err || "网络错误");
				});
		}).catch(err => {
			this.$message({
				type: 'info',
				message: '已取消操作'
			});
		})
	}

	// steWarehouse(){
	// 	const statusList = {
	// 	id : this.Disableid,
	// 	state : this.Disablestate
	// 	};
	// 	stateWarehouse(statusList)
	// 	.then((res) => {
	// 	  this.$message.success(res.data);
	// 	})addForm
	// 	.catch(err => {
	// 	  this.$message.error(err || "网络错误");
	// 	});
	// }


	setLabel(row: ApiCustomerList) {
		this.labelVisible = true;
		this.$nextTick(() => {
			this.labelDialogRef.init([row]);
		});
	}

	//用户新增的提交方法
	addHandler() {
		this.addForm.shopId = userStore.userInfo.shopInfoVo.shopId
		addAccountInfo(this.addForm).then(res => {
			this.$message.success("新增成功");
			// 关闭模态框
			this.addUserFlag = false;
			//重置弹出框的数据为空
			this.addForm = {
				nikeName: '',
				phone: '',
				passwd: '',
				forbidStatus: 0,
				employeeId: '',
			}
			// 提交数据成功，重新获取一次数据进行渲染
			this.searchQuery(1);
		}).catch(err => {
			this.$message.error(err || "网络错误");
		});
	}

	//用户编辑的提交方法
	editHandler() {
		console.log("beforeSendReq", this.editForm);
		editAccountInfo(this.editForm).then(res => {
			this.$message.success("编辑成功");
			// 关闭模态框
			this.editUserFlag = false;
			// 提交数据成功，重新获取一次数据进行渲染
			this.searchQuery(this.pageNum);
		}).catch(err => {
			this.$message.error(err || "网络错误");
		});
	}

	//重置用户密码的提交方法
	resetHandler() {
		resetUserPwd(this.resetForm).then(res => {
			this.$message.success("重置成功");
			// 关闭模态框
			this.resetUserFlag = false;
			// 提交数据成功，重新获取一次数据进行渲染
			this.searchQuery(this.pageNum);
		}).catch(err => {
			this.$message.error(err || "网络错误");
		});
	}

	//仓库编辑的提交方法
	houseEditSub() {
		const houseEdit = {
			id: this.EditwareList[0].id,
			warehouseNumber: this.EditwareList[0].warehouseNumber,
			warehouseFullName: this.EditwareList[0].warehouseFullName,
			warehouseAddress: this.EditwareList[0].warehouseAddress
		}
		// const houseEdit1 = JSON.parse(JSON.stringify(houseEdit));
		editWarehouse(houseEdit).then(res => {
			this.$message({
				message: res.data,
				type: 'success'
			});
			// 关闭模态框
			this.editUserFlag = false;
			this.searchQuery(this.pageNum);
			// this.searchQuery(1);
		}).catch(err => {
			// 提交失败返回提示
			this.$message.error(err || res.data);
			this.editUserFlag = false;
		});

	}
	// cancelEditSub(){
	//   editWarehouse(this.EditwareList[0]).then(res => {
	//     this.$message({
	//       message: res.data,
	//       type: 'success'
	//     });
	//     // 关闭模态框
	//     this.editUserFlag = false;
	//     // this.searchQuery(1);
	//   }).catch(err => {
	//     // 提交失败返回提示
	//     this.$message.error(err || res.data);
	//     this.editUserFlag = false;
	//   });
	//   this.searchQuery(1);
	// }



	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.pageSize = val;
		this.searchQuery();
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.pageNum = val;
		this.searchQuery(val);
	}

	openDialog() {
		this.$refs.ware.open()
	}
	//子组件点击绑定后，获取子组件发送的数据
	handleRequest(data: any) {
		this.editForm.nikeName = data.empFullName;
		this.editForm.employeeId = data.id
		this.addForm.nikeName = data.empFullName;
		this.addForm.employeeId = data.id
	}
	/**
	 * @method 绑定小程序用户
	 */
	bindUniappUser(row) {
		this.$refs.uniappWare.openDialog(row)
	}

	//绑定门店按钮功能模块
	bindStoreFrontBtn(scope) {
		this.$refs.bindStore.openDialog(scope.id, scope.storeFrontCode, scope.storeFrontName)
	}

	//绑定经手人功能模块
	bindDepartmentBtn(scope) {


		this.$refs.bindEmp.openDialog(scope.id, scope.employeeId)
	}

	//绑定仓库功能模块
	binWareBtn(row) {
		this.$refs.bindWare.openDialog(row.id, row.stockCode)
	}
	setDefaultDepartment(row) {
		let accountId = row.id;
		this.$confirm('是否设置为默认部门?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			//点击确定调用接口
			settingDefaultDepartment({ accountId }).then(() => {
				this.$message({
					type: 'success',
					message: '设置成功!'
				});
			}).catch(err => {
				this.$message.error(err || "网络错误");
			});
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消设置'
			});
		});
	}
	//设置默认门店模块
	setDefaultStore(row) {
		let accountId = row.id;
		this.$confirm('是否设置为默认门店?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			//点击确定调用接口
			setDefaultShop({ accountId }).then(() => {
				this.$message({
					type: 'success',
					message: '设置成功!'
				});
			}).catch(err => {
				this.$message.error(err || "网络错误");
			});
		}).catch(() => {
			this.$message({
				type: 'info',
				message: '已取消设置'
			});
		});
	}
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';
</style>
