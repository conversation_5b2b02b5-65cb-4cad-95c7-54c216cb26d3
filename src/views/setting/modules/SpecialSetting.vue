<!-- 2025.6.4有改动的页面 -->

<template>
    <div>
        <el-form :model="dataForm" label-width="190px" label-position="left">
            <el-form-item label="特殊设置" prop="miniModel">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <td>特殊权限信息</td>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="平台查看商户订单" required>
                <el-radio-group v-model="dataForm.platformSearchShopOrder">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="商户允许平台查看订单" required>
                <el-radio-group v-model="dataForm.shopAllowPlatformSearchOrder">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="平台代商户发货" required>
                <el-radio-group v-model="dataForm.platformProxyShipping">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="新人优惠券领取天数">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <el-input-number :step="1" :min="1" step-strictly
                            v-model="dataForm.newPeopleCouponDays"></el-input-number>天
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="小程序展示商品" required>
                <el-radio-group v-model="dataForm.miniShowProductType">
                    <el-radio :label="0">商品</el-radio>
                    <el-radio :label="1">权益包</el-radio>
                    <el-radio :label="2">商品和权益包</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="是否启用复购页" required>
                <el-radio-group v-model="dataForm.memberSales">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="佣金提现是否自动审核" required>
                <el-radio-group v-model="dataForm.commissionCashFlag">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="接口对接职员是否可添加" required>
                <el-radio-group v-model="dataForm.addEmployeeFlag">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="认证注册才允许进入小程序" required>
                <el-radio-group v-model="dataForm.authRegisterBrowseFlag">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="商品显示会员价" required>
                <el-radio-group v-model="dataForm.showMemberPriceFlag">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="提现方式" required>
                <el-radio-group v-model="dataForm.withdrawalMethod">
                    <el-radio :label="1">线下提现</el-radio>
                    <el-radio :label="2">线上提现</el-radio>
                    <el-radio :label="3">线上提现+线下提现</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="是否开启售后整单退" required>
                <el-radio-group v-model="dataForm.allAfsOrderFlag">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="是否开启聚合支付" required>
                <el-radio-group v-model="dataForm.aggregationFlag">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-row :gutter="15">
                    <el-col :span="12">
                        <div class="auth__buttons">
                            <el-button type="primary" @click="saveSpecialSetting" v-if="isSupper || saveButton">保存
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="信息设置" prop="miniModel">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <td>小程序备案信息</td>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="由某某公司提供电商运营支持">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <el-input v-model="miniFilings.company" placeholder="请输入电商运营公司"></el-input>

                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="小程序备案号">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <el-input v-model="miniFilings.reserveNumber" placeholder="请输入小程序备案号"></el-input>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="小程序由某某公司技术支持">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <el-input v-model="miniFilings.technologyCompany" placeholder="请输入技术公司"></el-input>

                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item label="注册隐私协议">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <el-upload action="" ref="uploadPrivacyFile" :http-request="uploadPrivacyAgreement"
                            :auto-upload="true" :show-file-list="false">

                            <el-button type="text" size="small" @click.stop="downloadFile" v-if="showFileName"
                                style="color: #67C23A;"> {{ miniFilings.privacyAgreement }}
                            </el-button>

                            <el-button type="text" size="small" v-if="!showFileName">未上传
                            </el-button>
                            <el-button type="primary">上传</el-button>
                            <div slot="tip" class="el-upload__tip">
                                支持PDF、DOC、DOCX格式，文件大小不超过10MB
                            </div>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-form-item>
            <el-form-item>
                <el-row :gutter="15">
                    <el-col :span="12">
                        <div class="auth__buttons">
                            <el-button type="primary" @click="submitFilings" v-if="isSupper || saveButton">保存
                            </el-button>
                        </div>
                    </el-col>
                </el-row>
            </el-form-item>
        </el-form>
    </div>
</template>
<script lang='ts'>
import { Component, Vue } from 'vue-property-decorator'
import { getSpecialSetting, modifySpecialSetting } from "@/api/specialSetting/index";
import {
    reserve,
    reserveUpdate,
} from "@/api/businessCenter/setting";
import { upLoad } from "@/api/index";
import { HttpRequestOptions } from "element-ui/types/upload";
@Component
export default class SpecialSetting extends Vue {
    dataForm = {
        platformSearchShopOrder: 0,
        shopAllowPlatformSearchOrder: 0,
        platformProxyShipping: 0,
        newPeopleCouponDays: 0,
        miniShowProductType: 0,
        memberSales: 0,
        commissionCashFlag: 0,
        addEmployeeFlag: 0,
        authRegisterBrowseFlag: 0,
        showMemberPriceFlag: 0,
        withdrawalMethod: 1,
        allAfsOrderFlag: 0,
        aggregationFlag:0,
    };
    isSupper = 0;
    saveButtonCode = "allSetting.SpecialSetting.save";
    saveButton = false;
    menuName = "通用设置";
    showFileName = false;
    // 小程序备案信息
    miniFilings = {
        reserveNumber: '',//备案号:
        company: '',//由XXX公司提供电商运营支持
        technologyCompany: '',//小程序由XXX公司技术支持
        privacyAgreement: "",//隐私协议文件名
        privacyAgreementUrl: "",//隐私协议文件地址
    }
    mounted() {
        this.getSpecialData();
        this.getReserve()
        this.buttonAuth();
    }
    /**获取小程序备案信息 */
    getReserve() {
        reserve({}).then((res) => {
            this.miniFilings = res.data
        }).catch(err => {
            this.$message.error(err)
        })
    }
    buttonAuth() {
        this.isSupper = this.$STORE.userStore.userInfo.isSupper
        let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)
        let buttonList = [];

        authMenuButtonVos.forEach(element => {
            buttonList.push(element.buttonCode);
        });

        this.buttonList = buttonList

        var saveButtonData = buttonList.find(e => e == this.saveButtonCode);

        if (saveButtonData != null && saveButtonData != undefined) {
            this.saveButton = true;
        }
    }
    getSpecialData() {
        getSpecialSetting().then(res => {
            if (res.data.platformSearchShopOrder != null && res.data.shopAllowPlatformSearchOrder != null && res.data.platformProxyShipping != null) {
                this.dataForm = res.data
            }
            console.log("res=", this.dataForm);
            this.showFileName = res.data.privacyAgreement != "";
        }).catch(err => {
            console.log(err)
        })
    }

    saveSpecialSetting() {
        if (!this.dataForm.newPeopleCouponDays) {
            this.$message.error("新人优惠券领取天数不能为空")
            return;
        }
        const form = {
            platformSearchShopOrder: this.dataForm.platformSearchShopOrder,
            shopAllowPlatformSearchOrder: this.dataForm.shopAllowPlatformSearchOrder,
            platformProxyShipping: this.dataForm.platformProxyShipping,
            newPeopleCouponDays: this.dataForm.newPeopleCouponDays,
            miniShowProductType: this.dataForm.miniShowProductType,
            memberSales: this.dataForm.memberSales,
            commissionCashFlag: this.dataForm.commissionCashFlag,
            addEmployeeFlag: this.dataForm.addEmployeeFlag,
            authRegisterBrowseFlag: this.dataForm.authRegisterBrowseFlag,
            showMemberPriceFlag: this.dataForm.showMemberPriceFlag,
            withdrawalMethod: this.dataForm.withdrawalMethod,
            allAfsOrderFlag: this.dataForm.allAfsOrderFlag,
            aggregationFlag:this.dataForm.aggregationFlag
        };
        console.log("form=", this.form);
        modifySpecialSetting(JSON.stringify(form)).then(() => {
            this.$message({
                type: "success",
                message: "保存成功!",
            });
        });

    }


    /**
     * 下载隐私协议文件
     */
    async downloadFile() {

        try {
            // 使用fetch获取文件内容
            const response = await fetch(this.miniFilings.privacyAgreementUrl);
            const blob = await response.blob();

            // 创建blob URL
            const blobUrl = window.URL.createObjectURL(blob);

            // 创建临时a标签下载
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = this.miniFilings.privacyAgreement; // 使用我们指定的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清理blob URL
            window.URL.revokeObjectURL(blobUrl);
        } catch (error) {
            console.error('下载失败:', error);
            this.$message.error('下载失败，请重试');
        }
    }

    /**
     * 上传隐私协议文件
     */
    async uploadPrivacyAgreement(file: HttpRequestOptions) {
        // 文件类型验证
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        const fileType = file.file.type;
        const isValidType = allowedTypes.includes(fileType);

        if (!isValidType) {
            this.$message.error('只支持PDF、DOC、DOCX格式的文件！');
            return;
        }

        // 文件大小验证（10MB）
        const isLt5M = file.file.size / 1024 / 1024 < 10;
        if (!isLt5M) {
            this.$message.error('文件大小不能超过10MB！');
            return;
        }

        try {
            const response = await upLoad({
                file: file.file,
            });
            const { code, data, msg } = response;
            if (code === 200) {
                this.showFileName = true;
                this.$message({ message: "上传成功", type: "success", });
                this.miniFilings.privacyAgreement = msg; // 显示文件名
                this.miniFilings.privacyAgreementUrl = data; // 保存文件地址
                console.log(this.miniFilings.privacyAgreement);
            }
        } catch (e) {
            this.$message({
                message: e,
                type: "warning",
            });
        }
    }

    async submitFilings() {
        reserveUpdate(this.miniFilings).then((res) => {
            this.$message.success('设置成功')
        }).catch((err) => {
            this.$message.error(err)
        })
    }
}
</script>