<template>
    <div>
        <Search @searchBy="getSearch" ref="Search"></Search>
        <!-- 导出按钮 -->
        <div class="topLine">
            <div class="topLine__left">
                <div class="all">
                    <div class="setClassify" style="margin-left: 40px">
                        <el-button type="primary" @click="exportData">导出列表</el-button>
                    </div>
                </div>
            </div>
        </div>
        <CommissionDetList ref="commissionDetList" @getShowProList="getShowProList">
        </CommissionDetList>
        <div style="height: 20px;"></div>
        <div class="listBottom">
            <!-- 设置分类 -->
            <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange" style="margin-top: 0px"></PageManage>
        </div>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch, Ref } from "vue-property-decorator";
import Search from "./components/Search.vue";
import CommissionDetList from "./components/CommissionDetList.vue";
import { SearchKeyType } from "./components/searchType";
import PageManage from "@/components/PageManage.vue";
import { exportMiniAccountCommissionManage } from '@/api/reward/reward';
import SetClassify from "@/views/reward/rewardSchemeDet/components/goodsComp/SetClassify.vue";
@Component({
    components: {
        SetClassify,
        Search,
        PageManage,
        CommissionDetList
    }
})
export default class ListApart extends Vue {


    pageSize = 0;

    pageNum = 0;

    total = 0;

    searchType: SearchKeyType = {};

    showGetList: any[] = [];

        /** 获取商品数组信息 */
    @Ref()
    readonly commissionDetList!: CommissionDetList;

    getSearch(data: SearchKeyType) {
        this.searchType = data;
        this.searchType.current = 1;
        this.getCommissionDetList();
    }

    getCommissionDetList() {
        this.commissionDetList.searchType = Object.assign(
            this.commissionDetList.searchType,
            this.searchType
        );
        this.commissionDetList.getCommissionDetList();

    }
    getShowProList(data) {
        this.showGetList = data || [];
        this.total = this.commissionDetList.total;
        this.pageSize = this.commissionDetList.searchType.size as number;
        this.pageNum = this.commissionDetList.searchType.current as number;
    }

    handleSizeChange(val: number) {
        this.commissionDetList.searchType.size = val;
        this.commissionDetList.getCommissionDetList();
    }

    handleCurrentChange(val: number) {
        this.commissionDetList.searchType.current = val;
        this.commissionDetList.getCommissionDetList();
    }

    /**
     * 导出数据
     */
    exportData() {
        this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            // 获取搜索参数
            const searchParams = { ...this.searchType };

            exportMiniAccountCommissionManage(searchParams).then((res) => {
                var blob = new Blob([res.data], {
                    type: "application/x-msdownload;charset=UTF-8",
                });
                // 创建一个blob的对象链接
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                // 把获得的blob的对象链接赋值给新创建的这个 a 链接
                let now = new Date();
                let timestamp = now.getTime();
                link.setAttribute('download', '佣金明细_' + timestamp + '.xls'); // 设置下载文件名
                document.body.appendChild(link);

                // 触发下载
                link.click();
                // 清理
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                this.$message.success('导出成功');
            }).catch((err) => {
                this.$message.error("导出失败: " + err);
            });
        }).catch(() => {
            // 用户取消导出
        });
    }

}
</script>

<style lang="scss" scoped>
@import "../../../assets/styles/goods/index.scss";

.topLine {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &__left {
        display: flex;
    }

    &__right {
        width: 450px;
        display: flex;
        justify-content: space-around;
    }
}

.listBottom {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: fixed;
    bottom: 10px;
    width: 990px !important;
    background-color: white;
    padding: 10px 0px;
    z-index: 10; 
}
</style>