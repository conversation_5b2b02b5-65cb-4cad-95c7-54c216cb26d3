<template>
    <m-card class="form" :needToggle="true">
        <el-form ref="form" :model="searchType" label-width="120px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="用户名称">
                        <el-input v-model="searchType.nikeName" placeholder="请输入用户名称" style="width: 100%;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="用户电话">
                        <el-input v-model="searchType.phone" placeholder="请输入用户电话" style="width: 100%;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="变更时间">
                        <el-date-picker v-model="value1" type="daterange" range-separator="-" start-placeholder="开始时间" style="width: 100%;"
                            end-placeholder="结束时间"  @change="chooseOrderDateTimes">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                
            </el-row>
            <el-row>
                <el-col :span="6">
                    <el-form-item label="分佣标题">
                        <el-input v-model="searchType.remark" placeholder="请输入分佣标题" style="width: 100%;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="变更方式">
                        <el-select v-model="searchType.way" placeholder="请选择" style="width: 100%;">
                            <el-option v-for="item in wayOption" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="primary" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>
    </m-card>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { SearchState, SearchKeyType } from "./searchType";
import DateUtil from "@/store/modules/date";
@Component
export default class Search extends Vue implements SearchState {
    name = "Search";
    searchType = {
        startTime: '',//开始时间
        endTime: '',//结束时间
        phone: '',//用户电话
        nikeName: '',//用户名称
        remark: '',//用户名称
        commissionType: 0,//分佣类型
        way: 0,//变更方式
    } as any;
    commissionTypeOption = [{
        value: 0,
        label: '全部'
    }, {
        value: 1,
        label: '佣金'
    }, {
        value: 3,
        label: '平级'
    }, {
        value: 4,
        label: '级差'
    }, {
        value: 5,
        label: '团队'
    }, {
        value: 6,
        label: '循环分佣'
    }, {
        value: 103,
        label: '佣金转赠'
    }, {
        value: 104,
        label: '订单消费'
    }, {
        value: 105,
        label: '订单取消'
    }, {
        value: 200,
        label: '佣金提现'
    }];
    wayOption = [{
        value: 0,
        label: '全部'
    }, {
        value: 1,
        label: '增加'
    }, {
        value: 2,
        label: '减少'
    }];
    value1 = ''
    created() {

    }
    /**
 * 选择单据日期
 * @param data 
 */
    chooseOrderDateTimes(data: any) {
        this.searchType.startTime = data ? this.dateConversion(data[0]) + " 00:00:00" : "";
        this.searchType.endTime = data ? this.dateConversion(data[1]) + " 23:59:59" : "";
        console.log("this.searchType", this.searchType);
    }
    dateConversion(value: Date) {
        const date = new DateUtil("").getYMDs(value);
        return date;
    }
    search() {
        this.$emit("searchBy", this.searchType);
    }
    reset() {
        this.value1 = ''
        this.searchType = {
            startTime: '',//开始时间
            endTime: '',//结束时间
            phone: '',//用户电话
            nikeName: '',//用户名称
            commissionType: 0,//分佣类型
            way: 0,//变更方式
        }
        this.$emit("searchBy", this.searchType);
    }
}
</script>
<style lang="scss" scoped>
.el-form-item .el-input {
    width: 224px;
}

.el-form-item .el-button {
    width: 90px;
}

@include b(form) {
    transform-origin: left top;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease 0s;

    &.show {
        height: 260px;
        margin-bottom: 20px;
    }

    &.hide {
        margin-bottom: 20px;
        height: 50px;

        .form__btn {
            width: 940px;
            height: 50px;
            background: #f9f9f9;
            line-height: 50px;
            // margin-top: 20px
        }
    }

    @include e(btn) {
        width: 100%;
        position: absolute;
        bottom: 0;
        text-align: center;
        padding-bottom: 20px;

        span {
            cursor: pointer;
        }
    }
}

.page {
    // height: 270px;
    background-color: #f9f9f9;
    margin-bottom: 20px;
}

@include b(search) {
    display: flex;
    flex-wrap: wrap;

    @include e(item) {
        padding: 20px 40px 10px 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @include m(text) {
            width: 60px;
        }
    }

    @include e(icon) {
        width: 40px;
        text-align: center;
        border-left: 1px solid #dcdfe6;
        cursor: pointer;
        vertical-align: middle;
    }
}

@include b(searchButton) {
    margin: 20px 30px;
}
</style>