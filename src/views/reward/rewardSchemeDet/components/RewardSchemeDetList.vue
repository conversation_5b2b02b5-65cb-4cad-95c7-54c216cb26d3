<template>
    <div style="margin-top: 20px">
        <el-table :data="goodList" style="width: 100%">
            <el-table-column label="序号" type="index" width="50">
			</el-table-column>
            <el-table-column prop="billNo" label="单据编号" width="180" >
            </el-table-column>
            <el-table-column prop="billDate" label="单据日期" width="120">
            </el-table-column>
            <el-table-column prop="startTime" label="有效期" width="160">
                <template v-slot="{ row }">
					<span v-if="row.startTime!=null&&row.startTime!=undefined&&row.startTime!=''">{{ row.startTime }}~{{ row.endTime }}</span>
				</template>
            </el-table-column>
            <el-table-column prop="name" label="方案名称" width="230" >
            </el-table-column>
            <el-table-column prop="userName" label="经手人" width="100" >
            </el-table-column>
            <el-table-column prop="createUserName" label="制单人" width="100" >
            </el-table-column>
            <el-table-column prop="remark" label="主表备注" width="120" >
            </el-table-column>
            <el-table-column prop="rewardType" label="奖励类型" width="100" >
                <template v-slot="{ row }">
                  <span v-if="row.rewardType == '1'">佣金</span>
<!--                <span v-if="row.rewardType == '2'">提成</span>-->
                  <span v-if="row.rewardType == '3'">平级</span>
                  <span v-if="row.rewardType == '4'">级差</span>
                  <span v-if="row.rewardType == '5'">团队</span>
                  <span v-if="row.rewardType == '6'">循环分佣</span>
				        </template>
            </el-table-column>
          <el-table-column prop="buyType" label="购买类型" width="100" >
            <template v-slot="{ row }">
              <span v-if="row.buyType == '1'">首单</span>
              <span v-if="row.buyType == '2'">复购</span>
            </template>
          </el-table-column>
            <el-table-column prop="rewardSchemeDetProducts"  label="分佣商品" width="250" >
            </el-table-column>

          <el-table-column prop="rewardSchemeDetProducts"  label="非分佣商品" width="250" >
          </el-table-column>
<!--            <el-table-column prop="priceType" label="价格类型" width="100" >
                <template v-slot="{ row }">
					          <span v-if="row.priceType == '1'">会员价</span>
                    <span v-if="row.priceType == '2'">复购价</span>
                    <span v-if="row.priceType == '3'">实售价</span>
				        </template>
            </el-table-column>-->


            <el-table-column prop="memberLevelName" label="受益等级" width="250" :show-overflow-tooltip="true" >
            </el-table-column>
            <el-table-column prop="detRemark" label="明细备注" width="120" >
            </el-table-column>
            <el-table-column prop="memberTypeName" label="会员类型名称" width="120" />
            <el-table-column prop="orderMemberTypeName" label="订单会员类型名称" width="250" />
            <el-table-column prop="commissionTitle" label="分佣标题" width="120" />
            <el-table-column prop="oneCommissionRate" label="一级下级比例" width="120" />
            <el-table-column prop="twoCommissionRate" label="二级下级比例" width="120" />
            <el-table-column prop="oneCommissionAmount" label="一级下级固定金额" width="120" />
            <el-table-column prop="twoCommissionAmount" label="二级下级固定金额" width="120" />
            <el-table-column prop="commissionAmountSourceName" label="分佣金额来源" width="150" />
        </el-table>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { pageRewardSchemeDet } from '@/api/reward/reward';
import { SearchKeyType } from "./searchType";

@Component({
    components: {

    }
})
export default class RewardSchemeDetList extends Vue {
    name = "RewardSchemeDetList"
    goodList = [];
    mounted() {
        this.getRewardSchemeDet();
    }
    searchType = {
        current: 1,
        billDateSort: 2,
        size: 10
    } as SearchKeyType;

    total = 0;
    /**
   * 获取商品列表
   */
    async getRewardSchemeDet() {
        const param = this.searchType;
        try {
            const res = await pageRewardSchemeDet(param);
            const goodList = res.data.list;
            this.total = res.data.total;
            this.goodList = goodList;
        } catch (error) {
            console.log(error);
        }
        this.$emit("getShowProList", this.goodList);
    }
}
</script>

<style lang="scss" scoped>
.goodList {
    width: 250px;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-right: 20px;
    overflow: hidden;
}

.center {
    display: flex;
    justify-content: center;
}

.digTitle {
    font-size: 17px;
    font-weight: bold;
}
</style>