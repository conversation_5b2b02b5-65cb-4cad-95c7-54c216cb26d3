<template>
    <m-card class="form" :needToggle="true">
        <el-form ref="form" :model="searchType" label-width="90px">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="单据日期">
                        <el-date-picker v-model="value1" type="daterange" range-separator="-" start-placeholder="开始时间"
                            end-placeholder="结束时间" style="width: 330px;" @change="chooseBillDateTimes">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="经手人">
                        <el-input v-model="searchType.userName" placeholder="请输入经手人"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="制单人">
                        <el-input v-model="searchType.createUserName" placeholder="请输入制单人"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="方案名称">
                        <el-input v-model="searchType.name" placeholder="请输入方案名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="有效期">
                        <el-date-picker v-model="value2" type="daterange" range-separator="-" start-placeholder="开始时间"
                            end-placeholder="结束时间" style="width: 330px;" @change="chooseTimes">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="奖励类型">
                        <el-select v-model="searchType.rewardType" placeholder="请选择" style="width: 224px" size="small">
                            <el-option v-for="item in rewardTypeList" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="商品名称">
                        <el-input v-model="searchType.productName" placeholder="请输入商品名称"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="会员等级">
                        <el-input v-model="searchType.memberLevelName" placeholder="请输入会员等级"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="分佣标题">
                        <el-input v-model="searchType.commissionTitle" placeholder="请输入分佣标题"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="订单会员类型">
                        <el-input v-model="searchType.orderMemberTypeName" placeholder="请输入订单会员类型"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="primary" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>
    </m-card>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { SearchState, SearchKeyType } from "./searchType";
import DateUtil from "@/store/modules/date";
@Component
export default class Search extends Vue implements SearchState {
    name = "Search";
    searchType = {
        billDateStartTime: "",//单据日期开始时间
        billDateEndTime: "",//单据日期结束时间
        userName: "",//经手人
        createUserName: "",//制单人
        name: "",//方案名称
        startTime: "",//有效期开始时间
        endTime: "",//有效期结束时间
        rewardType: "",//奖励类型
        productName: "",//商品名称
        memberLevelName: "",//会员等级名称
        orderMemberTypeName: "",//订单会员类型
        commissionTitle: "",//分佣标题
    } as SearchKeyType;
    rewardTypeList = [
        {
            value: "",
            label: "全部"
        },
        {
            value: "1",
            label: "佣金"
        },
        {
            value: "2",
            label: "提成"
        },
    ]
    value1 = ''
    value2 = ''
    mounted() {

    }
    /**
 * 选择单据日期
 * @param data 
 */
    chooseBillDateTimes(data: any) {
        this.searchType.billDateStartTime = data ? this.dateConversion(data[0]) + " 00:00:00" : "";
        this.searchType.billDateEndTime = data ? this.dateConversion(data[1]) + " 23:59:59" : "";
        console.log("this.searchType", this.searchType);
    }
    /**
     * 选择有效期
     * @param data 
     */
    chooseTimes(data: any) {
        this.searchType.startTime = data ? this.dateConversion(data[0]) + " 00:00:00" : "";
        this.searchType.endTime = data ? this.dateConversion(data[1]) + " 23:59:59" : "";
        console.log("this.searchType", this.searchType);

    }
    dateConversion(value: Date) {
        const date = new DateUtil("").getYMDs(value);
        return date;
    }
    search() {
        this.$emit("searchBy", this.searchType);
    }
    reset() {
        this.value1 = ''
        this.value2 = ''
        this.searchType = {
            billDateStartTime: "",//单据日期开始时间
            billDateEndTime: "",//单据日期结束时间
            userName: "",//经手人
            createUserName: "",//制单人
            name: "",//方案名称
            startTime: "",//有效期开始时间
            endTime: "",//有效期结束时间
            rewardType: "",//奖励类型
            productName: "",//商品名称
            memberLevelName: "",//会员等级名称
            orderMemberTypeName: "",//订单会员类型
            commissionTitle: "",//分佣标题
        }
        this.$emit("searchBy", this.searchType);
    }
}
</script>
<style lang="scss" scoped>
.el-form-item .el-input {
    width: 224px;
}

.el-form-item .el-button {
    width: 90px;
}

@include b(form) {
    transform-origin: left top;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease 0s;

    &.show {
        height: 380px;  // 从320px调整到380px，增加高度以适应新增的搜索条件
        margin-bottom: 20px;
    }

    &.hide {
        margin-bottom: 20px;
        height: 50px;

        .form__btn {
            width: 940px;
            height: 50px;
            background: #f9f9f9;
            line-height: 50px;
            // margin-top: 20px
        }
    }

    @include e(btn) {
        width: 100%;
        position: absolute;
        bottom: 0;
        text-align: center;
        padding-bottom: 20px;

        span {
            cursor: pointer;
        }
    }
}

.page {
    // height: 270px;
    background-color: #f9f9f9;
    margin-bottom: 20px;
}

@include b(search) {
    display: flex;
    flex-wrap: wrap;

    @include e(item) {
        padding: 20px 40px 10px 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        @include m(text) {
            width: 60px;
        }
    }

    @include e(icon) {
        width: 40px;
        text-align: center;
        border-left: 1px solid #dcdfe6;
        cursor: pointer;
        vertical-align: middle;
    }
}

@include b(searchButton) {
    margin: 20px 30px;
}
</style>