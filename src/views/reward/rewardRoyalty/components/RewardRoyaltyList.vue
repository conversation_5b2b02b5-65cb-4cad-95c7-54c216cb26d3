<template>
    <div style="margin-top: 20px">
        <el-table :data="goodList" style="width: 100%">
            <el-table-column label="序号" type="index" width="60">
            </el-table-column>
            <el-table-column prop="nikeName" label="会员昵称" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="phone" label="会员号码"  show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="memberLevelName" label="会员等级"  show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="rewardRoyalty" label="提成" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="rewardRoyalty" label="销售单据"  show-overflow-tooltip>
                <template v-slot="{ row }">
                    <el-button type="text" @click="goDetail(row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { searchMiniAccountRoyalty } from '@/api/reward/reward';
import { SearchKeyType } from "./searchType";
@Component({
    components: {

    }
})
export default class RewardRoyaltyList extends Vue {
    name = "RewardRoyaltyList"
    goodList = [];
    mounted() {
        this.getRewardRoyaltyList();
    }
    searchType = {
        current: 1,
        size: 10
    } as SearchKeyType;

    total = 0;
    /**
   * 获取商品列表
   */
    async getRewardRoyaltyList() {
        const param = this.searchType;
        try {
            const res = await searchMiniAccountRoyalty(param);
            const goodList = res.data.list;
            this.total = res.data.total;
            this.goodList = goodList;
        } catch (error) {
            console.log(error);
        }
        this.$emit("getShowProList", this.goodList);
    }

    goDetail(row) {
        this.$router.push({
            name: "rewardRoyaltyDet",
            query: {
                nikeName: row.nikeName,
            },
            params: {
                nikeName: row.nikeName,
            }
        });
    }
}
</script>
<style lang="scss" scoped>
.goodList {
    width: 250px;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-right: 20px;
    overflow: hidden;
}

.center {
    display: flex;
    justify-content: center;
}

.digTitle {
    font-size: 17px;
    font-weight: bold;
}
</style>