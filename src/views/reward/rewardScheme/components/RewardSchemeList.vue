<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
-->
<template>
  <!-- 商品列表 -->
  <div style="margin-top: 20px">
    <m-table :data.sync="goodList" :selection="true" :checked-item.sync="tableCheckedItem" slot="content"
      class="imgView">
      <m-table-column prop="billNo" label="单据编号" width="150">
        <template v-slot="{ row }">
          <span> {{ row.billNo }}</span>

        </template>
      </m-table-column>
      <m-table-column prop="billDate" label="单据日期" :showsSlection="true" width="100">
        <template v-slot="{ row }">
          <span> {{ row.billDate }}</span>
        </template>
      </m-table-column>
      <m-table-column prop="startTime" label="有效期" width="160">
        <template v-slot="{ row }">
          <span v-if="row.startTime != null && row.startTime != undefined && row.startTime != ''">{{ row.startTime }}~{{
            row.endTime }}</span>
        </template>
      </m-table-column>
      <m-table-column prop="userName" label="经手人" width="80">
        <template v-slot="{ row }">
          <span>{{ row.userName }}</span>
        </template>
      </m-table-column>
      <m-table-column prop="name" label="方案名称" width="180">
        <template v-slot="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </m-table-column>
      <m-table-column prop="status" label="状态" width="60">
        <template v-slot="{ row }">
          <el-tag type="warning" v-if="row.status == 0">草稿</el-tag>
          <el-tag type="success" v-else-if="row.status == 1">生效中</el-tag>
          <el-tag type="danger" v-else-if="row.status == -1">失效</el-tag>
          <el-tag type="info" v-else-if="row.status == -2">停止</el-tag>
        </template>
      </m-table-column>
      <m-table-column prop="status" label="审核状态" width="80">
        <template v-slot="{ row }">
          <el-tag type="warning" v-if="row.approvalStatus == 100">待审核</el-tag>
          <el-tag type="success" v-else-if="row.approvalStatus == 101">已审核</el-tag>
          <el-tag type="danger" v-else-if="row.approvalStatus == 200">已驳回</el-tag>
        </template>
      </m-table-column>
      <m-table-column prop="remark" label="备注" width="80">
        <template v-slot="{ row }">
          <span>{{ row.remark }}</span>
        </template>
      </m-table-column>
      <m-table-column prop="userName" label="操作" width="120">
        <template v-slot="{ row }">
          <div class="center">
            <set-drop setName="详情" :dropdownList="itemDropList(row)" @setClick="goDetail(row)"
              @command="getDropdown($event, row)" />
          </div>
        </template>
      </m-table-column>
    </m-table>
    <el-dialog title="审核奖励方案" :visible.sync="approvalFlag" width="30%">
      <el-form :model="approvalForm" ref="approvalForm" label-width="100px">
        <el-form-item label="审核状态" prop="approvalStatus">
          <el-select v-model="approvalForm.approvalStatus" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核原因" prop="approvalReason">
          <el-input type="textarea" :rows="2" placeholder="请输入审核原因" v-model="approvalForm.approvalReason"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="approvalFlag = false;">取 消</el-button>
        <el-button type="primary" @click="approvalHandler()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import SetDrop from "@/views/customer/common/SetDrop.vue";
import { GoodListState } from "./goodListType";
import { SearchKeyType } from "./searchType";
import { pageIntegralActivity, copyIntegral, deactivateIntegral } from "@/api/integralApi/integralApi"
import { pageRewardScheme, approvalRewardScheme, stopRewardScheme,deleteRewardScheme, copyRewardScheme } from '@/api/reward/reward';
import { GoodDetailInfo } from "../goodType";

@Component({
  components: {
    SetDrop
  }
})
export default class RewardSchemeList extends Vue implements GoodListState {
  name = "RewardSchemeList"
  tableCheckedItem = [];


  goodList: Array<GoodDetailInfo> = [];

  get itemDropList() {
    return (row: GoodDetailInfo) => {
      return [
        {
          text: "编辑",
          command: "edit",
          show: row.status == 0 && row.approvalStatus == 100 && (this.isSupper || this.editButton),
          disabled: false
        },
        {
          text: "审核",
          command: "approval",
          show: row.status == -1 && row.approvalStatus == 100 && (this.isSupper || this.toExamineButton),
          disabled: false
        },
        {
          text: "重新提交",
          command: "again",
          show: row.approvalStatus == 200 && (this.isSupper || this.editButton),
          disabled: false
        },
        {
          text: "停止",
          command: "stop",
          show: row.status != -2 && row.approvalStatus == 101 && (this.isSupper || this.stopButton),
          disabled: false
        },
        {
          text: "删除",
          command: "delete",
          show: row.status ==  0 && row.approvalStatus == 100 && (this.isSupper || this.deleteButton),
          disabled: false
        }
      ];
    };
  }
  options = [
    {
      value: '101',
      label: '审核通过'
    },
    {
      value: '200',
      label: '拒绝审核'
    }
  ]
  approvalFlag = false
  approvalForm = {
    id: '',
    approvalStatus: '',
    approvalReason: '',
  }
  searchType = {
    current: 1,
    billNoSort: 2,
    size: 10
  } as SearchKeyType;


  total = 0;

  menuName = "奖励方案";

  buttonList = [];

  isSupper = 0;

  editButtonCode = "rewardScheme.edit";

  editButton = false;

  stopButtonCode = "rewardScheme.stop";

  stopButton = false;

  deleteButtonCode = "rewardScheme.delete";

  deleteButton = false;


  toExamineButtonCode = "rewardScheme.toExamine";

  toExamineButton = false;

  mounted() {
    this.getRewardScheme();
    this.buttonAuth();
  }
  buttonAuth() {
    this.isSupper = this.$STORE.userStore.userInfo.isSupper
    let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

    let buttonList = [];

    authMenuButtonVos.forEach(element => {
      buttonList.push(element.buttonCode);
    });

    this.buttonList = buttonList

    var editButtonData = buttonList.find(e => e == this.editButtonCode);

    if (editButtonData != null && editButtonData != undefined) {
      this.editButton = true;
    }

    var stopButtonData = buttonList.find(e => e == this.stopButtonCode);

    if (stopButtonData != null && stopButtonData != undefined) {
      this.stopButton = true;
    }

    var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);

    if (deleteButtonData != null && deleteButtonData != undefined) {
      this.deleteButton = true;
    }

    var toExamineButtonData = buttonList.find(e => e == this.toExamineButtonCode);

    if (toExamineButtonData != null && toExamineButtonData != undefined) {
      this.toExamineButton = true;
    }

  }
  /**
   * 获取商品列表
   */
  async getRewardScheme() {
    const param = this.searchType;
    try {
      const res = await pageRewardScheme(param);
      const goodList = res.data.list;
      this.total = res.data.total;
      this.goodList = goodList;
    } catch (error) {
      console.log(error);
    }
    this.$emit("getShowProList", this.goodList);
  }

  /**
 * 批量审核选择
 */
  batchExamine() {
    if (this.tableCheckedItem.length == 0) {
      // 请选择要审核的提现申请
      this.$alert('请选择要审核的奖励方案', '批量审核', {
        confirmButtonText: '确定'
      });
    } else {
      var ids = "";
      this.tableCheckedItem.forEach((item, index) => {
        if (ids.length > 0) {
          ids += ','
        }
        ids += item.id
      })
      this.approvalFlag = true
      this.approvalForm.id = ids
    }
  }
  batchCopy() {
    if (this.tableCheckedItem.length == 0) {
      // 请选择要审核的提现申请
      this.$alert('请选择要复制的奖励方案', '复制奖励方案', {
        confirmButtonText: '确定'
      });
    } else {
      this.$confirm(
        `是否复制奖励方案!`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        },
      ).then(() => {
        var ids = "";
        this.tableCheckedItem.forEach((item, index) => {
          if (ids.length > 0) {
            ids += ','
          }
          ids += item.id
        })
        let params = {}
        params.ids = ids
        copyRewardScheme(params).then(res => {
          this.$message.success("复制成功");
          this.getRewardScheme();
        }).catch(err => {
          this.$message.error(err || "网络错误");
        });
      });



    }
  }
  /**
   * 获取下拉框
   */
  getDropdown(val: string | number, row: GoodDetailInfo) {
    if (val == 'edit') {
      this.edit(row)
    }
    if (val == 'again') {
      this.edit(row)
    }
    if (val == 'approval') {
      this.approval(row)
    }
    if (val == 'stop') {
      this.stopRewardScheme(row)
    }
    if (val == 'delete') {
      this.deleteRewardScheme(row)
    }
  }
  /**
   * 详情
   */
  goDetail(item: { id: string; }) {
    this.$router.push({
      name: "addRewardScheme",
      query: {
        id: item.id,
        isDisable: '1',
      },
      params: {
        id: item.id,
        isDisable: '1',
      }
    });
  }
  deleteRewardScheme(row) {
    this.$confirm(
      `是否删除奖励方案!`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    ).then(() => {
      let params = {}
      params.id = row.id
      deleteRewardScheme(params).then(res => {
        this.$message.success("删除成功");
        this.getRewardScheme();
      }).catch(err => {
        this.$message.error(err || "网络错误");
      });
    });
  }
  stopRewardScheme(row) {
    this.$confirm(
      `是否停止奖励方案!`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    ).then(() => {
      let params = {}
      params.id = row.id
      params.status = '-2'
      stopRewardScheme(params).then(res => {
        this.$message.success("停止成功");
        this.getRewardScheme();
      }).catch(err => {
        this.$message.error(err || "网络错误");
      });
    });
  }
  approval(row) {
    this.approvalFlag = true
    this.approvalForm.id = row.id
  }
  approvalHandler() {
    let approvalStatus = this.approvalForm.approvalStatus
    let approvalReason = this.approvalForm.approvalReason
    if (approvalStatus == null || approvalStatus == undefined || approvalStatus == '') {
      this.$message.error("请选择审核状态");
      return;
    }

    if (approvalStatus == '200' && (approvalReason == null || approvalReason == undefined || approvalReason == '')) {
      this.$message.error("拒绝审核，审核原因不能为空！");
      return;
    }
    let params = {}
    params.ids = this.approvalForm.id
    params.approvalStatus = this.approvalForm.approvalStatus
    params.approvalReason = this.approvalForm.approvalReason

    approvalRewardScheme(params).then(res => {
      this.$message.success("审核成功");
      // 关闭模态框
      this.approvalFlag = false;
      this.approvalForm = {
        id: '',
        approvalStatus: '',
        approvalReason: '',
      }
      // 提交数据成功，重新获取一次数据进行渲染
      this.getRewardScheme();
    }).catch(err => {
      this.$message.error(err || "网络错误");
    });
  }
  /**
   * 编辑
   */
  edit(item: { id: string; }) {
    this.$router.push({
      name: "addRewardScheme",
      query: {
        id: item.id,
      },
      params: {
        id: item.id,
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.goodList {
  width: 250px;
  display: flex;
  justify-content: center;
  text-align: center;
  padding-right: 20px;
  overflow: hidden;




}

.center {
  display: flex;
  justify-content: center;
}

.digTitle {
  font-size: 17px;
  font-weight: bold;
}
</style>
