<!-- 2025.6.4有改动的页面 -->
<template>
    <div>
        <el-tabs v-model="activeName">
            <el-tab-pane v-for="(item, index) in list" :key="index" :label="item.modeName" :name="item.modeName"
                :id="item.id"></el-tab-pane>
        </el-tabs>
        <div style="margin: 20px;">
            <span v-if="isDisable == '1'">审核原因：{{ formData.approvalReason }}</span>
        </div>
        <el-form ref="form" :model="formData" label-width="90px" :disabled="isDisable == '1'" label-position="left">
            <div>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="单据编号">
                            <el-input v-model="formData.billNo" maxlength="32" readonly placeholder="请输入单据编号"
                                style="width: 220px;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="单据日期">
                            <el-date-picker v-model="value0" align="right" type="date" placeholder="请选择开始时间"
                                @change="chooseTime">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="有效期">
                            <el-date-picker v-model="value1" type="daterange" range-separator="-"
                                start-placeholder="开始时间" end-placeholder="结束时间" style="width: 330px;"
                                @change="chooseTimes">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="经办人">
                            <el-select v-model="formData.userId" placeholder="请选择" filterable
                                :filter-method="getAccountInfo" @change="selectUser($event)">
                                <el-option v-for="item in Handlerlist" :key="item.language" :label="item.nikeName"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="方案名称">
                            <el-input v-model="formData.name" maxlength="32" placeholder="请输入方案名称"
                                style="width: 220px;"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-form-item label="备注">
                        <el-input type="textarea" placeholder="请输入内容" v-model="formData.remark" maxlength="200"
                            show-word-limit> </el-input>
                    </el-form-item>
                </el-row>
            </div>
        </el-form>
        <template>
            <el-table :data="tableData" style="width: 100%" border ref="multipleSelection" max-height="580px">
                <el-table-column :align="'center'" type="index" width="50">
                </el-table-column>
                <el-table-column :align="'center'" label="" width="80">
                    <template slot-scope="scope">
                        <el-button @click="handleClickmo(scope.$index, scope.row)" type="text" size="medium"
                            icon="el-icon-plus"></el-button>
                        <el-button type="text" size="medium" @click.prevent="removeDomain(scope.$index)"
                            icon="el-icon-minus"></el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="address" label="分佣标题" width="160">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.commissionTitle" placeholder="请输入分佣标题"
                            :disabled="isDisable == '1'"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="分佣金额来源" width="180">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.commissionAmountSource" clearable placeholder="请选择"
                            :disabled="isDisable == '1'">
                            <el-option v-for="(item, i) in commissionAmountSource" :key="i" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="订单会员类型" width="200">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.orderMemberTypeList" placeholder="请选择" multiple 
                            :disabled="isDisable == '1'">
                            <el-option v-for="(item, i) in memberTypeList" :key="i" :label="item.name" :value="item.id">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="会员类型" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.memberTypeId" placeholder="请选择"
                            @change="selectMemberType($event, scope.row)" :disabled="isDisable == '1'">
                            <el-option v-for="(item, i) in memberTypeList" :key="i" :label="item.name" :value="item.id">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column width="200">
                    <template slot="header" slot-scope="scope">
                        受益等级
                        <el-tooltip class="item" effect="dark" content="受益方" placement="top">
                            <i class="el-icon-info"></i>
                        </el-tooltip>
                    </template>
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.memberLevelList" placeholder="请选择" multiple 
                            :disabled="isDisable == '1'">
                            <el-option v-for="item in gtlevel" :key="item.id" :label="item.memberLevel"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column width="200">
                    <template slot="header" slot-scope="scope">
                        条件升级等级
                    </template>
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.upMemberLevelList" placeholder="请选择" multiple 
                            :disabled="isDisable == '1'">
                            <el-option v-for="item in gtlevel" :key="item.id" :label="item.memberLevel"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column width="200">
                    <template slot="header" slot-scope="scope">
                        条件直推等级
                        <!-- <el-tooltip class="item" effect="dark" content="受益方" placement="top">
                            <i class="el-icon-info"></i>
                        </el-tooltip> -->
                    </template>
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.directMemberLevelList" placeholder="请选择" multiple 
                            :disabled="isDisable == '1'">
                            <el-option v-for="item in gtlevel" :key="item.id" :label="item.memberLevel"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column label="条件直推指标" width="160">
                    <template slot-scope="scope">
                        <el-input-number :min="0" placeholder="请输入条件直推指标" :controls="false"
                            v-model="scope.row.directMemberCount" class="input_number"
                            :disabled="isDisable == '1'"></el-input-number>
                    </template>
                </el-table-column>
                <el-table-column label="奖励类型" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.rewardType" placeholder="请选择" :disabled="isDisable == '1'"
                            @change="(newVal) => changeRewardType(scope.row, newVal)">
                            <el-option v-for="(item, key) in rewardTypeList" :key="key" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column label="购买类型" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.buyType" clearable placeholder="请选择" :disabled="isDisable == '1'">
                            <el-option v-for="(item, key) in buyType" :key="key" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="分佣商品名称" width="200" prop="rewardSchemeDetProductName" show-overflow-tooltip>
                    <template slot-scope="scope" >
                        <div v-if="scope.row.rewardSchemeDetProductName">
                            <div>
                                {{ scope.row.rewardSchemeDetProductName }}
                            </div>
                            <div style="color: #409EFF;cursor: pointer;" v-if="isDisable != '1'"
                                @click="addTemPackage(scope.row, scope.$index, 1)">
                                重新选择
                            </div>
                        </div>
                        <div v-else style="color: #409EFF;cursor: pointer;"
                            @click="addTemPackage(scope.row, scope.$index, 1)">
                            请选择分佣商品
                        </div>
                    </template>
                </el-table-column>

                <el-table-column label="非分佣商品名称" width="200" prop="rewardSchemeDetNonProductName" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <div v-if="scope.row.rewardSchemeDetNonProductName">
                            <div>
                                {{ scope.row.rewardSchemeDetNonProductName }}
                            </div>
                            <div style="color: #409EFF;cursor: pointer;" v-if="isDisable != '1'"
                                @click="addTemPackage(scope.row, scope.$index, 2)">
                                重新选择
                            </div>
                        </div>
                        <div v-else style="color: #409EFF;cursor: pointer;"
                            @click="addTemPackage(scope.row, scope.$index, 2)">
                            请选择非分佣商品
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="级差计算方式" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.differCalType" clearable placeholder="请选择"
                            :disabled="isDisable == '1'">
                            <el-option v-for="(item, key) in differCalType" :key="key" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <!-- <el-table-column label="商品名称" width="200">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.productName" @change="selectInspectType($event, scope.row)"
                            placeholder="请选择" filterable remote :remote-method="(query) => dataFilter(query, scope)"
                            @click.native="chooseProductSelect(scope)" :disabled="isDisable == '1'">
                            <el-option v-for="(item, key) in library" :key="key"
                                :label="`${item.goodsCode}.${item.name}.${item.skuStock}`" :value="item.skuId">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column> -->
                <!-- <el-table-column label="商品编码" width="110">
                    <template slot-scope="scope">
                        <div>{{ scope.row.goodsCode }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="商品规格" width="160">
                    <template slot-scope="scope">
                        <div>{{ scope.row.skuStock }}</div>
                    </template>
                </el-table-column> -->
                <!-- <el-table-column label="价格类型" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.priceType" placeholder="请选择" :disabled="isDisable == '1'">
                            <el-option v-for="(item, key) in priceTypeList" :key="key" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column> -->

                <el-table-column label="一级下级比例" width="160">
                    <template slot-scope="scope">
                        <!-- <el-input-number :min="0" :controls="false" v-model="scope.row.oneCommissionRate"
                            class="input_number" :disabled="isDisable == '1'"></el-input-number> -->
                        <el-input v-model="scope.row.oneCommissionRate" placeholder="请输入"
                            @change="changeInput(scope.row, 'oneCommissionRate', $event)"
                            :disabled="isDisable == '1'"></el-input>

                    </template>
                </el-table-column>
                <el-table-column label="二级下级比例" width="160">
                    <template slot-scope="scope">
                        <!-- <el-input-number :min="0" :controls="false" v-model="scope.row.twoCommissionRate"
                            class="input_number" :disabled="isDisable == '1'"></el-input-number> -->
                        <el-input v-model="scope.row.twoCommissionRate" placeholder="请输入"
                            @change="changeInput(scope.row, 'twoCommissionRate', $event)"
                            :disabled="isDisable == '1'"></el-input>

                    </template>
                </el-table-column>
                <el-table-column label="一级下级固定金额" width="160">
                    <template slot-scope="scope">
                        <!-- <el-input-number :min="0" :controls="false" v-model="scope.row.oneCommissionAmount"
                            class="input_number" :disabled="isDisable == '1'"></el-input-number> -->
                        <el-input v-model="scope.row.oneCommissionAmount" placeholder="请输入"
                            @change="changeInput(scope.row, 'oneCommissionAmount', $event)"
                            :disabled="isDisable == '1'"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="二级下级固定金额" width="160">
                    <template slot-scope="scope">
                        <!-- <el-input-number :min="0" :controls="false" v-model="scope.row.twoCommissionAmount"
                            class="input_number" :disabled="isDisable == '1'"></el-input-number> -->
                        <el-input v-model="scope.row.twoCommissionAmount" placeholder="请输入"
                            @change="changeInput(scope.row, 'twoCommissionAmount', $event)"
                            :disabled="isDisable == '1'"></el-input>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="提成比例%" width="160">
                    <template slot-scope="scope">
                        <el-input-number :min="0" :controls="false" v-model="scope.row.royaltyRate" class="input_number"
                            :disabled="isDisable == '1' || scope.row.royaltyDisplay"></el-input-number>
                    </template>
                </el-table-column>
                <el-table-column label="提成固定金额" width="160">
                    <template slot-scope="scope">
                        <el-input-number :min="0" :controls="false" v-model="scope.row.royaltyAmount"
                            class="input_number"
                            :disabled="isDisable == '1' || scope.row.royaltyDisplay"></el-input-number>
                    </template>
                </el-table-column> -->

                <!-- <el-table-column label="平级处理" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.sameLevelFlag" placeholder="请选择" clearable
                            :disabled="isDisable == '1'">
                            <el-option v-for="(item, i) in gradationTypeList" :key="i" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column label="级差处理" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.differFlag" placeholder="请选择" clearable
                            :disabled="isDisable == '1'">
                            <el-option v-for="(item, i) in gradationTypeList" :key="i" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column> -->

                <el-table-column label="业务员分佣" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.salesmanFlag" placeholder="请选择" :disabled="isDisable == '1'">
                            <el-option v-for="(item, i) in salesmanFlag" :key="i" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <el-table-column label="自身下单分佣" width="160">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.selfCommission" placeholder="请选择" :disabled="isDisable == '1'">
                            <el-option v-for="(item, i) in salesmanFlag" :key="i" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>



                <el-table-column prop="address" label="备注" width="160">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.remark" @input="calculThree(scope.row)" placeholder="请输入内容"
                            :disabled="isDisable == '1'"></el-input>
                    </template>
                </el-table-column>
            </el-table>
        </template>
        <div class="primary__bottom">
            <el-button type="primary" @click="submit('-1')" v-if="isDisable != '1'">保存</el-button>
            <el-button type="primary" @click="submit('0')" v-if="isDisable != '1'">存为草稿</el-button>
            <el-button @click="editSecUnit" v-if="isDisable != '1'">取 消</el-button>
            <el-button @click="newly" v-if="isDisable == '1'">返 回</el-button>
        </div>
        <ChooseGoods ref="chooseGoods" @handleGoods="handleGoods">
        </ChooseGoods>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from "vue-property-decorator";
import DateUtil from "@/store/modules/date";
import { getAccountInfo, ByNameOrNumber } from '@/api/warehouse/warehouse';
import { getReceiptNoAndUser, addRewardScheme, queryRewardScheme, editRewardScheme } from '@/api/reward/reward';
import {
    getMemberLevelList,
} from "@/api/customer/customer";

import {
    getMemberType,
    selectMemberLevelList,
} from "@/api/sign/index";
import ChooseGoods from "./components/ChooseGoods.vue";

@Component({
    components: {
        ChooseGoods
    }
})
export default class pointsGoodsDetails extends Vue {
    title = ''
    value0 = ''
    value1 = []
    activeName = '发布奖励方案'
    arrKey: number = 0
    list = [{ modeName: '发布奖励方案', id: '' }]
    formData = {
        billNo: "",//单据编号 
        billDate: "",//单据日期
        startTime: "",//有效期开始时间
        endTime: "",//有效期结束时间
        userId: "",//经手人id
        userName: "",//经手人名称
        name: "",//方案名称
        remark: "",//备注
        list: [],//奖励方案明细
    };
    isDisable = '0'
    //经办人
    Handlerlist = [];
    //商品编码的下拉框模块
    library = [];
    //会员等级
    gtlevel = [];
    //新增必填项
    tableData = [{
        arrKey: 0,
        rewardType: '',//奖励类型
        productId: '',//商品id
        skuStock: '',//商品规格
        skuId: '',//规格id
        productName: '',//商品名称
        goodsCode: '',//商品编码
        priceType: '',//价格类型
        memberTypeId: '',//会员类型ID
        memberLevelId: '',//受益等级id
        upMemberLevelId: '',//条件升级等级ID
        directMemberLevelIds: '',//条件直推等级ID
        orderMemberTypeIds: '',//条件升级等级ID
        memberLevelName: '',//会员等级名称
        oneCommissionRate: undefined,//一级下级比例
        twoCommissionRate: undefined,//二级下级比例
        oneCommissionAmount: undefined,//一级下级固定金额
        twoCommissionAmount: undefined,//二级下级固定金额
        royaltyRate: undefined,//提成比例
        royaltyAmount: undefined,//提成固定金额
        remark: "",//明细备注
        royaltyDisplay: false,
        commissionTitle: "",//分佣标题
        commissionAmountSource: "",//分佣金额来源
        salesmanFlag: undefined,//业务员分佣
        selfCommission: undefined,//自身下单分佣

        orderMemberTypeList: [],//订单会员类型
        memberLevelList: [],//受益等级
        upMemberLevelList: [],//条件升级等级
        directMemberLevelList: [],//条件直推等级

        directMemberCount: undefined,//条件直推指标
        rewardSchemeDetProductList: [],//分佣商品
        rewardSchemeDetNonProductList: [],//非分佣商品
        rewardSchemeDetProductName: '',//分佣商品名字
        rewardSchemeDetNonProductName: '',//非分佣商品名字

        circularSubdivision: false, //是否为循环分佣

    }]
    rewardTypeList = [
        {
            value: '1',
            label: '佣金'
        },
        // {
        //     value: '2',
        //     label: '提成'
        // },
        {
            value: '3',
            label: '平级'
        },
        {
            value: '4',
            label: '级差'
        },
        {
            value: '5',
            label: '团队'
        },
        {
            value: '6',
            label: '循环分佣'
        },
    ]
    priceTypeList = [
        {
            value: '1',
            label: '会员价'
        },
        {
            value: '2',
            label: '复购价'
        },
        {
            value: '3',
            label: '实售价'
        }
    ]
    gradationTypeList = [
        {
            value: 0,
            label: '否'
        },
        {
            value: 1,
            label: '是'
        },
    ]
    buyType = [
        {
            value: 1,
            label: '首单'
        },
        {
            value: 2,
            label: '复购'
        },
    ]
    salesmanFlag = [
        {
            value: 0,
            label: '否'
        },
        {
            value: 1,
            label: '是'
        },
    ]
    differCalType = [
        {
            value: 1,
            label: '订单总额'
        },
        {
            value: 2,
            label: '分佣率'
        },
    ]
    commissionAmountSource = [
        {
            value: 1,
            label: '订单实际支付金额'
        },
        {
            value: 2,
            label: '佣金金额'
        },
        // {
        //     value: 3,
        //     label: '订单金额'
        // },
    ]
    equalLevelVal = ''
    gradationVal = ''

    memberTypeList = [];



    created() {
        this.getAccountInfo();
        // this.getMemberLevelList();

        if (this.$route.query.isDisable == '1') {
            this.isDisable = this.$route.query.isDisable
        }

        if (this.$route.query.id) {
            this.queryRewardScheme()
            this.title = '编辑奖励方案'
        } else {
            this.receiptNoAndUser();
            this.title = '新增奖励方案'
            this.getMemberType();
        }


    }
    isNumber(str: any) {
        return !isNaN(str) && !isNaN(parseFloat(str));
    }
    areAllNumbersPositive(str: any) {
        if (typeof str !== "string" || str.trim() === "") {
            return false;
        }

        const numbers = str.split(",");
        const numberRegex = /^[+]?\d*\.?\d+$/; // 匹配整数或小数（正数）

        for (const numStr of numbers) {
            const trimmed = numStr.trim();

            // 检查是否符合数字格式，并且 > 0
            if (!numberRegex.test(trimmed) || parseFloat(trimmed) <= 0) {
                return false;
            }
        }

        return true;
    }
    setSwitch(row: any, string: string,) {
        switch (string) {
            case 'oneCommissionRate':
                row.oneCommissionRate = '';
                break;
            case 'oneCommissionAmount':
                row.oneCommissionAmount = '';
                break;
            case 'twoCommissionRate':
                row.twoCommissionRate = '';
                break;
            case 'twoCommissionAmount':
                row.twoCommissionAmount = '';
                break;
        }
    }

    changeInput(row: any, string: string, value: any) {

        if (row.circularSubdivision) {
            console.log(value);
            if (!this.areAllNumbersPositive(value)) {
                this.$message.error('每个值都必须为数字，须为大于0');
                this.setSwitch(row, string);
            }
        } else {

            if (value != null && value != undefined &&value!="") {
                if (this.isNumber(value)) {
                    if (value * 1 < 0) {
                        this.$message.error('每个值都必须为大于0');
                        this.setSwitch(row, string);
                    }
                } else {
                    this.$message.error('每个值都必须为数字');
                    this.setSwitch(row, string);
                }
            }
        }
        // switch (string) {
        //     case 'oneCommissionRate':
        //         console.log("星期日");
        //         break;
        //     case 'oneCommissionAmount':
        //         console.log("星期一");
        //         break;
        //     case 'twoCommissionRate':
        //         console.log("星期二");
        //         break;
        //     case 'twoCommissionAmount':
        //         console.log("星期二");
        //         break;
        // }
    }

    handleGoods(val: any) {
        if (this.selectNum == 1) {
            // 分佣商品
            this.tableData[this.selectIndex].rewardSchemeDetProductList = val
            let result = this.tableData[this.selectIndex].rewardSchemeDetProductList.map(item => item.productName).join('、');
            this.tableData[this.selectIndex].rewardSchemeDetProductName = result;
        } else {
            // 非分佣商品
            this.tableData[this.selectIndex].rewardSchemeDetNonProductList = val;
            let result = this.tableData[this.selectIndex].rewardSchemeDetNonProductList.map(item => item.productName).join('、');
            this.tableData[this.selectIndex].rewardSchemeDetNonProductName = result;

        }
        console.log("handleGoods=", this.tableData[this.selectIndex]);



    }

    selectIndex = 0;
    selectNum = 0;

    addTemPackage(row: any, index: number, num: number) {
        this.selectIndex = index;
        this.selectNum = num;
        // if (num == 1) {
        //     // 分佣商品
        //     this.$refs.chooseGoods.products = row.rewardSchemeDetProductList  
        // } else {
        //     // 非分佣商品
        //     this.$refs.chooseGoods.products = row.rewardSchemeDetNonProductList
        // }
        this.$refs.chooseGoods.searchType = {
            productName: "",
            showCategoryId: "",
            status: "1",
        }

        this.$refs.chooseGoods.searchGoods();
    }

    getMemberType() {
        getMemberType({ status: 1 }).then(res => {
            console.log("会员类型分组=", res);
            this.memberTypeList = res.data;
            this.orderMemberLevelIdList = res.data;

            if (this.tableData.length) {
                console.log("this.tableData=", this.tableData);

                this.tableData.forEach(item => {
                    let gtlevel: any = this.memberTypeList.find(items => items.id == item.memberTypeId)
                    console.log("gtlevel=", gtlevel);

                    if (gtlevel) {
                        this.selectMemberLevelList(gtlevel.id);
                    }

                })
            }
        }).catch(err => {
            this.$message.error(err);
        })
    }
    queryRewardScheme() {
        let options = { id: this.$route.query.id }

        queryRewardScheme(options).then((res) => {
            console.log("编辑奖励方案=", res);
            this.formData = res.data
            if (res.data.billDate != null && res.data.billDate != undefined && res.data.billDate != '') {
                this.value0 = res.data.billDate
                this.formData.billDate = this.formData.billDate + " 00:00:00"
            }
            if (res.data.startTime != null && res.data.startTime != undefined && res.data.startTime != '') {
                this.value1.push(res.data.startTime)
                this.formData.startTime = this.formData.startTime + " 00:00:00"
            }
            if (res.data.endTime != null && res.data.endTime != undefined && res.data.endTime != '') {
                this.value1.push(res.data.endTime)
                this.formData.endTime = this.formData.endTime + " 23:59:59"
            }

            let list = res.data.list
            list.forEach(((item: any, index: number | string) => {
                item.arrKey = index
                if (item.rewardType != null) {
                    item.rewardType = item.rewardType + ""
                } else {
                    item.rewardType = ""
                }
                if (item.priceType != null) {
                    item.priceType = item.priceType + ""
                } else {
                    item.priceType = ""
                }
                if (item.oneCommissionAmount == null) {
                    item.oneCommissionAmount = undefined;
                }
                if (item.oneCommissionRate == null) {
                    item.oneCommissionRate = undefined;
                }
                if (item.twoCommissionAmount == null) {
                    item.twoCommissionAmount = undefined;
                }
                if (item.twoCommissionRate == null) {
                    item.twoCommissionRate = undefined;
                }
                if (item.royaltyAmount == null) {
                    item.royaltyAmount = undefined;

                }
                if (item.royaltyRate == null) {
                    item.royaltyRate = undefined;
                }

                if (item.rewardType == '1') {
                    item.royaltyDisplay = true
                } else {
                    item.royaltyDisplay = false
                }

                if (!item.memberTypeId) {
                    item.memberLevelId = '';
                    item.memberLevelName = '';
                }

            }))
            console.log("list", list);
            if (res.data.list.length) {
                this.tableData = list;

            }
            this.getMemberType();
        }).catch((err) => {
            this.$message.error(err);
        })
    }
    changeRewardType(row, value) {
        console.log("row", row);
        console.log("value", value);
        row.oneCommissionRate = undefined
        row.twoCommissionRate = undefined
        row.oneCommissionAmount = undefined
        row.twoCommissionAmount = undefined
        row.royaltyRate = undefined
        row.royaltyAmount = undefined
        if (value == '1') {
            row.royaltyDisplay = true;
        } else {
            row.royaltyDisplay = false;
        }

        row.circularSubdivision = value == 6 ? true : false;
    }
    receiptNoAndUser() {
        getReceiptNoAndUser({}).then(res => {
            console.log("res", res);
            this.formData.billNo = res.data.billNo
            this.formData.billDate = res.data.billDate + " 00:00:00"
            this.formData.userId = res.data.userId
            this.formData.userName = res.data.userName
            this.value0 = res.data.billDate
        })
    }
    chooseTime(data: any) {
        this.formData.billDate = data ? this.dateConversion(data) + " 00:00:00" : "";
    }
    chooseTimes(data: any) {
        console.log(this.value1);
        this.formData.startTime = data ? this.dateConversion(data[0]) + " 00:00:00" : "";
        this.formData.endTime = data ? this.dateConversion(data[1]) + " 23:59:59" : "";
        console.log("this.formData", this.formData);

    }
    dateConversion(value: Date) {
        const date = new DateUtil("").getYMDs(value);
        return date;
    }

    //获取经办人
    getAccountInfo() {
        const param = {
            current: 1,
            size: 10,
        }
        getAccountInfo(param).then(res => {
            this.Handlerlist = res.data.list
        })
    }
    getMemberLevelList() {
        getMemberLevelList({}).then(res => {
            this.gtlevel = res.data
        })
    }
    submit(type) {

        if (type == '-1') {
            if (this.formData.billNo == '') {
                this.$message.error("单据编号不能为空！");
                return
            }
            if (this.formData.billDate == '') {
                this.$message.error("单据日期不能为空!");
                return
            }
            if (this.formData.startTime == '') {
                this.$message.error("有效期-开始时间不能为空!");
                return
            }
            if (this.formData.endTime == '') {
                this.$message.error("有效期-结束时间不能为空!");
                return
            }
            if (this.formData.userId == '') {
                this.$message.error("经手人不能为空!");
                return
            }
            if (this.formData.name == '') {
                this.$message.error("方案名称不能为空！");
                return
            }
            // 处理四个字段为""->null
            this.tableData.forEach(item => {
                ['oneCommissionRate', 'oneCommissionAmount', 'twoCommissionRate', 'twoCommissionAmount'].forEach(key => {
                     if (item[key] === "") {
                         item[key] = null;
                    }
                });
            });

            var list = this.tableData
            for (var i = 0; i < list.length; i++) {
                let e = list[i];

                //会员类型判断
                if (e.memberTypeId == null || e.memberTypeId == undefined || e.memberTypeId == "") {
                    this.$message.error("会员类型不能为空！");
                    return false;
                }
                if (!e.orderMemberTypeList.length) {
                    this.$message.error("订单会员类型不能为空！");
                    return false;
                }
                if (e.commissionTitle == null || e.commissionTitle == undefined || e.commissionTitle == "") {
                    this.$message.error("分佣标题不能为空！");
                    return false;
                }
                if (e.rewardType == null || e.rewardType == undefined || e.rewardType == "") {
                    this.$message.error("奖励类型不能为空！");
                    return false;
                }
                if ((e.oneCommissionRate == null || e.oneCommissionRate == null) &&
                    (e.oneCommissionAmount == null || e.oneCommissionAmount == null) &&
                    (e.twoCommissionAmount == null || e.twoCommissionAmount == null) &&
                    (e.twoCommissionRate == null || e.twoCommissionRate == null)) {
                    this.$message.error("一级下级比例，一级下级固定金额，二级下级比例，二级下级固定金额不能都为空！");
                    return false;
                }
                if (e.oneCommissionRate != null && e.oneCommissionRate != undefined && e.oneCommissionRate != ""
                    && e.oneCommissionAmount!=null && e.oneCommissionAmount != undefined && e.oneCommissionAmount != "") {
                    this.$message.error("一级下级比例，一级下级固定金额不能都设置值！");
                    return false;
                }
                if (e.twoCommissionRate != null && e.twoCommissionRate != undefined && e.twoCommissionRate != ""
                    && e.twoCommissionAmount != null && e.twoCommissionAmount != undefined&& e.twoCommissionAmount != "") {
                    this.$message.error("二级下级比例，二级下级固定金额不能都设置值！");
                    return false;
                }
            }
        }

        var param = this.formData
        param.approvalStatus = '100'
        param.status = type
        param.list = this.tableData
        param.billDate = param.billDate
        param.startTime = param.startTime
        param.endTime = param.endTime

        console.log(param);


        if (this.$route.query.id) {
            param.id = this.$route.query.id
            this.editRewardScheme(param)
        } else {
            this.addRewardScheme(param)
        }

    }
    editRewardScheme(param) {
        editRewardScheme(param).then(res => {
            this.$message({
                message: res.data,
                type: 'success'
            });
            this.newly()
        }).catch(err => {
            this.$message.error(err || "网络错误");
            this.disableCommit = false
        });
    }
    addRewardScheme(param) {
        addRewardScheme(param).then(res => {
            this.$message({
                message: res.data,
                type: 'success'
            });
            this.newly()
        }).catch(err => {
            this.$message.error(err || "网络错误");
            this.disableCommit = false
        });
    }
    newly() {
        this.$router.push({ name: "rewardScheme" });
    }
    editSecUnit() {
        this.$confirm(
            `确定退出新增奖励方案页面?退出后，未保存的信息将不会保留!`,
            "提示",
            {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            },
        ).then(() => {
            this.newly()
        })
    }
    //动态增加 新增模态框的行
    handleClickmo(scope: any, row: any) {
        this.arrKey++
        this.tableData.push({
            arrKey: this.arrKey,
            rewardType: '',//奖励类型
            productId: '',//商品id
            skuStock: '',//商品规格
            skuId: '',//规格id
            productName: '',//商品名称
            goodsCode: '',//商品编码
            priceType: '',//价格类型
            memberTypeId: '',//会员类型ID
            memberLevelId: '',//受益等级id
            upMemberLevelId: '',//条件升级等级ID
            directMemberLevelIds: '',//条件直推等级ID
            orderMemberTypeIds: '',//条件升级等级ID
            memberLevelName: '',//会员等级名称
            oneCommissionRate: undefined,//一级下级比例
            twoCommissionRate: undefined,//二级下级比例
            oneCommissionAmount: undefined,//一级下级固定金额
            twoCommissionAmount: undefined,//二级下级固定金额
            royaltyRate: undefined,//提成比例
            royaltyAmount: undefined,//提成固定金额
            remark: "",//明细备注
            royaltyDisplay: false,
            commissionTitle: "",//分佣标题
            commissionAmountSource: "",//分佣金额来源
            salesmanFlag: undefined,//业务员分佣
            selfCommission: undefined,//自身下单分佣

            orderMemberTypeList: [],//订单会员类型
            memberLevelList: [],//受益等级
            upMemberLevelList: [],//条件升级等级
            directMemberLevelList: [],//条件直推等级

            directMemberCount: undefined,//条件直推指标
            rewardSchemeDetProductList: [],//分佣商品
            rewardSchemeDetNonProductList: [],//非分佣商品
            rewardSchemeDetProductName: '',//分佣商品名字
            rewardSchemeDetNonProductName: '',//非分佣商品名字

            circularSubdivision: false, //是否为循环分佣
        });

    }
    //动态删除
    removeDomain(index) {
        //当只有一行数据的时候不允许删除
        if (this.tableData.length == 1) {
            return
        }
        this.arrKey--//你找找有没有方法，删除对应下标的元素不改变下标的方法 只能说百度了
        console.log("删除：" + index)
        for (var i = index + 1; i < this.tableData.length; i++) {
            this.tableData[i].arrKey--;
            console.log(i)
        }
        this.tableData.splice(index, 1)
    }
    selectUser(userId) {
        let item = this.Handlerlist.find(items => userId == items.id)
        this.formData.userId = item.id
        this.formData.userName = item.nikeName
        console.log("this.formData", this.formData);
    }
    selectInspectType(inspectType, scope) {
        let item = this.library.find(items => inspectType == items.skuId)
        this.tableData[scope.arrKey].productName = item.name//商品名称
        this.tableData[scope.arrKey].productId = item.productId//商品id
        this.tableData[scope.arrKey].goodsCode = item.goodsCode//商品编码
        this.tableData[scope.arrKey].skuStock = item.skuStock//商品规格
        this.tableData[scope.arrKey].skuId = item.skuId//规格id
        this.dataFilter('', scope)
    }

    selectMemberType(memberId, scope) {

        scope.memberLevelList = [];
        scope.upMemberLevelList = [];
        scope.directMemberLevelList = [];

        this.selectMemberLevelList(memberId);
    }

    selectMemberLevelList(memberTypeId: any) {
        selectMemberLevelList({ memberTypeId }).then(res => {
            console.log("selectMemberLevelList=", res);
            if (res.data) {
                this.gtlevel = res.data;
            } else {
                this.gtlevel = [];
            }

        }).catch(err => {
            this.$message.error(err)
        })
    }



    chooseProductSelect(scope) {
        this.dataFilter('', scope)
    }
    //商品编码搜索框
    dataFilter(keyword: string, scope: any) {
        const param = {
            keyword: keyword,
            current: 1,
            size: 10,
        }
        ByNameOrNumber(param).then(res => {
            this.library = res.data.list
        })
    }
}
</script>

<style lang="scss" scoped>
// 全局css 加上以下代码，可以隐藏上下箭头

// 取消input的上下箭头
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;

}

/deep/ input::-webkit-outer-spin-button {
    -webkit-appearance: none !important;

}

/deep/ input[type="number"] {
    -moz-appearance: textfield;

}

.contain {
    width: 400px;
    min-height: 200px;
    max-height: 500px;
    padding: 3px;
    border: 1px solid #a0b3d6;
    font-size: 12px;
    overflow-x: hidden;
    overflow-y: auto;
}

.primary__bottom {
    margin-top: 30px;
    text-align: center;

    .el-button {
        width: 100px;
        font-size: 16px;
        font-weight: 700;
    }
}
</style>