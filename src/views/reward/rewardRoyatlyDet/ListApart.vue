<template>
    <div>
        <Search @searchBy="getSearch" ref="Search"></Search>
        <!-- 导出按钮 -->


        <div class="topLine">
            <div class="topLine__left">
                <div class="all">
                    <div class="setClassify" style="margin-left: 40px">
                        <el-button type="primary" @click="exportData">导出列表</el-button>
                    </div>
                </div>
            </div>
        </div>
        <RewardRoyatlyDetList ref="rewardRoyatlyDetList" @getShowProList="getShowProList">
        </RewardRoyatlyDetList>
        <div style="height: 20px;"></div>
        <div class="listBottom">
            <!-- 设置分类 -->
            <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handleCurrentChange" style="margin-top: 0px"></PageManage>
        </div>
    </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch, Ref } from "vue-property-decorator";
import Search from "./components/Search.vue";
import { SearchKeyType } from "./components/searchType";
import RewardRoyatlyDetList from "./components/RewardRoyatlyDetList.vue";
import PageManage from "@/components/PageManage.vue";
import { exportMiniAccountRoyaltyDet } from '@/api/reward/reward';
import SetClassify from "@/views/reward/rewardSchemeDet/components/goodsComp/SetClassify.vue";
@Component({
    components: {
        SetClassify,
        Search,
        RewardRoyatlyDetList,
        PageManage
    }
})
export default class ListApart extends Vue {

    /** 获取商品数组信息 */
    @Ref()
    readonly rewardRoyatlyDetList!: RewardRoyatlyDetList;

    pageSize = 0;

    pageNum = 0;

    total = 0;

    searchType: SearchKeyType = {};
    showGetList: any[] = [];
    getSearch(data: SearchKeyType) {
        this.searchType = data;
        this.searchType.current = 1;
        this.getRewardRoyatlyDetList();
    }

    getRewardRoyatlyDetList() {
        this.rewardRoyatlyDetList.searchType = Object.assign(
            this.rewardRoyatlyDetList.searchType,
            this.searchType
        );
        this.rewardRoyatlyDetList.getRewardRoyatlyDet();

    }
    getShowProList(data) {
        this.showGetList = data || [];
        this.total = this.rewardRoyatlyDetList.total;
        this.pageSize = this.rewardRoyatlyDetList.searchType.size as number;
        this.pageNum = this.rewardRoyatlyDetList.searchType.current as number;
    }

    handleSizeChange(val: number) {
        this.rewardRoyatlyDetList.searchType.size = val;
        this.rewardRoyatlyDetList.getRewardRoyatlyDet();
    }

    handleCurrentChange(val: number) {
        this.rewardRoyatlyDetList.searchType.current = val;
        this.rewardRoyatlyDetList.getRewardRoyatlyDet();
    }

    /**
 * 导出数据
 */
    exportData() {
        this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            // 获取搜索参数
            const searchParams = { ...this.searchType };

            exportMiniAccountRoyaltyDet(searchParams).then((res) => {
                var blob = new Blob([res.data], {
                    type: "application/x-msdownload;charset=UTF-8",
                });
                // 创建一个blob的对象链接
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                // 把获得的blob的对象链接赋值给新创建的这个 a 链接
                let now = new Date();
                let timestamp = now.getTime();
                link.setAttribute('download', '提成明细_' + timestamp + '.xls'); // 设置下载文件名
                document.body.appendChild(link);

                // 触发下载
                link.click();
                // 清理
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                this.$message.success('导出成功');
            }).catch((err) => {
                this.$message.error("导出失败: " + err);
            });
        }).catch(() => {
            // 用户取消导出
        });
    }

}
</script>

<style lang="scss" scoped>
@import "../../../assets/styles/goods/index.scss";

.topLine {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &__left {
        display: flex;
    }

    &__right {
        width: 450px;
        display: flex;
        justify-content: space-around;
    }
}

.listBottom {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: fixed;
    bottom: 10px;
    width: 990px !important;
    background-color: white;
    padding: 10px 0px;
    z-index: 10;
}
</style>