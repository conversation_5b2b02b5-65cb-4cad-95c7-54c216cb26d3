<template>
    <div style="margin-top: 20px">
        <el-table :data="goodList" style="width: 100%">
            <el-table-column label="序号" type="index" width="60">
			</el-table-column>
            <el-table-column prop="orderId" label="订单编号" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="payTime" label="订单时间" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="nikeName" label="会员昵称"  show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="phone" label="会员电话号码"  show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="amount" label="提成" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="payAmount" label="订单金额"  show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="remark" label="备注"  show-overflow-tooltip>
            </el-table-column>
        </el-table>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { searchMiniAccountRoyaltyDet } from '@/api/reward/reward';
import { SearchKeyType } from "./searchType";

@Component({
    components: {

    }
})
export default class RewardRoyatlyDetList extends Vue {
    name = "RewardRoyatlyDetList"
    goodList = [];
    created() {
        if(this.$route.query.nikeName){
            this.searchType.nikeName = this.$route.query.nikeName
        }
        this.getRewardRoyatlyDet();
    }
    searchType = {
        current: 1,
        size: 10
    } as SearchKeyType;

    total = 0;
    /**
   * 获取商品列表
   */
    async getRewardRoyatlyDet() {
        const param = this.searchType;
        try {
            const res = await searchMiniAccountRoyaltyDet(param);
            const goodList = res.data.list;
            this.total = res.data.total;
            this.goodList = goodList;
        } catch (error) {
            console.log(error);
        }
        this.$emit("getShowProList", this.goodList);
    }
}
</script>

<style lang="scss" scoped>
.goodList {
    width: 250px;
    display: flex;
    justify-content: center;
    text-align: center;
    padding-right: 20px;
    overflow: hidden;
}

.center {
    display: flex;
    justify-content: center;
}

.digTitle {
    font-size: 17px;
    font-weight: bold;
}
</style>