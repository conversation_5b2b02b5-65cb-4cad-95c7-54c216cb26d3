<!--
 * @description: 平台用户管理
-->
<template>
  <div class="customer">
    <el-tabs>
      <div class="memberList">
        <div class="line"></div>
        <m-card class="form" :needToggle="true">
          <el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="100px">
            <el-row :gutter="40">
              <el-col :span="10">
                <el-form-item label="职员编号"><el-input v-model="dataForm.empNumber" clearable
                                                         placeholder="请输入职员编号" /></el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="部门"><el-input v-model="dataForm.departmentName" clearable
                                                     placeholder="请输入部门" /></el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="40">
              <el-col :span="10">
                <el-form-item label="姓名"><el-input v-model="dataForm.empFullName" clearable
                                                     placeholder="请输入姓名" /></el-form-item>
              </el-col>
            </el-row>
            <el-button type="primary" style="margin-left:100px" @click="searchQuery(1)">搜索</el-button>
          </el-form>
        </m-card>
        <div style="margin-bottom:20px; display: flex; justify-content: flex-start; align-items: center;">
          <el-button type="primary" icon="el-icon-circle-plus-outline" @click="getEmpNumber" v-if="addEmployeeFlag == 1">
            新增
          </el-button>
          <el-dropdown style="margin-left: 10px;" placement="bottom-start" @command="handleBatchCommand">
            <el-button type="primary" plain>
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="clockIn" :disabled="selectedEmployees.length === 0">
                批量上班
              </el-dropdown-item>
              <el-dropdown-item command="clockOut" :disabled="selectedEmployees.length === 0">
                批量下班
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <!-- 部门列表 -->
        <template>
          <el-table :data="employeeList" style="width: 100%" border max-height="100%" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55">
            </el-table-column>
            <el-table-column prop="employeeImageUrl" label="照片" width="80">
              <template slot-scope="scope">
                <img v-if="scope.row.employeeImageUrl" :src="scope.row.employeeImageUrl" class="emp-avatar" />
                <span v-else>无照片</span>
              </template>
            </el-table-column>
            <el-table-column prop="empNumber" label="职员编号">
            </el-table-column>
            <el-table-column prop="departmentName" label="部门">
            </el-table-column>
            <el-table-column prop="empFullName" label="姓名">
            </el-table-column>
            <el-table-column prop="highNumber" label="初始好评数">
            </el-table-column>
            <el-table-column prop="serviceNumber" label="初始服务人数">
            </el-table-column>
            <el-table-column prop="workStatus" label="工作状态">
              <template slot-scope="scope">
                <el-tag :type="scope.row.workStatus === 1 ? 'success' : 'info'">
                  {{ scope.row.workStatus === 1 ? '上班' : '休息' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="" label="关联职位" width="150">
              <template slot-scope="scope">
                <div>
                  <a v-if="scope.row.positionList && scope.row.positionList.length > 0" style="color: #409eff; cursor: pointer;"
                     @click="bindPositionBtn(scope.row)">
                    {{ scope.row.positionList.map(p => p.positionName).join(', ') }}
                  </a>
                  <a v-else style="color: #409eff; cursor: pointer;"
                     @click="bindPositionBtn(scope.row)">关联职位</a>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-tooltip effect="dark" content="编辑" placement="top" v-if="addEmployeeFlag == 1">
                  <el-button type="text" size="medium" icon="el-icon-edit" @click="editBtn(scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <!-- 删除-->
                  <el-button type="text" size="medium" icon="el-icon-delete-solid" v-if="isSupper || deleteButton"
                             @click="delWareBtn(scope.row)"></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 仓库弹窗 -->

        <!-- 新增模态框 -->
        <el-dialog title="新增职员" :visible.sync="addStaff" width="30%">
          <el-form :model="addFormStaff" ref="addForm" label-width="100px">
            <el-form-item label="头像" prop="employeeImageUrl">
              <el-upload
                class="avatar-uploader"
                action=""
                :show-file-list="false"
                :auto-upload="true"
                :before-upload="beforeAvatarUpload"
                :http-request="uploadAvatar"
              >
                <img v-if="addFormStaff.employeeImageUrl" :src="addFormStaff.employeeImageUrl" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div class="el-upload__tip">仅支持JPG/PNG格式，大小不超过1MB</div>
            </el-form-item>
            <el-form-item label="职员编号" prop="empNumber" :rules="[
							{ required: true, message: '职员编号不能为空' }
						]">
              <el-input v-model="addFormStaff.empNumber" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="职员名称" prop="empFullName" :rules="[
							{ required: true, message: '职员名称不能为空' }
						]">
              <el-input v-model="addFormStaff.empFullName"></el-input>
            </el-form-item>
            <el-form-item label="初始好评数" prop="highNumber" :rules="[
							{ required: true, message: '初始好评数不能为空' },
							{ type: 'number', min: 0, message: '初始好评数必须大于等于0' }
						]">
              <el-input-number v-model="addFormStaff.highNumber" :min="0" controls-position="right"></el-input-number>
            </el-form-item>
            <el-form-item label="初始服务数" prop="serviceNumber" :rules="[
							{ required: true, message: '初始服务数不能为空' },
							{ type: 'number', min: 0, message: '初始服务数必须大于等于0' }
						]">
              <el-input-number v-model="addFormStaff.serviceNumber" :min="0" controls-position="right"></el-input-number>
            </el-form-item>
            <el-form-item label="工作状态" prop="workStatus" :rules="[
							{ required: true, message: '请选择工作状态' }
						]">
              <el-select v-model="addFormStaff.workStatus" placeholder="请选择工作状态">
                <el-option :value="1" label="上班"></el-option>
                <el-option :value="2" label="休息"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
						<el-button @click="addStaff = false;">取 消</el-button>
						<el-button type="primary" @click="addHandler()">确 定</el-button>
					</span>
        </el-dialog>

        <!-- 编辑模态框 -->
        <el-dialog title="编辑职员" :visible.sync="editStaff" width="30%">
          <el-form :model="editFormStaff" ref="addForm" label-width="100px">
            <!-- <el-form-item label="职员id" prop="id" :rules="[
							{ required: true, message: '职员id不能为空' }
						]">
              <el-input v-model="editFormStaff.id" disabled autocomplete="off"></el-input>
            </el-form-item> -->
            <el-form-item label="头像" prop="employeeImageUrl">
              <el-upload
                class="avatar-uploader"
                action=""
                :show-file-list="false"
                :auto-upload="true"
                :before-upload="beforeAvatarUpload"
                :http-request="uploadEditAvatar"
              >
                <img v-if="editFormStaff.employeeImageUrl" :src="editFormStaff.employeeImageUrl" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div class="el-upload__tip">建议上传100x100像素的图片，支持JPG/PNG格式，大小不超过2MB</div>
            </el-form-item>
            <el-form-item label="职员编号" prop="empNumber" :rules="[
							{ required: true, message: '职员编号不能为空' }
						]">
              <el-input v-model="editFormStaff.empNumber" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="职员名称" prop="empFullName" :rules="[
							{ required: true, message: '职员名称不能为空' }
						]">
              <el-input v-model="editFormStaff.empFullName"></el-input>
            </el-form-item>
            <el-form-item label="初始好评数" prop="highNumber" :rules="[
							{ required: true, message: '初始好评数不能为空' },
							{ type: 'number', min: 0, message: '初始好评数必须大于等于0' }
						]">
              <el-input-number v-model="editFormStaff.highNumber" :min="0" controls-position="right"></el-input-number>
            </el-form-item>
            <el-form-item label="初始服务数" prop="serviceNumber" :rules="[
							{ required: true, message: '初始服务数不能为空' },
							{ type: 'number', min: 0, message: '初始服务数必须大于等于0' }
						]">
              <el-input-number v-model="editFormStaff.serviceNumber" :min="0" controls-position="right"></el-input-number>
            </el-form-item>
            <el-form-item label="工作状态" prop="workStatus" :rules="[
							{ required: true, message: '请选择工作状态' }
						]">
              <el-select v-model="editFormStaff.workStatus" placeholder="请选择工作状态">
                <el-option :value="1" label="上班"></el-option>
                <el-option :value="2" label="休息"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
						<el-button @click="editStaff = false;">取 消</el-button>
						<el-button type="primary" @click="editHandler()">确 定</el-button>
					</span>
        </el-dialog>

        <!-- 分页 -->
        <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
                    @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
      </div>
    </el-tabs>
    <bindPosition ref="bindPosition" @searchQuery="searchQuery(1)" />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';
import PageManage from '@/components/PageManage.vue';
import { ElForm } from 'element-ui/types/form';
import { pageEmployeeList, deleteEmployee, getMainSpecialSetting, getEmpNumber, addEmployee, updateEmployee, batchEditWorkStatus } from "@/api/employee/employee";
import { upLoad } from "@/api/index";
import bindPosition from '../employee/components/bindPosition.vue'
@Component({
  components: {
    PageManage,
    bindPosition
  }
})
export default class Index extends Vue {
  @Ref()
  readonly dataFormRef!: ElForm;

  // 数据表单  组成数据用于向后端提交数据
  dataForm = {
    empNumber: '',
    departmentName: '',
    empFullName: '',
    warehouseFullName: ''
  };

  // 仓库列表
  wareList = [];

  /** 分页条数 */
  pageSize = 10;

  /** 分页页码 */
  pageNum = 1;

  /** 数据长度 */
  total = 0;

  // 存放全部仓库数据
  employeeList = [];

  //需要绑定的人员id
  employeeId = '';

  // 等于1就显示添加、编辑按钮
  addEmployeeFlag = '';

  // 新增职员
  addStaff = false;

  //新增弹出框
  addFormStaff = {
    empNumber: '',
    empFullName: '',
    employeeImageUrl: '',
    highNumber: 0,
    serviceNumber: 0,
    workStatus: 1,
  };

  // 编辑职员
  editStaff = false;

  //编辑弹出框
  editFormStaff = {
    id: '',
    empNumber: '',
    empFullName: '',
    employeeImageUrl: '',
    highNumber: 0,
    serviceNumber: 0,
    workStatus: 1,
  };

  // 选中的职员列表
  selectedEmployees: any[] = [];

  menuName = "职员管理";

  buttonList = []

  isSupper = 0;

  deleteButtonCode = "employee.delete";

  deleteButton = false;

  mounted() {
    this.searchQuery(1);
    this.buttonAuth();
    this.getMainSpecialSetting();
  }

  buttonAuth() {
    this.isSupper = this.$STORE.userStore.userInfo.isSupper
    let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)
    let buttonList = [];
    authMenuButtonVos.forEach(element => {
      buttonList.push(element.buttonCode);
    });
    this.buttonList = buttonList

    var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);

    if (deleteButtonData != null && deleteButtonData != undefined) {
      this.deleteButton = true;
    }

  }

  // 获取特殊配置接口
  getMainSpecialSetting() {
    getMainSpecialSetting().then(res => {
      this.addEmployeeFlag = res.data.addEmployeeFlag;
      // console.log("res=",res.data.addEmployeeFlag);
    })
  }

  // 获取职员编号接口
  getEmpNumber() {
    getEmpNumber().then(res => {
      this.addStaff = true;
      // console.log("res=",res.data);
      this.addFormStaff.empNumber = res.data;
    })
  }

  //新增的提交方法
  addHandler() {
    addEmployee(this.addFormStaff).then(res => {
      this.$message.success("新增成功");
      // 关闭模态框
      this.addStaff = false;
      //重置弹出框的数据为空
      this.addFormStaff = {
        empNumber: '',
        empFullName: '',
        employeeImageUrl: '',
        highNumber: 0,
        serviceNumber: 0,
        workStatus: 1,
      };
      // 提交数据成功，重新获取一次数据进行渲染
      this.searchQuery(1);
    }).catch(err => {
      this.$message.error(err || "网络错误");
    });
  }

  //编辑的提交方法
  editHandler(){
    updateEmployee(this.editFormStaff).then(res => {
      this.$message.success("编辑成功");
      // 关闭模态框
      this.editStaff = false;
      // 提交数据成功，重新获取一次数据进行渲染
      this.searchQuery(1);
    }).catch(err => {
      this.$message.error(err || "网络错误");
    });
  }

  // 职员编辑
  editBtn(row: any) {
    // console.log("row=", row);
    this.editFormStaff = {
      id: row.id,
      empNumber: row.empNumber,
      empFullName: row.empFullName,
      employeeImageUrl: row.employeeImageUrl,
      highNumber: row.highNumber,
      serviceNumber: row.serviceNumber,
      workStatus: row.workStatus,
    }
    this.editStaff = true;
  }



  //获取用户列表（数据）
  searchQuery(pageNum: number) {
    // console.log('执行了');
    const form = this.dataForm;
    const param = {
      current: pageNum,
      size: this.pageSize,
      ...form
    }

    pageEmployeeList(param).then(res => {
      // 仓库数据
      this.employeeList = res.data.list
      // 显示数据条数
      this.pageSize = res.data.size;
      // 第几页
      this.pageNum = res.data.current;

      this.total = res.data.total;
    })
  }

  //删除按钮功能模块
  delWareBtn(scope: any) {
    this.$confirm('此操作将永久删除该职员, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const delParam = {
        id: scope.id,
      }
      deleteEmployee(delParam).then(res => {
        this.$message({
          type: 'success',
          message: res.data
        });
        this.searchQuery(1);
      })
    }).catch(() => {
      this.$message({
        type: 'info',
        message: '已取消删除'
      });
      this.searchQuery(1);
    });
  }



  /**
   * @method handleSizeChange
   * @description 每页 条
   */
  handleSizeChange(val: number) {
    this.pageSize = val;
    this.searchQuery(1);
  }

  /**
   * @method handleCurrentChange
   * @description 当前页
   */
  handleCurrentChange(val: number) {
    this.pageNum = val;
    this.searchQuery(val);
  }

  bindPositionBtn(row) {
    const positionIds = row.positionList && row.positionList.length > 0 ? row.positionList.map(p => p.id) : [];
    this.$refs.bindPosition.openDialog(row.id, positionIds);
  }

  /**
   * 上传图片前验证
   */
  beforeAvatarUpload(file: { type: string; size: number }) {
    const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
    const isLt1M = file.size / 1024 / 1024 < 1;

    if (!isJPG) {
      this.$message.error('上传头像图片只能是 JPG/PNG 格式!');
    }
    if (!isLt1M) {
      this.$message.error('上传头像图片大小不能超过 2MB!');
    }
    return isJPG && isLt1M;
  }

  /**
   * 新增弹窗上传图片
   */
  async uploadAvatar(file: { file: any }) {
    try {
      const res = await upLoad({
        file: file.file,
      });
      this.addFormStaff.employeeImageUrl = res.data;
      this.$message.success('上传成功');
    } catch (error) {
      this.$message.error('上传失败');
    }
  }

  /**
   * 编辑弹窗上传图片
   */
  async uploadEditAvatar(file: { file: any }) {
    try {
      const res = await upLoad({
        file: file.file,
      });
      this.editFormStaff.employeeImageUrl = res.data;
      this.$message.success('上传成功');
    } catch (error) {
      this.$message.error('上传失败');
    }
  }

  /**
   * 表格选中状态变化
   */
  handleSelectionChange(selection: any[]) {

    this.selectedEmployees = []
    selection.forEach((item: any) => {
      this.selectedEmployees.push({'id':item.id, 'empFullName':item.empFullName});
    });
  }

  /**
   * 处理批量操作命令
   */
  handleBatchCommand(command: string) {
    if (command === 'clockIn') {
      this.batchWork(1); // 上班
    } else if (command === 'clockOut') {
      this.batchWork(2); // 休息
    }
  }


  /**
   * 批量上班
   */
  async batchWork(status:number) {

    const msg = status === 1 ? '上班' : '下班';

    if (this.selectedEmployees.length === 0) {
      this.$message.warning('请先选择要操作的职员');
      return;
    }
    
    const selectedNames = this.selectedEmployees.map(emp => emp.empFullName).join('、');
    try {
      await this.$confirm(
        `确定要将选中的职员（${selectedNames}）设置为${msg}状态吗？`,
        `批量${msg}确认`,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      const ids = this.selectedEmployees.map(emp => emp.id);
      const data = {
        employeeIds: ids,
        workStatus: status
      }
      await batchEditWorkStatus(data);

      this.$message.success(`修改成功`);
      this.searchQuery(this.pageNum); // 刷新列表
      this.selectedEmployees = []; // 清空选中状态
    } catch (error) {
      if (error !== 'cancel') {
        this.$message.error(`批量${msg}操作失败：` + error);
      }
    }
  }

}

</script>

<style lang="scss" scoped>
@import '@/assets/styles/cutomer/customer';

.emp-avatar {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  object-fit: cover;
}

.avatar-uploader {
  text-align: center;

  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #409EFF;
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 7px;
  line-height: 1.4;
}
</style>
