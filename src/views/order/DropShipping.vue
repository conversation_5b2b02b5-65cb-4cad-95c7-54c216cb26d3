<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 10:45:18
  2025-6-26
  代发货订单
-->
<template>
  <div>
    <m-container class="order" :pagination-visible="false" :current.sync="query.current" :size.sync="query.size"
      :total.sync="query.total">
      <DeliveryOrderFrom slot="form"  @input-set="myInput"/>

      <template slot="content">
        <el-tabs v-model="activeName" @tab-click="handleTabClick">
          <el-tab-pane label="所有订单" name="-1"></el-tab-pane>
          <el-tab-pane label="待发货" name="0"> </el-tab-pane>
          <el-tab-pane label="已发货" name="1"> </el-tab-pane>
        </el-tabs>

        <DropShippingTable @table-function="tableFunction" :data="orderShipping" :query.sync="queryShipping"
          :checked-item.sync="checkedItem" :controlOption="getControlOption(query.orderStatus)" />
      </template>
    </m-container>

    <OrderDetailModal v-model="detailVisible" :detail="orderDetail" :need-tabs="true" :is-delivery="true"
      :type="detailType" @reset="handleSeeDetail" @closeDetail="closeDetail" />

    <RemarkModal v-model="remarklVisible" title="备注" :current-order="currentOrder" :orderIds="orderIds"
      @onOk="handleRemarks" @onCancel="toggleRemarklVisible" />

    <DropShippingDelivery  v-model="showDropShipping"  :tableData="tableData"
     @reset="getLogisticsCompany" @closeDetail="closeDetail2"  />

    <DeliveryPrintModal v-model="printModalVisible" :logisticsPrinterVos="logisticsPrinterVos"
      :logistics-company="logisticsCompany" @reset="getLogisticsCompany" @onConfirm="confirm" />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch } from "vue-property-decorator";
import DeliveryOrderFrom from "./components/DeliveryOrderFromDropShipping.vue";
import DropShippingTable from "./components/DropShippingTable.vue";
import OrderDetailModal from "./components/detail/Index.vue";
import DropShippingDelivery from "./components/DropShippingDelivery.vue";
import RemarkModal from "./components/RemarkModal.vue";
import DeliveryPrintModal from "./components/DeliveryPrintModal.vue";
import {
  DeliveryState,
  DeliveryOrderQueryType,
  DeliveryToolOptions,
  DeliveryOrderList,
  ApiOrderDetail,
  LogisticsCompanyType,
  LogisticsPrinterVos,
  LogisticsAddressVos,
  WarehouseVos,
} from "./orderType";

import { deliveryOrderQuery, filterEmpty } from "./common/order";
import { getLogisticsCompany } from "@/api/logistics/logistics";
import { surveyWarehouse, getWarehouseByShopId } from "@/api/warehouse/warehouse";

import {
  getOrders,
  getOrderDetail,
  remarks,
  close,
  printDeliver,
  batchPrintDeliver,
  getLogisticsWait,
  payment,
  updateSendStatus,
  approved,
  deliverMessage,
  vailDeliveryByOrderId,
  vailDelivery,
  getSearch,
  deliverProxyMessage,
} from "@/api/order";
import { bulkShipment } from "@/api/logistics/logistics";
import cloneDeep from "lodash/cloneDeep";
import { isClose } from "./common/order";
import { Dictionary } from "vue-router/types/router";

Component.registerHooks(["beforeRouteEnter", "beforeRouteUpdate"]);

@Component({
  components: {
    DeliveryOrderFrom,
    OrderDetailModal,
    RemarkModal,
    DropShippingTable,
    DropShippingDelivery,
    DeliveryPrintModal,
  },
})
export default class DeliveryOrder extends Vue implements DeliveryState {
  query = { ...deliveryOrderQuery } as DeliveryOrderQueryType;

  defaultContorlOption: Array<DeliveryToolOptions> = [
    {
      label: "批量备注",
      value: "remark",
    },
    {
      label: "重置未发送",
      value: "notIssued",
    },
    {
      label: "重置已发送",
      value: "issued",
    },
  ];

  waitContorlOption: Array<DeliveryToolOptions> = [
    {
      label: "批量备注",
      value: "remark",
    },
    {
      label: "重置未发送",
      value: "notIssued",
    },
    {
      label: "重置已发送",
      value: "issued",
    },
  ];

  waitPayContorlOption: Array<DeliveryToolOptions> = [
    {
      label: "批量备注",
      value: "remark",
    },
    {
      label: "批量关闭",
      value: "close",
    },
    {
      label: "批量线下支付",
      value: "payment",
    },
    {
      label: "批量审核通过",
      value: "approved",
    },
  ];

  quicklyOption: Array<DeliveryToolOptions> = [
    { label: "近一个月订单", value: "0" },
    { label: "近三个月订单", value: "1" },
    { label: "全部订单", value: "2" },
  ];

  data: Array<DeliveryOrderList> = [];

  orderIds: Array<string | DeliveryOrderList | any> = [];

  detailVisible = false;

  remarklVisible = false;

  sendModalVisible = false;

  printModalVisible = false;

  orderDetail = {} as ApiOrderDetail;

  checkedItem: any = [];

  currentOrder = {} as DeliveryOrderList;

  needTabs = false;

  detailType = "1";

  logisticsCompany: Array<LogisticsCompanyType> = [];

  logisticsPrinterVos: Array<LogisticsPrinterVos> = [];

  logisticsAddressVos: Array<LogisticsAddressVos> = [];

  warehouseVos: Array<WarehouseVos> = [];

  deliveryType: Array<{ label: string; value: number }> = [
    { label: "物流配送", value: 102 },
    { label: "送货上门", value: 101 },
    { label: "自提", value: 100 },
    { label: "手动发货", value: 103 },
  ];

  //禁用发货的按钮
  disableSendBtn = false;

  get orders() {
    return this.data.map((item) => {
      item.close = isClose(item.status);
      return item;
    });
  }

  /** 表格选中条目ID组 */
  get selectedIds() {
    return this.checkedItem.map((item) => item.id);
  }

  get quicklyName() {
    const quicklyOption = this.quicklyOption.find(
      (item) => item.value === this.query.quicklyDate,
    );
    return quicklyOption ? quicklyOption.label : "";
  }

  /** 监听query变化 */
  @Watch("query", { deep: true })
  handleQueryChange(v: DeliveryOrderQueryType) {
    this.getOrders(filterEmpty({ ...this.$route.query, ...v }));
  }

  /** 监听query变化 */
  @Watch("queryShipping", { deep: true })
  handleQueryChange2(v: DeliveryOrderQueryType) {
    this.getSearch();
  }

  dropShippingList: any = [];

  queryShipping: any = {
    "current": 1,
    "size": 10,
    "userName": "",
    "productName": "",
    "deliveryStatus": '',
    "total": 0,

  };
  activeName: string = '-1';

  get orderShipping() {
    return this.dropShippingList;
  }
  tableData: any = {};

  showDropShipping: boolean = false;

  myInput(from: any){
    this.queryShipping = from;
  }
  // 获取订单 5.30
  getSearch() {
    const param = this.queryShipping;
    getSearch(param).then(res => {
      console.log("代发货订单列表=", res);
      this.queryShipping.total = res.data.total;
      this.dropShippingList = res.data.list;
    }).catch(err => {
      this.$message.error(err)
    })
  }

  /** 获取订单 */
  beforeRouteEnter(
    to: { query: DeliveryOrderQueryType },
    _form: any,
    next: (
      arg0: (vm: {
        query: DeliveryOrderQueryType;
        data: DeliveryOrderList[];
      }) => void,
    ) => void,
  ) {
    const query = filterEmpty(
      Object.assign(cloneDeep(deliveryOrderQuery), to.query),
    );
    getOrders(query)
      .then((res) => {
        console.log(res);
        const { list = [], ...other } = res.data;
        next((vm) => {
          Object.assign(vm.query, query, other);
          vm.data = list;
        });
      })
      .catch(() => {
        alert("列表获取失败,请稍后再试");
      });
  }

  created() {
    this.getLogisticsCompany();
    //5.30
    this.getSearch();


    if (this.$route.query.orderId != null && this.$route.query.orderId != undefined && this.$route.query.orderId != "") {
      var params = {};
      params.orderId = this.$route.query.orderId

      // 从消息列表过了，到代发货消息
      if (this.$route.query.orderStatus) {
        params.orderStatus = this.$route.query.orderStatus
        this.query.orderStatus = params.orderStatus
      }

      this.getOrders(Object.assign({}, this.query, params));
      this.query.orderId = this.$route.query.orderId


    } else {
      var params = {};
      params.orderStatus = '-1'
      this.getOrders(Object.assign({}, this.query, params));
      this.query.orderStatus = '-1'
    }
  }

  /** 获取物流公司及打印机 */
  getLogisticsCompany() {
    getLogisticsCompany()
      .then((res) => {
        this.logisticsCompany = res.data.logisticsCompanyVos;
        this.logisticsPrinterVos = res.data.LogisticsPrinterVos;
        this.logisticsAddressVos = res.data.logisticsAddressVos;
        console.log("logisticsAddressVos", this.logisticsAddressVos);
      })
      .catch((err) => {
        this.$message.warning(err);
      });
  }

  //获取仓库
  // surveyWarehouse() {
  //   const param = {
  //     current: "1",
  //     size: 100,
  //   };
  //   surveyWarehouse(param)
  //     .then((res) => {
  //       this.warehouseVos = res.data.list;
  //     })
  //     .catch((err) => {
  //       this.$message.warning(err);
  //     });
  // }

  //根据店铺id获取仓库
  getWarehouseData(shopId: string) {
    let param = {};
    param.shopId = shopId;
    getWarehouseByShopId(param)
      .then((res) => {
        this.warehouseVos = res.data;
      })
      .catch((err) => {
        this.$message.warning(err);
      });
  }

  /** 确认批量发货 */
  confirm(data: { orderIds: string | any[]; deliveryType: number, orderId: string }) {

    console.log("确认批量发货=", data);
    // if (!data.orderIds.length) {
    //   data.orderIds = this.checkedItem.map((item) => item.orderId);
    // }
    this.disableSendBtn = true;

    // 物流配送 / 送货上门 / 自提
    if (103 !== data.deliveryType) {
      console.log('物流配送 / 送货上门 / 自提');
      batchPrintDeliver([data])
        .then(() => {
          this.disableSendBtn = false;
          this.$message.success("发货成功");
          this.getOrders(Object.assign({}, this.query, this.$route.query));
          this.hideModal();
        })
        .catch((err) => {
          this.disableSendBtn = false;
          this.$message.warning(err || "发货失败");
        });
    }
    // 手动发货
    else {
      // if (!data.orderIds.length && !this.checkedItem.length) {
      //   data.orderIds = [data.orderId]
      // }
      console.log('手动发货');

      bulkShipment([data])
        .then(() => {
          this.disableSendBtn = false;
          this.$message.success("发货成功");
          this.getOrders(Object.assign({}, this.query, this.$route.query));
          this.hideModal();
        })
        .catch((err) => {
          this.disableSendBtn = false;
          this.$message.warning(err || "发货失败");
        });
    }
  }

  /** 查看物流 */
  viewLogistics() {
    this.detailType = "2";
    this.detailVisible = true;
  }

  /** 获取按钮选项 */
  getControlOption(type: string | number) {
    return (
      {
        ["-1"]: this.waitContorlOption,
        0: this.waitPayContorlOption,
        1: this.waitContorlOption,
      }[type] || this.defaultContorlOption
    );
  }

  /** 获取订单列表 */
  getOrders(
    q: DeliveryOrderQueryType & Dictionary<string | (string | null)[]>,
    cb?: (() => void) | undefined,
  ) {
    getOrders(q)
      .then((res) => {
        const { list, ...other } = res.data;
        Object.assign(this.query, other);
        this.data = list;
        console.log("======this.data===============", this.data);
        if (cb) {
          cb();
        }
      })
      .catch((err) => {
        this.$message.warning(err || "订单列表获取失败");
      });
  }

  /**
   * 监听table传来的事件
   * @param {name} 事件名 remark | close | detail
   */
  tableFunction(name: string, data: any, isLogistics: boolean) {
    console.log("name", name);
    switch (name) {
      case "remark":
        return this.triggerRemark(data);
      case "close":
        return this.handleClose(data);
      case "payment":
        return this.offlinePayment(data);
      case "notIssued":
        return this.modifySendStatus(data, 0);
      case "issued":
        return this.modifySendStatus(data, 1);
      case "approved":
        return this.approved(data);
      case "detail":
        return this.handleSeeDetail(data);
      case "print":
        return this.togglePrintisible();
      case "send":
        return this.send(data);
      case "batchSend":
        return this.batchSend(data);
      case "isSend":
        return this.isSend(data);
      case "deliverMessage":
        return this.handDeliverMessage(data);
    }
  }

  /**
   * 发货提醒
   * @param data 
   */
  async handDeliverMessage(data: DeliveryOrderList) {
    console.log(123);
    this.$confirm('此操作将批量发送代发货消息, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const params = this.checkedItem.map((item) => item.id)
      const orderIds = this.checkedItem.map((item) => item.id).join(",");
      const waitLen = this.dropShippingList.filter(
        (item) =>
          orderIds.includes(item.id) && item.deliveryStatus == 'NO'
      ).length;

      if (!waitLen) {
        return this.$message.info("您没有选择待发货订单");
      }

      deliverProxyMessage(params)
        .then(() => {
          this.$message.success("代发货提醒成功");
        })
        .catch((err) => {
          this.$message.warning(err || "代发货提醒失败");
        });

    }).catch(() => {
      this.$message({
        type: 'info',
        message: '已取消代发货提醒'
      });
    });

  }
  // 批量发货
  async batchSend(data: any) {
    console.log("data=", data);


    let orderIds: any = null;
    if (data) {
      orderIds = data
    } else {
      orderIds = this.checkedItem.map((item) => item.id).join(",");
    }
    console.log("!data=", !data);
    console.log("orderIds=", orderIds);
    const waitLen = this.dropShippingList.filter(
      (item) =>
        orderIds.includes(item.id)
    ).length;

    if (!waitLen) {
      return this.$message.info("您没有选择待发货订单");
    }

    this.$router.push(`/order/dropShipping/sendSendDropShip?orderIds=${orderIds}`);

    // deliverProxyMessage(orderIds.split(","))
    //   .then((res) => {
    //     this.$router.push(`/order/dropShipping/sendSendDropShip?orderIds=${orderIds}`);
    //   }).catch((err) => {
    //     this.$message.warning(err);
    //   });

  }

  /** 发货 */
  async send(data: DeliveryOrderList) {
    console.log("data", data);
    //重新获取仓库
    this.getWarehouseData(data.shopId);
    try {
      let orderId = data.orderId
      let params = {};
      params.orderId = orderId;
      vailDeliveryByOrderId(params)
        .then((res) => {
          if (res.data == 1) {
            this.toggleSendVisible(data)
          } else if (res.data == 2) {
            this.$message.warning("其他商户订单不允许发货");
          } else if (res.data == 3) {
            this.$message.warning("订单已交由平台发货，不允许发货");
          } else {
            this.$message.warning("当前商户账户不允许发货");
          }
        })
        .catch((err) => {
          this.$message.warning("验证失败");
        });
    } catch (err) {
      this.$message.warning("未发货订单状态获取失败");
    }
  }
  /** 打印面单 */
  async isSend(data: DeliveryOrderList) {
    data.isSend = true
    this.currentOrder = data;
    this.sendModalVisible = !this.sendModalVisible;
  }



  /**
   * 查看详情
   */
  async handleSeeDetail(orderData: any) {
    console.log("orderData", orderData);
    this.showDropShipping = true;
    this.tableData = orderData;
  }

  /**
   * 关闭
   * @param {orderData} 订单数据 如果没有参数为批量操作
   */
  handleClose(orderData?: any) {
    this.$confirm("确定关闭订单？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        const orderIds = orderData
          ? [orderData.orderId]
          : [...this.selectedIds];
        close(orderIds)
          .then(() => {
            this.getOrders(Object.assign({}, this.query, this.$route.query));
            this.$message.success("关闭成功");
          })
          .catch((err) => {
            this.$message.warning(err || "关闭失败");
          });
      })
      .catch(() => {
        //
      });
  }

  /**
   * 线下支付
   * @param {orderData} 订单数据 如果没有参数为批量操作
   */
  offlinePayment(orderData?: any) {
    this.$confirm("确定是线下支付订单？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        const orderIds = orderData
          ? [orderData.orderId]
          : [...this.selectedIds];
        payment(orderIds)
          .then(() => {
            this.getOrders(Object.assign({}, this.query, this.$route.query));
            this.$message.success("线下支付成功");
          })
          .catch((err) => {
            this.$message.warning(err || "线下支付失败");
          });
      })
      .catch(() => {
        //
      });
  }
  /**
   * 订单审核通过
   * @param {orderData} 订单数据 如果没有参数为批量操作
   */
  approved(orderData?: DeliveryOrderList) {
    this.$confirm("确定审核通过订单吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        const orderIds = orderData
          ? [orderData.orderId]
          : [...this.selectedIds];

        const waitLen = this.orders.filter(
          (item) =>
            orderIds.includes(item.orderId) && item.status != "WAIT_FOR_PAY",
        ).length;

        if (waitLen && !orderData) {
          return this.$message.info("修改失败，订单状态必须为待付款");
        }
        approved(orderIds)
          .then(() => {
            this.getOrders(Object.assign({}, this.query, this.$route.query));
            this.$message.success("订单审核成功");
          })
          .catch((err) => {
            this.$message.warning(err || "订单审核失败");
          });
      })
      .catch(() => {
        //
      });
  }

  /**
   * 修改订单发送状态
   * @param {orderData} 订单数据 如果没有参数为批量操作
   */
  modifySendStatus(orderData?: any, status?: bigint | number) {
    let data = {
      sendStatus: status,
    };
    this.$confirm("确定修改订单发送状态？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        const orderIds = orderData
          ? [orderData.orderId]
          : [...this.selectedIds];
        updateSendStatus({ orderIds: orderIds, ...data })
          .then(() => {
            this.getOrders(Object.assign({}, this.query, this.$route.query));
            this.$message.success("修改订单发送状态成功");
          })
          .catch((err) => {
            this.$message.warning(err || "修改订单发送状态失败");
          });
      })
      .catch(() => {
        //
      });
  }

  /**
   * 备注
   * @param {orderData} 订单数据 如果没有参数为批量操作
   */
  handleRemarks(form: { note: string; over: boolean }) {
    remarks({ orderIds: this.orderIds, ...form })
      .then(() => {
        this.getOrders(Object.assign({}, this.query, this.$route.query));
        this.toggleRemarklVisible();
        this.$message.success("备注成功");
      })
      .catch((err) => {
        this.$message.warning(err || "备注失败");
      });
  }

  setQuicklyDate(v: string) {
    this.query.quicklyDate = v;
  }

  async closeDetail() {
    this.detailVisible = false
  }

  async closeDetail2() {
    this.showDropShipping = false
  }

  toggleDetailModal() {
    this.detailVisible = !this.detailVisible;
    if (!this.detailVisible) {
      this.detailType = "1";
    }
  }

  toggleRemarklVisible() {
    this.remarklVisible = !this.remarklVisible;
  }

  hideModal() {
    this.sendModalVisible = false;

    this.printModalVisible = false;
  }

  toggleSendVisible(data: DeliveryOrderList) {
    this.currentOrder = data;
    if (this.currentOrder.deliveryType == "SELF") {
      this.currentOrder.deliveryType2 = 100
    } else {
      this.currentOrder.deliveryType2 = 102
    }
    this.sendModalVisible = !this.sendModalVisible;
  }
  togglePrintisible() {
    this.printModalVisible = !this.printModalVisible;
  }

  handleTabClick({ name: orderStatus }: { name: string }) {
    // Object.assign(this.query, {
    //   ...deliveryOrderQuery,
    //   orderStatus,
    //   current: 1,
    //   deliverId: "",
    //   quicklyDate: this.query.quicklyDate,
    // });
    console.log("orderStatus=", orderStatus);
    if (orderStatus == '-1') {
      this.queryShipping.deliveryStatus = '';
    } else if (orderStatus == '0') {
      this.queryShipping.deliveryStatus = 0;
    } else {
      this.queryShipping.deliveryStatus = 1;
    }
  }

  triggerRemark(orderData: DeliveryOrderList) {
    this.currentOrder = orderData;
    this.orderIds = this.currentOrder
      ? [this.currentOrder.orderId]
      : [...this.selectedIds];

    this.toggleRemarklVisible();
  }
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/order/order.scss";
</style>
