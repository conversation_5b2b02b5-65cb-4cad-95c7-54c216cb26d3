<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 14:08:55
 2025-7-18
-->
<template>
	<div>
		<div class="order__control--top">
			<div class="control--l">
				<el-button-group class="fix" v-if="controlOption">
					<el-button plain @click="emitFun('batchSend')" type="primary"
						v-if="(isSupper || deliverGoodsBatchButton)">批量发货</el-button>
					<el-button plain @click="emitFun('deliverMessage')" type="primary"
						v-if="mainFlag && (isSupper || deliveryBatchSendMessageButton)">发货提醒</el-button>
					<el-button plain @click="emitFun('batchReceipt')" type="primary"
						v-if="mainFlag && isSupper">批量确认收货</el-button>
					<el-button plain @click="emitFun('excelData')" type="primary">导出</el-button>
					<!-- <el-button v-if="controlOption && controlOption.length" plain class="dropdown__fix more" type="primary">
            <el-dropdown size="mini" v-if="controlOption" trigger="click">
              <span class="dropdown__fix--more">...</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, i) of controlOption" :key="i"
                  @click.native="emitFun(item.value)">{{ item.label }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            </el-button> -->
				</el-button-group>
			</div>
			<div class="control--r">
				<slot></slot>
			</div>
		</div>

		<!-- <m-table :data="data" :selection="true" :custom="true" :columns="columns" :needHoverBorder="true"
			:checked-item.sync="tableCheckedItem" multipleKey="itemVoList">
			<template v-slot:header="{ row }">
				<div class="order--header" :class="isClose(row.status) && 'order__close'">
					<el-tag class="header__tag" v-if="getAfterName(row.type)">{{ getAfterName(row.type) }}单
					</el-tag>
					<el-tag class="header__tag" v-if="row.privileges && row.privileges.includes('5')">优先发货
					</el-tag>
					<span style="margin-right: 50px">
						订单号：{{ row.orderId }}
						<span v-if="row.type == 'TICKET'">（通惠证订单）</span>
						<span v-if="row.type == 'MALL'">（商品订单）</span>
						<span v-if="row.type == 'INTEGRAL'">（积分订单）</span>
					</span>
					<span style="margin-right: 50px">
						创建时间：{{ row.createTime }}
					</span>
					<span style="margin-right: 40px">
						对接状态：<span v-if="row.sendStatus === 1">已发送</span><span v-else>未发送</span>
					</span>
					<span style="margin-right: 40px">
						发货方式：{{ getDeliveryTypeName(row.deliveryType) }}
					</span>
					<span><el-popover placement="bottom-start" width="200" trigger="hover" :disabled="!row.note">
							<div>备注：{{ row.note }}</div>
							<i slot="reference" class="iconfont iconPC-beizhu" :class="{ red: row.note }"
								v-if="isSupper || editButton" @click="emitFun('remark', row)"></i>
						</el-popover></span>
				</div>
			</template>

<template v-slot:custom-body="{ row }">
				<template v-for="(goods, i) of row.itemVoList">
					<tr :key="i">
						<td>

							<div class="table__goods">
								<div class="table__goods--image">

									<img v-if="row.type == 'TICKET' && !goods.productPic" src="@/assets/images/certificateImg.png"
										:alt="goods.productName" />
									<img v-else :src="goods.productPic" :alt="goods.productName" />
								</div>
								<div class="table__goods--info">
									<div class="goods--name">
										{{ goods.productName | strFilter }}
									</div>
									<div class="goods--specs">
										<span class="l">{{ goods.specs }}</span>
									</div>
									<div class="goods--specs" v-if="goods.afs" style="color: orange;">
										<span class="l">{{ getAfterName(goods.afs.type) }}
											{{ getAfterStatusName(goods.afs.status) }}</span>
									</div>
									<div class="goods--price">
										<span> ￥{{ goods.productPrice.toFixed(2) }} </span>
										<span class="specs">
											<span class="r">×{{ goods.productQuantity }}</span>
										</span>
									</div>
								</div>
							</div>
						</td>

						<td v-if="i === 0" :rowspan="row.itemVoList.length">
							<div class="table__user">
								<div class="user--name">
									<span>{{ row.userName }}</span> <br />
									({{ row.receiverName }},{{ row.receiverPhone }})
								</div>
							</div><br />
							<div class="table__user">商家名称：{{ row.shopName }}</div>
						</td>
						<td v-if="i === 0" :rowspan="row.itemVoList.length" class="text--center">

							<span v-if="row.type == 'INTEGRAL'">￥{{ row.payAmount }} +
								{{ row.allIntegral ? row.allIntegral : 0 }}积分</span>
							<span v-else>
								原价：￥{{ row.totalAmount }}
								<br>
								优惠：￥{{ row.youhuiPrice }}；扣减：￥{{ row.deductionPrice }}
								<br>
								现价：￥{{ row.payAmount }}
							</span>
							<br>
							<br><span>{{ row.warehouse && row.warehouse.warehouseFullName || '' }}</span>

						</td>

						<td v-if="i === 0" :rowspan="row.itemVoList.length" class="text--center">
							<template v-if="row.deliveryType === 'SHIPPED'">
								付款成功
							</template>

<template else>
								{{ getOrderStatusName(row.status) }}
							</template>

<div v-if="row.status !== 'WAIT_FOR_PAY'" style="margin-top: 5px">

	<el-button v-if="row.status === 'WAIT_FOR_SEND' && (isSupper || deliverGoodsButton)"
		@click="emitFun('send', row, true)" type="primary" size="mini" style="margin-bottom: 5px">发货</el-button>
</div>
</td>

<td v-if="i === 0" :rowspan="row.itemVoList.length">
	<el-button-group class="fix">
		<el-button plain @click="emitFun('detail', row)" type="primary">查看详情</el-button>

		<el-button v-if="row.status === 'WAIT_FOR_PAY' ||
									(logisticsBtnVisible(row) && row.deliverySn)
								" plain class="dropdown__fix more" type="primary">
			<el-dropdown size="mini" trigger="hover">
				<span class="dropdown__fix--more">...</span>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item v-if="row.status === 'WAIT_FOR_PAY' && (isSupper || closeButton)"
						@click.native="emitFun('close', row)">关闭订单</el-dropdown-item>
					<el-dropdown-item v-if="row.status === 'WAIT_FOR_PAY' && (isSupper || paymentButton)"
						@click.native="emitFun('payment', row)">线下付款</el-dropdown-item>
					<el-dropdown-item v-if="row.status === 'WAIT_FOR_PAY' && (isSupper || toExamineButton)"
						@click.native="emitFun('approved', row)">审核通过</el-dropdown-item>
					<el-dropdown-item
						v-if="(row.status === 'WAIT_FOR_SEND' || row.status === 'APPROVED') && (isSupper || notIssuedButton)"
						@click.native="emitFun('notIssued', row)">重置未发送</el-dropdown-item>
					<el-dropdown-item
						v-if="(row.status === 'WAIT_FOR_SEND' || row.status === 'APPROVED') && (isSupper || issuedButton)"
						@click.native="emitFun('issued', row)">重置已发送</el-dropdown-item>
					<el-dropdown-item v-if="logisticsBtnVisible(row) && row.deliverySn"
						@click.native="emitFun('detail', row, true)">
						查看物流</el-dropdown-item>
					<el-dropdown-item
						v-if="(row.status === 'WAIT_FOR_PICKUP' && row.deliveryType === 'LOGISTICS') && (isSupper || issuedButton)"
						@click.native="emitFun('isSend', row, true)">打印面单</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
		</el-button>
		<el-button v-else-if="row.status !== 'WAIT_FOR_PAY'
								" plain class="dropdown__fix more" type="primary">
			<el-dropdown size="mini" trigger="hover">
				<span class="dropdown__fix--more">...</span>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item v-if="row.status === 'APPROVED' && (isSupper || paymentButton)"
						@click.native="emitFun('payment', row)">线下付款</el-dropdown-item>
					<el-dropdown-item
						v-if="(row.status === 'WAIT_FOR_SEND' || row.status === 'APPROVED') && (isSupper || notIssuedButton)"
						@click.native="emitFun('notIssued', row)">重置未发送</el-dropdown-item>
					<el-dropdown-item
						v-if="(row.status === 'WAIT_FOR_SEND' || row.status === 'APPROVED') && (isSupper || issuedButton)"
						@click.native="emitFun('issued', row)">重置已发送</el-dropdown-item>
					<el-dropdown-item
						v-if="(row.status === 'WAIT_FOR_PICKUP' && row.deliveryType === 'LOGISTICS') && (isSupper || issuedButton)"
						@click.native="emitFun('isSend', row, true)">打印面单</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
		</el-button>
	</el-button-group>
</td>
</tr>
</template>
</template>
</m-table> -->

		<template>
			<el-table ref="multipleTable" :data="data" border tooltip-effect="dark" style="width: 100%" height="670"
				@selection-change="handleSelectionChange">
				<el-table-column type="selection" width="55" fixed="left">
				</el-table-column>
				<el-table-column label="商品" min-width="300" fixed="left">
					<template slot-scope="scope">
						<div v-for="(goods, i) of scope.row.itemVoList" :key="i" class="table__box">
							<div class="table__goods">
								<div class="table__goods--image">

									<img v-if="scope.row.type == 'TICKET' && !goods.productPic"
										src="@/assets/images/certificateImg.png" :alt="goods.productName" />
									<img v-else :src="goods.productPic" :alt="goods.productName" />
								</div>
								<div class="table__goods--info">
									<div style="font-size: 16px;">
										{{ goods.productName }}
									</div>
									<!-- <div class="goods--name">
										{{ goods.productName | strFilter }}
									</div> -->
									<div v-if="goods.saleDescribe" style="font-size: 16px;margin: 6px 0;">
										卖点描述：{{ goods.saleDescribe }}
									</div>
									<div class="goods--specs">
										<span class="l">{{ goods.specs }}</span>
									</div>
									<div class="goods--specs" v-if="goods.afs" style="color: orange;">
										<span class="l">{{ getAfterName(goods.afs.type) }}
											{{ getAfterStatusName(goods.afs.status) }}</span>
									</div>
									<div class="goods--price">
										<span> ￥{{ goods.productPrice.toFixed(2) }} </span>
										<span class="specs">
											<span class="r">×{{ goods.displayQuantity }}</span>
										</span>
									</div>
								</div>
							</div>
							<!-- <el-divider v-if="scope.row.itemVoList.length != 1"></el-divider> -->
						</div>
					</template>
				</el-table-column>
				<el-table-column label="客户" min-width="180">
					<template slot-scope="scope">
						<div>
							<div class="table__user">
								<div class="user--name">
									<span>{{ scope.row.userName }}</span> <br />
									({{ scope.row.receiverName }},{{ scope.row.receiverPhone }})
								</div>
							</div>
							<div class="table__user">推荐人：{{ scope.row.recommendName }}</div>
							<div class="table__user">商家名称：{{ scope.row.shopName }}</div>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="订单信息" min-width="300">
					<template slot-scope="scope">
						<div>
							订单号：{{ scope.row.orderId }}
							<span v-if="scope.row.type == 'TICKET'">（通惠证订单）</span>
							<span v-if="scope.row.type == 'MALL'">（商品订单）</span>
							<span v-if="scope.row.type == 'INTEGRAL'">（积分订单）</span>
						</div>
						<div>
							创建时间：{{ scope.row.createTime }}
						</div>
						<div>
							对接状态：<span v-if="scope.row.sendStatus === 1">已发送</span><span v-else>未发送</span>
						</div>
						<div>
							发货方式：{{ getDeliveryTypeName(scope.row.deliverTypeEnumList) }}
						</div>
						<div>
							<el-popover placement="bottom-start" width="200" trigger="hover"
								:disabled="!scope.row.note">
								<div>备注：{{ scope.row.note }}</div>
								<!-- <i slot="reference" class="iconfont iconPC-beizhu" :class="{ red: scope.row.note }"
									v-if="isSupper || editButton" @click="emitFun('remark', scope.row)"></i> -->
							</el-popover>
						</div>
						<div>
							<el-button size="mini" round type="primary"
								v-if="scope.row.status === 'WAIT_FOR_SEND' && (isSupper || deliverGoodsButton)"
								@click.native="emitFun('send', scope.row, true)">发货</el-button>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="交易额" min-width="180">
					<template slot-scope="scope">
						<div>
							<span v-if="scope.row.type == 'INTEGRAL'">￥{{ scope.row.payAmount }} +
								{{ scope.row.allIntegral ? scope.row.allIntegral : 0 }}积分</span>
							<span v-else>
								原价：￥{{ scope.row.totalAmount }}
								<br>
								优惠：￥{{ scope.row.youhuiPrice }}
								<br>
								扣减：￥{{ scope.row.deductionPrice }}
								<br>
								满减：￥{{ scope.row.promotionAmount }}
								<br>
								现价：￥{{ scope.row.payAmount }}
							</span>
							<br>
							<br><span>{{ scope.row.warehouse && scope.row.warehouse.warehouseFullName || '' }}</span>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="订单状态" min-width="80">
					<template slot-scope="scope">
						<div>
							<template v-if="scope.row.deliveryType === 'SHIPPED'">
								付款成功
							</template>

							<template v-else>
								{{ getOrderStatusName(scope.row.status) }}
							</template>

							<!-- <div v-if="scope.row.status !== 'WAIT_FOR_PAY'" style="margin-top: 5px">

								<el-button v-if="scope.row.status === 'WAIT_FOR_SEND' && (isSupper || deliverGoodsButton)"
									@click="emitFun('send', scope.row, true)" type="primary" size="mini"
									style="margin-bottom: 5px">发货</el-button>
							</div> -->
						</div>
					</template>
				</el-table-column>
				<el-table-column label="效果图上传" min-width="80">
					<template slot-scope="scope">
						<div>
							<template v-if="scope.row.uploadEffectStatus == 1">
								已上传
							</template>
							<template v-else>
								未上传
							</template>
						</div>
					</template>
				</el-table-column>
				<!-- <el-table-column label="支付信息" min-width="180">
					<template slot-scope="{ row }">
						<div>
							支付方式：{{ row.payType }}
						</div>
						<div>
							现金支付金额：￥{{ row.cashAmount }}
						</div>
						<div>
							佣金支付金额：￥{{ row.commissionAmount }}
						</div>
						<div>
							金豆支付金额：￥{{ row.goldenAmount }}
						</div>
					</template>
				</el-table-column> -->

				<!-- <el-table-column label="代下单标识" min-width="120">
					<template slot-scope="{ row }">
						<div>
							{{ row.otherOrder == 1 ? '是' : '否' }}
						</div>
					</template>
				</el-table-column> -->
				<!-- <el-table-column label="赠品标识" min-width="120">
					<template slot-scope="{ row }">
						<div>
							{{ row.otherOrder == 1 ? '是' : '否' }}
						</div>
					</template>
				</el-table-column> -->

				<el-table-column fixed="right" label="操作" width="160">
					<template slot-scope="scope">

						<el-button-group class="fix">
							<el-button plain @click="emitFun('detail', scope.row)" type="primary">查看详情</el-button>

							<!-- <el-button v-if="isSupper || editButton" @click="emitFun('remark', scope.row)" type="primary" plain>备注</el-button> -->


							<el-button v-if="scope.row.status === 'WAIT_FOR_PAY' ||
								(logisticsBtnVisible(scope.row) && scope.row.deliverySn)
							" plain class="dropdown__fix more" type="primary">
								<el-dropdown size="mini" trigger="hover">
									<span class="dropdown__fix--more">...</span>
									<el-dropdown-menu slot="dropdown">
										<el-dropdown-item
											v-if="scope.row.status === 'WAIT_FOR_PAY' && (isSupper || closeButton)"
											@click.native="emitFun('close', scope.row)">关闭订单</el-dropdown-item>
										<el-dropdown-item
											v-if="scope.row.status === 'WAIT_FOR_PAY' && (isSupper || paymentButton)"
											@click.native="emitFun('payment', scope.row)">线下付款</el-dropdown-item>
										<el-dropdown-item
											v-if="scope.row.status === 'WAIT_FOR_PAY' && (isSupper || toExamineButton)"
											@click.native="emitFun('approved', scope.row)">审核通过</el-dropdown-item>
										<el-dropdown-item
											v-if="(scope.row.status === 'WAIT_FOR_SEND' || scope.row.status === 'APPROVED') && (isSupper || notIssuedButton)"
											@click.native="emitFun('notIssued', scope.row)">重置未发送</el-dropdown-item>
										<el-dropdown-item
											v-if="(scope.row.status === 'WAIT_FOR_SEND' || scope.row.status === 'APPROVED') && (isSupper || issuedButton)"
											@click.native="emitFun('issued', scope.row)">重置已发送</el-dropdown-item>
										<el-dropdown-item v-if="logisticsBtnVisible(scope.row) && scope.row.deliverySn"
											@click.native="emitFun('detail', scope.row, true)">
											查看物流</el-dropdown-item>
										<el-dropdown-item
											v-if="(scope.row.status === 'WAIT_FOR_PICKUP' && scope.row.deliveryType === 'LOGISTICS') && (isSupper || issuedButton)"
											@click.native="emitFun('isSend', scope.row, true)">打印面单</el-dropdown-item>

										<el-dropdown-item v-if="isSupper || editButton"
											@click.native="emitFun('remark', scope.row)">备注</el-dropdown-item>

										<el-dropdown-item
											v-if="scope.row.status === 'WAIT_FOR_SEND' && (isSupper || deliverGoodsButton)"
											@click.native="emitFun('send', scope.row, true)">发货</el-dropdown-item>
										<!-- <el-dropdown-item v-if="scope.row.status === 'COMPLETE' && isSupper"
											@click.native="emitFun('delete', scope.row)">删除</el-dropdown-item> -->

									</el-dropdown-menu>
								</el-dropdown>
							</el-button>
							<el-button v-else-if="scope.row.status !== 'WAIT_FOR_PAY'
							" plain class="dropdown__fix more" type="primary">
								<el-dropdown size="mini" trigger="hover">
									<span class="dropdown__fix--more">...</span>
									<el-dropdown-menu slot="dropdown">
										<el-dropdown-item
											v-if="scope.row.status === 'APPROVED' && (isSupper || paymentButton)"
											@click.native="emitFun('payment', scope.row)">线下付款</el-dropdown-item>
										<el-dropdown-item
											v-if="(scope.row.status === 'WAIT_FOR_SEND' || scope.row.status === 'APPROVED') && (isSupper || notIssuedButton)"
											@click.native="emitFun('notIssued', scope.row)">重置未发送</el-dropdown-item>
										<el-dropdown-item
											v-if="(scope.row.status === 'WAIT_FOR_SEND' || scope.row.status === 'APPROVED') && (isSupper || issuedButton)"
											@click.native="emitFun('issued', scope.row)">重置已发送</el-dropdown-item>
										<el-dropdown-item
											v-if="(scope.row.status === 'WAIT_FOR_PICKUP' && scope.row.deliveryType === 'LOGISTICS') && (isSupper || issuedButton)"
											@click.native="emitFun('isSend', scope.row, true)">打印面单</el-dropdown-item>

										<el-dropdown-item v-if="isSupper || editButton"
											@click.native="emitFun('remark', scope.row)">备注</el-dropdown-item>

										<el-dropdown-item
											v-if="scope.row.status === 'WAIT_FOR_SEND' && (isSupper || deliverGoodsButton)"
											@click.native="emitFun('send', scope.row, true)">发货</el-dropdown-item>
										<!-- <el-dropdown-item v-if="scope.row.status === 'COMPLETE' && isSupper"
											@click.native="emitFun('delete', scope.row)">删除</el-dropdown-item> -->

									</el-dropdown-menu>
								</el-dropdown>
							</el-button>
						</el-button-group>

					</template>
				</el-table-column>
			</el-table>

		</template>

		<div class="order__control--bottom fixed" style="width: 100%">
			<!-- <div>
        <el-button-group class="fix" v-if="controlOption">
          <el-button plain @click="emitFun('send')" type="primary"
            >批量发货</el-button
          >
          <el-button
            v-if="controlOption && controlOption.length"
            plain
            class="dropdown__fix more"
            type="primary"
          >
            <el-dropdown
              size="mini"
              v-if="controlOption"
              trigger="hover"
            >
              <span class="dropdown__fix--more">...</span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(item, i) of controlOption"
                  :key="i"
                  @click.native="emitFun(item.value)"
                      >{{ item.label }}</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </el-button>
            </el-button-group>
          </div> -->

			<el-pagination small layout="total,  prev, pager, next,  sizes" :current-page.sync="form.current"
				:size.sync="form.size" :page-size.sync="form.size" :page-sizes="[10, 20, 50, 100]"
				:total.sync="form.total">
			</el-pagination>
		</div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import {
	DeliveryOrderList,
	DeliveryOrderQueryType,
	DeliveryToolOptions,
} from "../orderType";
import {
	getDeliverOrderStatusName,
	getDeliveryTypeName,
	isClose,
	strFilter,
} from "../common/order";
import { getAfterStatusName, getAfterName } from "../common/afterSale";

@Component({ filters: { strFilter } })
export default class OderTable extends Vue {
	/** 表格数据 */
	@Prop({ type: Array })
	data!: DeliveryOrderList[];

	/** 表格内查询按钮 */
	@Prop()
	controlOption!: DeliveryToolOptions[];

	/** 查询条件 */
	@Prop()
	query!: DeliveryOrderQueryType;

	/** 父级已选条码 */
	@Prop()
	checkedItem!: Array<{ orderId: string }>;

	/** 本地form 主要用户页码切换 */
	get form() {
		return this.query;
	}

	set form(v) {
		this.$emit("update:query", v);
	}

	/** 已选表格选项 */
	get tableCheckedItem() {
		return this.checkedItem || [];
	}

	set tableCheckedItem(v) {
		this.$emit("update:checked-item", v);
	}

	/** 表头 */
	columns = [
		{
			label: "商品",
			width: 270,
			coustomStyle: "text-align: left;",
		},
		{
			label: "客户",
			width: 220,
			coustomStyle: "text-align: left;",
		},
		{
			label: "交易额",
			width: 154,
		},
		{
			label: "订单状态",
			width: 115,
		},
		{
			label: "操作",
			width: 142,
		},
	];



	multipleSelection: [];

	menuName = "快递订单";
	buttonList = [];
	isSupper = 0;
	mainFlag = 0;

	editButtonCode = "delivery.edit";
	editButton = false;

	closeButtonCode = "delivery.close";
	closeButton = false;

	paymentButtonCode = "delivery.payment";
	paymentButton = false;

	notIssuedButtonCode = "delivery.notIssued";
	notIssuedButton = false;

	issuedButtonCode = "delivery.issued";
	issuedButton = false;

	toExamineButtonCode = "delivery.toExamine";
	toExamineButton = false;

	deliverGoodsButtonCode = "delivery.deliver.goods";
	deliverGoodsButton = false;

	deliverGoodsBatchButtonCode = "delivery.deliver.batch.goods";
	deliverGoodsBatchButton = false;

	deliveryBatchSendMessageButtonCode = "delivery.batch.send.message";
	deliveryBatchSendMessageButton = false;

	mounted() {
		this.buttonAuth();
	}

	buttonAuth() {
		this.mainFlag = this.$STORE.userStore.userInfo.shopInfoVo.mainFlag
		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter((i: any) => i.menuName == this.menuName)


		let buttonList = [] as any;

		authMenuButtonVos.forEach((element: any) => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var editButtonData = buttonList.find((e: any) => e == this.editButtonCode);

		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}

		var closeButtonData = buttonList.find((e: any) => e == this.closeButtonCode);

		if (closeButtonData != null && closeButtonData != undefined) {
			this.closeButton = true;
		}


		var paymentButtonData = buttonList.find((e: any) => e == this.paymentButtonCode);

		if (paymentButtonData != null && paymentButtonData != undefined) {
			this.paymentButton = true;
		}

		var notIssuedButtonData = buttonList.find((e: any) => e == this.notIssuedButtonCode);

		if (notIssuedButtonData != null && notIssuedButtonData != undefined) {
			this.notIssuedButton = true;
		}

		var issuedButtonData = buttonList.find((e: any) => e == this.issuedButtonCode);

		if (issuedButtonData != null && issuedButtonData != undefined) {
			this.issuedButton = true;
		}

		var toExamineButtonData = buttonList.find((e: any) => e == this.toExamineButtonCode);

		if (toExamineButtonData != null && toExamineButtonData != undefined) {
			this.toExamineButton = true;
		}
		var deliverGoodsButtonData = buttonList.find((e: any) => e == this.deliverGoodsButtonCode);

		if (deliverGoodsButtonData != null && deliverGoodsButtonData != undefined) {
			this.deliverGoodsButton = true;
		}

		var deliverGoodsBatchButtonData = buttonList.find((e: any) => e == this.deliverGoodsBatchButtonCode);

		if (deliverGoodsBatchButtonData != null && deliverGoodsBatchButtonData != undefined) {
			this.deliverGoodsBatchButton = true;
		}
		var deliveryBatchSendMessageButtonData = buttonList.find((e: any) => e == this.deliveryBatchSendMessageButtonCode);

		if (deliveryBatchSendMessageButtonData != null && deliveryBatchSendMessageButtonData != undefined) {
			this.deliveryBatchSendMessageButton = true;
		}
	}

	/** 触发父级方法 */
	emitFun(name: string, data: DeliveryOrderList, status?: boolean) {
		// 阻止未选中元素的批量操作
		if (!data && !this.checkedItem.length && name != 'deliverMessage' && name != 'excelData') {
			return this.$message.info("请先选择记录");
		}
		this.$emit("table-function", name, data, status);
	}

	/** 物流按钮显示隐藏 */
	logisticsBtnVisible(item: DeliveryOrderList) {
		const other = ["WAIT_FOR_PAY", "WAIT_FOR_SEND", "COMPLETE"];
		return (
			!isClose(item.status) &&
			!other.includes(item.status) &&
			item.deliveryType === "LOGISTICS"
		);
	}


	/**
	* 多选
	*/
	handleSelectionChange(val: any) {
		this.multipleSelection = []
		val.forEach((item: any) => {
			this.multipleSelection.push(item.orderId);
		});
		this.$emit("update:checked-item", val);
		// console.log("fdf=",this.multipleSelection);
		// console.log("val=",val);

	}

	getOrderStatusName = getDeliverOrderStatusName;

	getDeliveryTypeName = getDeliveryTypeName;

	isClose = isClose;

	getAfterStatusName = getAfterStatusName;

	getAfterName = getAfterName;
}
</script>

<style lang="scss">
.header__tag {
	border-radius: 0;
	margin-right: 10px;
}

.fixed {
	@include flex(space-between);
	position: fixed;
	bottom: 10px;
	width: 990px !important;
	z-index: 10;
	background: #fff;
	padding: 10px 0;
}

.table__box {
	border-bottom: 1px #ddd solid;
	margin-bottom: 15px;
	padding-bottom: 15px;

	&:last-child {
		border-bottom: none;
		margin-bottom: 0;
		padding-bottom: 0;

	}
}
</style>