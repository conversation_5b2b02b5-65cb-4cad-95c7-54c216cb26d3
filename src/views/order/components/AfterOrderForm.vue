<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 14:07:29
-->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="90px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="商品名称">
            <el-input v-model="form.goodsName" placeholder="请输入商品名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="买家昵称">
            <el-input v-model="form.userName" placeholder="请输入买家昵称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货人姓名">
            <el-input v-model="form.receiverName" placeholder="请输入收货人姓名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="订单号">
            <el-input v-model="form.orderId" placeholder="请输入订单号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="成交时间">
            <el-date-picker v-model="payTime" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="商家信息">
            <el-input v-model="form.shopName" @focus="getShopList" readonly placeholder="请输入商家信息" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-button type="primary" @click="emitSeach">搜索</el-button>
      </el-form-item>
    </el-form>
    <div>
      <el-dialog :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
        <el-table ref="multipleTable" :data="shopList" tooltip-effect="dark" style="width: 100%" :row-key="getRowKeys"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" :reserve-selection="true">
          </el-table-column>
          <el-table-column label="序号" type="index">
          </el-table-column>
          <el-table-column width="55">
          </el-table-column>
          <el-table-column label="商家名称" width="120">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column prop="categoryName" label="所属分类" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="contacts" label="联系人" width="120">
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" show-overflow-tooltip>
          </el-table-column>
        </el-table>
        <PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange" style="margin-top: 0px">
        </PageManage>
        <span slot="footer" class="dialog-footer">
          <!-- <el-button @click="toggleSelection([selectionList[0],tableData[0]])">切换第二、第三行的选中状态</el-button> -->
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="primaryButton">确 定</el-button>
          <el-button type="primary" @click="reset">清 空</el-button>
        </span>
      </el-dialog>
    </div>
  </m-card>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { orderQuery } from "../common/order";
import { cloneDeep } from "lodash";
import { RegionType } from "@/views/goods/goodManage/supplierManageType";
import { DeliveryOrderQueryType } from "../orderType";
import { getShopsPartnerList, getQueryPageList } from "@/api/shopsPartner/shopsPartner";
import PageManage from "@/components/PageManage.vue";

/** 订单顶部查询表单 */
@Component({
  components: {
    PageManage
  }
})
export default class OrderFrom extends Vue {
  @Prop()
  value!: DeliveryOrderQueryType;

  form = cloneDeep(orderQuery);

  payTime = [];

  remarkOption: Array<RegionType> = [
    {
      label: "全部状态",
      value: "0",
    },
    {
      label: "有备注",
      value: "1",
    },
    {
      label: "无备注",
      value: "2",
    },
  ];

  areas = [
    {
      label: "商超",
      value: "2",
    },
  ];
  shopList = [];
  current = 1;
  size = 10;
  total = 0;
  dialogVisible = false;
  selectedArray  = []
  /** 监听数据变化 维护本地查询条件 */
  @Watch("value", { immediate: true, deep: true })
  handleFormChange(v: DeliveryOrderQueryType) {
    this.form = cloneDeep(v);
  }

  @Watch("payTime")
  handlePayTimehange(v: DeliveryOrderQueryType[]) {
    if (!v || v.length === 0) {
      Object.assign(this.form, { startTime: "", endTime: "" });
    } else {
      Object.assign(this.form, { startTime: v[0], endTime: v[1] });
    }
  }

    /**
 * @method handleCurrentChange
 * @description 当前页
 */
 handleCurrentChange(val: number) {
    this.current = val;
    this.getShopList()
    // this.getPageTicket();
  }
  handleSizeChange(val: number) {
    this.current = 1;
    this.size = val;
    this.getPageList();
  }
  handleClose(done: any) {
    this.selectedArray = []
    done();
  }

  close(){
    this.selectedArray = []
    this.dialogVisible = false;
  }
  reset(){
    this.selectedArray = []
    this.form.queryShopIds = "";
    this.form.shopName = "";
    this.$refs.multipleTable.clearSelection();
    this.dialogVisible = false;
  }
  primaryButton() {
    this.form.queryShopIds = this.selectedArray.map(row => row.shopId).join(",");
    this.form.shopName = this.selectedArray.map(row => row.name).join("，");
    this.dialogVisible = false;
  }
  handleSelectionChange(val) {
    this.selectedArray = val
  }
  getRowKeys(row) {
    return row.id //唯一性
  }
  /** 触发父级查询 */
  emitSeach() {
    this.$emit("input", this.form);
  }

    /**
  * 获取店铺列表
  */
  getShopList() {
    let params = {}
    params.prohibitStatus = "0"
    params.current = this.current
    params.size = this.size
    getQueryPageList(params).then((res) => {
      this.shopList = res.data.list
      this.total = res.data.total
      this.dialogVisible = true
    }).catch((err) => {
      this.$message.error(err)
    })

  }
}
</script>

<style lang="scss" scoped>
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  &.show {
    height: 264px;
  }

  &.hide {
    height: 50px;

    .form__btn {
      width: 940px;
      height: 50px;
      background: #f9f9f9;
      line-height: 50px;
      // margin-top: 20px
    }
  }

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}
</style>
