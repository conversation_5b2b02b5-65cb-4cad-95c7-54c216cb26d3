<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 17:16:33
-->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="120px">

      <el-row>
        <el-col :span="8">
          <el-form-item label="销售专区">
            <el-select v-model="form.area" placeholder="请选择销售专区" style="width: 100%;">
              <el-option label="全部" :value="null" />
              <el-option v-for="item of areas" :key="item.id" :label="item.modeName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="下单日期">
            <el-date-picker v-model="orderDate" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
              style="width: 100%;" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="成交日期">
            <el-date-picker v-model="payTime" type="daterange" value-format="yyyy-MM-dd" range-separator="至"
              style="width: 100%;" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="商品名称">
            <el-input v-model="form.goodsName" placeholder="请输入商品名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="买家昵称">
            <el-input v-model="form.userName" placeholder="请输入买家昵称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货人姓名">
            <el-input v-model="form.receiverName" placeholder="请输入收货人姓名"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="订单号">
            <el-input v-model="form.orderId" placeholder="请输入订单号"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="物流单号">
            <el-input placeholder="请输入物流单号" v-model="form.deliverySn"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注状态">
            <el-select v-model="form.remarkType" placeholder="请选择备注状态" style="width: 100%;">
              <el-option v-for="item of remarkOption" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="商家信息">
            <el-input v-model="form.shopName" @focus="getShopList" readonly placeholder="请输入商家信息" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="门店名称">
            <el-input v-model="form.storeFrontName" placeholder="请输入门店名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货人电话号码">
            <el-input v-model="form.receiverPhone" placeholder="请输入收货人电话号码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="买家电话号码">
            <el-input v-model="form.phone" placeholder="请输入买家电话号码"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否退货">
            <el-select v-model="form.isRefund" placeholder="请选择" style="width: 100%;">
              <el-option v-for="item in isRefundOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="效果图上传">
            <el-select v-model="form.uploadEffectStatus" placeholder="请选择" style="width: 100%;">
              <el-option v-for="item in uploadEffectStatusOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单组编号">
            <el-input v-model="form.orderGroupId" placeholder="请输入订单组编号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注">
            <el-input type="textarea" :rows="2" v-model="form.note" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" @click="emitSeach">搜索</el-button>
      </el-form-item>
    </el-form>
    <div>
      <el-dialog :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
        <el-table ref="multipleTable" :data="shopList" tooltip-effect="dark" style="width: 100%" :row-key="getRowKeys"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" :reserve-selection="true">
          </el-table-column>
          <el-table-column label="序号" type="index">
          </el-table-column>
          <el-table-column width="55">
          </el-table-column>
          <el-table-column label="商家名称" width="120">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column prop="categoryName" label="所属分类" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="contacts" label="联系人" width="120">
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" show-overflow-tooltip>
          </el-table-column>
        </el-table>
        <PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
          @handleCurrentChange="handleCurrentChange" style="margin-top: 0px">
        </PageManage>
        <span slot="footer" class="dialog-footer">
          <!-- <el-button @click="toggleSelection([selectionList[0],tableData[0]])">切换第二、第三行的选中状态</el-button> -->
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="primaryButton">确 定</el-button>
          <el-button type="primary" @click="reset">清 空</el-button>
        </span>
      </el-dialog>
    </div>
  </m-card>
</template>

<script lang="ts">
/* eslint-disable indent */
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { orderQuery } from "../common/order";
import { DeliveryToolOptions } from "../orderType";
import { getRegionList } from "@/api/good/goods";
import { getShopsPartnerList, getQueryPageList } from "@/api/shopsPartner/shopsPartner";
import PageManage from "@/components/PageManage.vue";

/** 订单顶部查询表单 */
@Component({
  components: {
    PageManage
  }
})
export default class OrderFrom extends Vue {
  @Prop({ default: () => ({}) })
  value!: any;

  form = { ...orderQuery };

  payTime = [];

  orderDate = [];

  /** 配送类型 */
  deliverTypes: Array<DeliveryToolOptions> = [
    {
      label: "物流配送",
      value: "102",
    },
  ];

  /** 备注选项 */
  remarkOption: Array<DeliveryToolOptions> = [
    {
      label: "全部状态",
      value: "0",
    },
    {
      label: "有备注",
      value: "1",
    },
    {
      label: "无备注",
      value: "2",
    },
  ];

  /** 退款选项 */
  isRefundOptions: Array<{ value: string, label: string }> = [
    { value: '-1', label: '所有' },
    { value: '1', label: '是' },
    { value: '0', label: '否' }
  ];

  uploadEffectStatusOptions: Array<{ value: string, label: string }> = [
    { value: '-1', label: '全部' },
    { value: '1', label: '已上传' },
    { value: '0', label: '未上传' }
  ];

  areas = [];

  shopList = [];

  current = 1;

  size = 10;

  total = 0;

  dialogVisible = false;

  selectedArray = []

  mounted() {
    this.getRegionList();
    //判断路由是否有值 如果有则搜索该订单
    if (this.$route.query.orderId) {
      this.form.orderId = this.$route.query.orderId as string;
    }
    //判断路由是否有用户名称参数
    if (this.$route.query.userName || this.$route.query.productName || this.$route.query.orderGroupId) {
      this.form.userName = this.$route.query.userName as string;
      this.form.goodsName = this.$route.query.productName as string;
      this.form.orderGroupId = this.$route.query.orderGroupId as string;
    }
    //判断路由是否有买家电话号码参数
    if (this.$route.query.phone) {
      this.form.phone = this.$route.query.phone as string;
    }
  }

  // get areas() {
  //   return [
  //     {
  //       label: "商超",
  //       value: "2",
  //     },
  //   ];
  // }


  @Watch("payTime")
  handlePayTimehange(v: Array<{ startTime: string; endTime: string }>) {
    if (!v || v.length === 0) {
      Object.assign(this.form, { startTime: "", endTime: "" });
    } else {
      Object.assign(this.form, { startTime: v[0], endTime: v[1] });
    }
  }

  @Watch("orderDate")
  handleOrderDateChange(v: Array<{ startDate: string; endDate: string }>) {
    if (!v || v.length === 0) {
      Object.assign(this.form, { startDate: "", endDate: "" });
    } else {
      Object.assign(this.form, { startDate: v[0], endDate: v[1] });
    }
  }

  /**
 * @method handleCurrentChange
 * @description 当前页
 */
  handleCurrentChange(val: number) {
    this.current = val;
    this.getShopList()
    // this.getPageTicket();
  }

  handleClose(done: any) {
    this.selectedArray = []
    done();
  }

  close() {
    this.selectedArray = []
    this.dialogVisible = false;
  }

  reset() {
    this.selectedArray = []
    this.form.queryShopIds = "";
    this.form.shopName = "";
    this.$refs.multipleTable.clearSelection();
    this.dialogVisible = false;
  }

  primaryButton() {
    this.form.queryShopIds = this.selectedArray.map(row => row.shopId).join(",");
    this.form.shopName = this.selectedArray.map(row => row.name).join("，");
    this.dialogVisible = false;
  }

  handleSelectionChange(val) {
    this.selectedArray = val
  }

  getRowKeys(row) {
    return row.id //唯一性
  }

  /**
  * @method handleSizeChange
  * @description 每页 条
  */
  handleSizeChange(val: number) {
    this.size = val;
    this.getShopList()
  }

  /**
   * 获取所有专区
   */
  async getRegionList() {
    this.form.area = ""
    const param = {
      current: 1,
      size: 20,
    };
    getRegionList(param).then(res => {
      this.areas = res.data.list
    })
  }

  /** 触发父级查询 */
  emitSeach() {
    console.log(this.form);
    this.$emit("input", { ...this.form, current: 1 });
  }

  /**
  * 获取店铺列表
  */
  getShopList() {
    let params = {}
    params.prohibitStatus = "0"
    params.current = this.current
    params.size = this.size
    getQueryPageList(params).then((res) => {
      this.shopList = res.data.list
      this.total = res.data.total
      this.dialogVisible = true
    }).catch((err) => {
      this.$message.error(err)
    })

  }
}
</script>

<style lang="scss" scoped>
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}
</style>
