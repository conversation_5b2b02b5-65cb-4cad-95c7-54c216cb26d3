<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 17:16:33
  2025.5.30有改动的页面
  代发货搜索
-->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="90px">

      <el-row>
        <el-col :span="8">
          <el-form-item label="用户名称">
            <el-input v-model="form.userName" placeholder="请输入用户名称"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="商品名称">
            <el-input v-model="form.productName" placeholder="请输入商品名称"></el-input>
          </el-form-item>
        </el-col>

      </el-row>

      
      <el-form-item>
        <el-button type="primary" @click="emitSeach">搜索</el-button>
      </el-form-item>
    </el-form>
  
  </m-card>
</template>

<script lang="ts">
/* eslint-disable indent */
import { Vue, Component, Watch } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";

/** 订单顶部查询表单 */
@Component({
  components: {
    PageManage
  }
})
export default class OrderFrom extends Vue {
  form =  {
    "current": 1,
    "size": 10,
    "userName": "",
    "productName": "",
    "deliveryStatus": '',
    "total": 0,

  };;

  
  mounted() {  

  }

 

  /** 触发父级查询 */
  emitSeach() {
    console.log(this.form);
    this.$emit("input-set", this.form);
  }


}
</script>

<style lang="scss" scoped>
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}
</style>
