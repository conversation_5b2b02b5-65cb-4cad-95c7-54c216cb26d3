<!-- 
 2025.5.28有改动的页面 
-->
<template>
    <div class="box">
        <!-- 搜索 -->
        <m-card class="form" :needToggle="true">

            <el-form class="customer__dataForm" ref="dataFormRef" :model="dataForm" label-width="120px">
                <el-row :gutter="40">
                    <el-col :span="10">
                        <el-form-item label="门店名称"><el-input v-model="dataForm.storeFrontName" clearable
                                placeholder="请输入门店名称" /></el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="下单时间">
                            <el-date-picker v-model="date" type="daterange" value-format="yyyy-MM-dd"
                                style="width: 100%;" range-separator="至" start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="40">
                    <el-col :span="10">
                        <el-form-item label="商品名称"><el-input v-model="dataForm.productName" clearable
                                placeholder="请输入商品名称" /></el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="职员名称"><el-input v-model="dataForm.accountName" clearable
                                placeholder="请输入职员名称" /></el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="40">
                    <el-col :span="10">
                        <el-form-item label="订单号"><el-input v-model="dataForm.orderId" clearable
                                placeholder="请输入订单号" /></el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="订单状态">
                            <el-select v-model="value" multiple placeholder="请选择订单状态" style="width: 100%;"
                                @change="handleOrderStatusChange">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="40">
                    <el-col :span="10">
                        <el-form-item label="收货人电话号码">
                            <el-input v-model="dataForm.receiverPhone" placeholder="请输入收货人电话号码"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="10">
                        <el-form-item label="买家电话号码">
                            <el-input v-model="dataForm.phone" placeholder="请输入买家电话号码"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="40">
                    <el-col :span="10">
                        <el-form-item label="是否退货">
                            <el-select v-model="dataForm.isRefund" placeholder="请选择" style="width: 100%;">
                                <el-option v-for="item in isRefundOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-button type="primary" style="margin-left:100px; margin-bottom: 30px;"
                    @click="search()">搜索</el-button>
            </el-form>
        </m-card>
        <!-- 导出按钮 -->
        <div class="topLine" style="margin-top: 20px;">
            <div class="topLine__left">
                <div style="margin-left: 30px;">
                    <el-button type="primary" round @click="exportData">导出列表</el-button>
                </div>
            </div>
        </div>
        <el-table :data="orderDetail" border show-summary :summary-method="getSummaries"
            style="width: 100%; margin-top: 20px">
            <el-table-column type="index" :index="indexMethod" label="序号">
            </el-table-column>
            <el-table-column prop="" label="订单号" width="180">
                <template slot-scope="scope">
                    <span style="cursor: pointer; color:rgb(64, 158, 255) ;"
                        @click="lookOrderDetail(scope.row.orderId)">{{ scope.row.orderId }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="productName" label="商品名称" width="200">
            </el-table-column>
            <el-table-column prop="productPrice" label="单价">
            </el-table-column>
            <el-table-column prop="productQuantity" label="数量">
            </el-table-column>
            <el-table-column prop="realAmount" label="金额">
            </el-table-column>
            <el-table-column prop="refundProductQuantity" label="退货数量">
            </el-table-column>
            <el-table-column prop="refundRealAmount" label="退款金额">
            </el-table-column>
            <el-table-column prop="storeFrontName" label="门店名称" width="150"></el-table-column>
            <el-table-column prop="nikeName" label="客户名称" width="140">
            </el-table-column>


            <el-table-column prop="phone" label="电话号码">
            </el-table-column>
            <el-table-column prop="accountName" label="职员名称">
            </el-table-column>
            <el-table-column prop="deliverTypeName" label="发货方式">

            </el-table-column>
            <el-table-column prop="warehouseName" label="发货仓库" width="150">
            </el-table-column>
        </el-table>

        <PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" class="PageManage"
            @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>
</template>

<script>
import { getOrderDetailByOrderId, exportOrderDetail } from "@/api/order";
import PageManage from '@/components/PageManage.vue';
import debounce from 'lodash/debounce'
import { getDeliveryTypeName } from '@/views/order/common/order.ts'
export default {
    name: "orderDetails",//订单明细
    data() {
        return {
            orderDetail: [],
            dataForm: {
                productName: "",
                orderId: "",
                orderStatus: "-1",
                storeFrontName: "",
                startDate: "",
                endDate: "",
                accountName: "",
                phone: "",
                receiverPhone: "",
                isRefund: '-1', //是否退款
            },
            date: [],
            isRefundOptions: [
                { value: '-1', label: '所有' },
                { value: '1', label: '是' },
                { value: '0', label: '否' }
            ],
            options: [{
                value: '-1',
                label: '所有订单'
            }, {
                value: '0',
                label: '待付款'
            }, {
                value: '1',
                label: '待发货'
            }, {
                value: '2',
                label: '配送中'
            }, {
                value: '3',
                label: '待提货'
            },
            {
                value: '4',
                label: '已完成'
            },
            {
                value: '5',
                label: '已关闭  '
            }],
            value: [],
            /** 分页条数 */
            pageSize: 10,

            /** 分页页码 */
            pageNum: 1,

            /** 数据长度 */
            total: 0,
            search: debounce(() => {
                this.getOrderDetail(1)
            }, 500),
        }
    },
    mounted() {
        this.getOrderDetail(1);
        let { name, roterName } = this.$route.params
        if (roterName === 'packageStoreCollect') {
            this.dataForm.storeFrontName = name
            this.search()
        } else if (roterName === 'packageEmployeeCollect') {
            this.dataForm.accountName = name
            this.search()
        }

    },
    methods: {
        indexMethod(index) {
            return index + 1;
        },
        // 处理订单状态多选变化
        handleOrderStatusChange(selectedValues) {
            console.log("ddd:", selectedValues);
            console.log("ddd:", selectedValues.includes('-1'));
            // 如果选择了"所有订单"，清空其他选项
            if ((selectedValues.includes('-1') && selectedValues.length === 1) || selectedValues[selectedValues.length - 1] == '-1') {
                this.value = ['-1'];
            } else {  // 如果选择了具体状态，不能选"所有订单"
                this.value = selectedValues.filter(val => val != '-1');
            }
        },
        getOrderDetail(pageNum) {

            // 处理多选订单状态
            if (this.value.length === 0 || this.value.includes('-1')) {
                this.dataForm.orderStatus = ['-1']; // 所有订单
            } else {
                this.dataForm.orderStatus = this.value;
            }
            //判断是否选择了日期
            if (this.date) {
                this.dataForm.startDate = this.date[0];
                this.dataForm.endDate = this.date[1];
            }

            //提交的参数
            let params = {
                ...this.dataForm,
                current: pageNum,
                size: this.pageSize,
            };
            getOrderDetailByOrderId(params)
                .then(res => {
                    this.orderDetail = res.data.list;
                    this.total = res.data.total;
                })
                .catch(err => {
                    console.log(err);
                });
            this.pageNum = pageNum;
        },
        /**
        * @method handleSizeChange
         * @description 每页 条
        */
        handleSizeChange(val) {
            this.pageSize = val;
            this.getOrderDetail(1);
        },
        /**
         * @method handleCurrentChange
         * @description 当前页
         */
        handleCurrentChange(val) {
            this.pageNum = val;
            this.getOrderDetail(val);
        },
        getDeliveryTypeName(type) {
            return getDeliveryTypeName(type)
        },
        // 导出数据
        async exportData() {
            this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {

                // 处理多选订单状态
                if (this.value.length === 0 || this.value.includes('-1')) {
                    this.dataForm.orderStatus = ['-1']; // 所有订单
                } else {
                    this.dataForm.orderStatus = this.value;
                }
                //判断是否选择了日期
                if (this.date) {
                    this.dataForm.startDate = this.date[0];
                    this.dataForm.endDate = this.date[1];
                }
                const params = {
                    ...this.dataForm,
                    current: this.pageNum,
                    size: this.pageSize,
                };

                exportOrderDetail(params).then((res) => {
                    var blob = new Blob([res.data], {
                        type: "application/x-msdownload;charset=UTF-8",
                    });
                    // 创建一个blob的对象链接
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    // 把获得的blob的对象链接赋值给新创建的这个 a 链接
                    let now = new Date();
                    let timestamp = now.getTime();
                    link.setAttribute('download', '订单明细列表_' + timestamp + '.xls'); // 设置下载文件名
                    document.body.appendChild(link);

                    // 触发下载
                    link.click();
                    // 清理
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    this.$message.success('导出成功');

                }).catch((err) => {
                    this.$message.error("导出失败");
                });
            }).catch(() => {
                // 用户取消导出
            });
        },
        //合计
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                //判断字段为payAmount或者productQuantity才进行合计 并且处理小数点位数
                if (column.property === 'productQuantity' || column.property === 'realAmount') {
                    const values = data.map(item => Number(item[column.property]));
                    const total = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return prev + curr;
                        }
                        return prev;
                    }, 0);
                    const totalStr = total.toFixed(2);
                    sums[index] = totalStr;
                }
            });

            return sums;
        },
        //查看订单详情
        lookOrderDetail(orderId) {
            this.$router.push({ path: `delivery`, query: { orderId: orderId } });
        },

    },
    components: {
        PageManage
    }
}
</script>
