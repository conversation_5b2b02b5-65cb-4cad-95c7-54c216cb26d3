<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 10:45:18
-->
<template>
  <div>
    <!-- <m-container class="order" :pagination-visible="false" :current.sync="query.current" :size.sync="query.size"
      :total.sync="query.total">
      <MarketingFrom v-model="query" slot="form" @input-set="inputSet" :query.sync="query" />

      <template slot="content">
        <el-tabs v-model="query.status2" @tab-click="handleTabClick">
          <el-tab-pane label="所有预约" name="0"></el-tab-pane>
          <el-tab-pane label="待评价" name="104"> </el-tab-pane>
          <el-tab-pane label="已完成" name="105"> </el-tab-pane>
          <el-tab-pane label="已失效" name="300"> </el-tab-pane>
        </el-tabs>

      </template>
</m-container> -->

    <MarketingFrom  v-model="query" slot="form" @input-set="inputSet" :query.sync="query" />

    <div style="margin-bottom: 20px"></div>
    <MarketingTable @table-function="tableFunction" :data="dataList" :query.sync="query" @input-set="inputSet"
      :checked-item.sync="checkedItem" :controlOption="getControlOption(query.orderStatus)" />

    <OrderDetailModal v-model="detailVisible" :detail="orderDetail" :need-tabs="true" :is-delivery="true"
      :type="detailType" @reset="handleSeeDetail" @closeDetail="closeDetail" />

  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch } from "vue-property-decorator";
import MarketingFrom from "./components/MarketingFrom.vue";
import MarketingTable from "./components/MarketingTable.vue";
import OrderDetailModal from "./components/detail/Index.vue";
import {
  DeliveryState,
  DeliveryOrderList,
} from "./orderType";

import { filterEmpty } from "./common/order";

import {
  close,
} from "@/api/order";

import {  getDetail, getCurShopPrizeList } from "@/api/marketing/index"

Component.registerHooks(["beforeRouteEnter", "beforeRouteUpdate"]);

@Component({
  components: {
    MarketingFrom,
    MarketingTable,
    OrderDetailModal,
  },
})
export default class DeliveryOrder extends Vue {


  checkedItem = null

  detailVisible = false;

  detailType = "1";

  created() {
    // 检查URL参数，如果有奖项名称参数则设置到查询条件中
    const routeQuery = this.$route.query;
    if (routeQuery.levelName) {
      this.query.levelName = routeQuery.levelName.toString();
    }
    if (routeQuery.mainPrizeName) {
      this.query.mainPrizeName = routeQuery.mainPrizeName.toString();
    }
    if (routeQuery.prizeName) {
      this.query.prizeName = routeQuery.prizeName.toString();
    }
    this.getCurShopPrizeList(this.query);
  }



  //-----------------------------------------------
  query: any = {
    date: '',
    startTime: '',
    endTime: '',

    current: 1,
    size: 20,
    total: 0,
    userPhone: '',
    userName: '',
    levelName: '', // 奖项名称
  };

  dataList = [];

  orderDetail = {};



  /** 监听query变化 */
  // @Watch("query", { deep: true })
  // handleQueryChange(v: any) {
  //   this.getCurShopPrizeList(filterEmpty({ ...v }));
  //   console.log('监听query变化', v)
  // }

  getCurShopPrizeList(param: any) {
    getCurShopPrizeList(param).then(res => {
      this.dataList = res.data.list;
      this.query.total = res.data.total;     
    }).catch(err => {
      this.$message.error(err)
    })
  }

  handleTabClick({ name: orderStatus }: { name: string }) {
    if (orderStatus != '0') {
      Object.assign(this.query, {
        status: orderStatus,
        current: 1,
      });
    } else {
      Object.assign(this.query, {
        status: null,
        current: 1,
      });
    }
  }

  closeDetail() {
    console.log("closeDetail=");
  }

  inputSet(form: any) {
    console.log("inputSet=", form);
    // this.query = { }
    this.getCurShopPrizeList(filterEmpty({ ...form }));
  }

  getDetail(row: any) {
    getDetail({ id: row.id }).then(res => {
      console.log("res111111=", res.data);
      this.orderDetail = res.data;
      this.detailVisible = true;
    }).catch(err => {
      this.$message.error(err)
    })
  }
  //-----------------------------------------------

  getControlOption(orderStatus: any) {
    console.log("handleTabClick=");
  }

  /**
   * 监听table传来的事件
   * @param {name} 事件名 remark | close | detail
   */
  tableFunction(name: string, data: DeliveryOrderList, isLogistics: boolean) {
    console.log("name", name);
    switch (name) {
      case "remark":
        return this.triggerRemark(data);
      case "detail":
        return this.getDetail(data);
      case "orderDetail":
        return this.goToOrderDetail(data);
      case "addLuckyDraw":
        return this.addLuckyDraw(data);
      case "copyLuckyDraw":
        return this.copyLuckyDraw(data);

    }
  }

  addLuckyDraw(data: any) {
    this.$router.push("/marketing/marketingList/addLuckyDraw")
  }

  copyLuckyDraw(data: any) {

  }

  /**
   * 跳转到订单详情页面
   */
  goToOrderDetail(data: any) {

    this.$router.push({
      path: '/order/delivery',
      query: {
        userName: data.userName,
        productName: data.prizeName,
        orderGroupId: data.postOrderId
      }
    });
    // this.$router.push({
    //   path: '/order/delivery',
    //   query: {
    //     userName: "HH"
    //   }
    // });
  }

  /**
   * 查看详情
   */
  async handleSeeDetail(orderData: DeliveryOrderList, isLogistics: boolean) {
    console.log("orderData", orderData);
    console.log("isLogistics", isLogistics);

    //
  }

  /**
   * 关闭
   * @param {orderData} 订单数据 如果没有参数为批量操作
   */
  handleClose(orderData?: any) {
    this.$confirm("确定关闭订单？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        const orderIds = orderData
          ? [orderData.orderId]
          : [...this.selectedIds];
        close(orderIds)
          .then(() => {
            this.getOrders(Object.assign({}, this.query, this.$route.query));
            this.$message.success("关闭成功");
          })
          .catch((err) => {
            this.$message.warning(err || "关闭失败");
          });
      })
      .catch(() => {
        //
      });
  }


}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/order/order.scss";
</style>
