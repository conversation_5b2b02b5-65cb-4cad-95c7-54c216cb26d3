<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 14:08:55
 2025.5.28有改动的页面
-->
<template>
	<div>
		<!-- <div class="order__control--top">
			<div class="control--l">
				<el-button-group class="fix" >
					<el-button plain @click="emitFun('addLuckyDraw')" type="primary"
						>新增抽奖活动</el-button>
					<el-button plain @click="emitFun('copyLuckyDraw')" type="primary"
						>复制抽奖方案</el-button>
					
				</el-button-group>
			</div>
		</div> -->

		<template>
			<el-table ref="multipleTable" :data="data" border tooltip-effect="dark" style="width: 100%" height="670"
				@selection-change="handleSelectionChange">
				<el-table-column type="selection" width="55" fixed="left">
				</el-table-column>
				<el-table-column label="活动名称" min-width="200" fixed="left" prop="mainPrizeName">
        </el-table-column>
        <el-table-column label="奖项名称" min-width="200" prop="levelName">
				</el-table-column>
				<el-table-column label="奖品名称" min-width="200" prop="prizeName">

				</el-table-column>
				<el-table-column label="客户名称" min-width="160" prop="userName">
				</el-table-column>
				<el-table-column label="客户号码" min-width="160" prop="userPhone">
				</el-table-column>
				<el-table-column label="是否中奖" min-width="120" prop="content">
					<template slot-scope="{row}">
					{{ row.prizeFlag == 1 ? '是' : row.prizeFlag == 0 ? '否' : '' }}
					</template>
				</el-table-column>
				<el-table-column label="中奖时间" min-width="180" prop="createTime">
				</el-table-column>
				<el-table-column label="奖品类型" min-width="180" prop="content">
					<template slot-scope="{row}">
					{{ row.prizeType == 1 ? '商城商品' :  row.prizeType == 2 ? '优惠券' : '' }}
					</template>
				</el-table-column>
				<el-table-column label="奖品发放方式" min-width="180" prop="content">
					<template slot-scope="{row}">
					{{ row.verifyType == 1 ? '线上邮寄' : row.verifyType == 2 ? '线下核销' : '' }}
					</template>
				</el-table-column>
				<el-table-column label="状态" min-width="120" prop="mobile">
					<template slot-scope="{row}">
			<!--						<el-tag type="warning" v-if="row.status == 100">未核销</el-tag>
					<el-tag type="success" v-else-if="row.status == 101">已核销</el-tag>
					<el-tag type="danger" v-else-if="row.status == 200">已失效</el-tag>-->
						<el-tag type="danger" v-if="row.status == 0">未发放</el-tag>
						<el-tag type="success" v-else-if="row.status == 1">已发放</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="有效期" min-width="300" prop="content">
					<template slot-scope="{row}">
					{{ row.startTime }} - {{ row.endTime }}
					</template>
				</el-table-column>

				<el-table-column label="核销人" min-width="180" prop="verifyUserName">
				</el-table-column>
			
				<el-table-column label="核销时间" min-width="300" prop="verifyTime">					
				</el-table-column>
								
				<el-table-column fixed="right" label="操作" width="120">
					<template slot-scope="scope">

						<el-button
							plain
							@click="emitFun('orderDetail', scope.row)"
							type="primary"
							:disabled="!canShowOrderDetail(scope.row)">
							订单详情
						</el-button>



					</template>
				</el-table-column>
			</el-table>

		</template>

		<div class="order__control--bottom fixed" style="width: 100%">			

			<el-pagination small layout="total,  prev, pager, next,  sizes" :current-page.sync="form.current"
				:size.sync="form.size" :page-size.sync="form.size" :page-sizes="[10, 20, 50, 100]" :total.sync="form.total" @current-change="handleCurrentChange"
				@size-change="handleSizeChange">
			</el-pagination>
		</div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";

import {
  isClose,
  strFilter,
} from "../common/order";

@Component({ filters: { strFilter } })
export default class OderTable extends Vue {
	/** 表格数据 */
	@Prop({ type: Array })
	data!: any[];

	/** 表格内查询按钮 */
	@Prop()
	controlOption!: any[];

	/** 查询条件 */
	@Prop()
	query!: any;

	/** 父级已选条码 */
	@Prop()
	checkedItem!: Array<{ orderId: string }>;

	/** 本地form 主要用户页码切换 */
	get form() {
	  return this.query;
	}

	// set form(v) {
	// 	this.$emit("input-set", v);
	// }

	
	/** 已选表格选项 */
	get tableCheckedItem() {
	  return this.checkedItem || [];
	}

	set tableCheckedItem(v) {
	  this.$emit("update:checked-item", v);
	}


	multipleSelection: [];



	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	 handleSizeChange(val: number) {
	    this.form.size = val;
	    this.$emit("input-set", this.form);
	}
	
	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
	    this.form.current = val;
	    console.log('当前页',val);
	    this.$emit("input-set", this.form);
	}



	/** 触发父级方法 */
	emitFun(name: string, data?: any, status?: boolean) {
	  // 阻止未选中元素的批量操作
	  // if (!data && !this.checkedItem.length && name != 'deliverMessage') {
	  // 	return this.$message.info("请先选择条目");
	  // }
	  this.$emit("table-function", name, data, status);
	}

	/** 物流按钮显示隐藏 */
	logisticsBtnVisible(item: DeliveryOrderList) {
	  const other = ["WAIT_FOR_PAY", "WAIT_FOR_SEND", "COMPLETE"];
	  return (
	    !isClose(item.status) &&
			!other.includes(item.status) &&
			item.deliveryType === "LOGISTICS"
	  );
	}


	/**
	* 多选
	*/
	handleSelectionChange(val: any) {
	  this.multipleSelection = []
	  val.forEach((item: any) => {
	    this.multipleSelection.push(item.orderId);
	  });
	  this.$emit("update:checked-item", val);
	  // console.log("fdf=",this.multipleSelection);
	  // console.log("val=",val);

	}

	/**
	 * 判断是否可以显示订单详情按钮
	 * 条件：中奖 && 已发放 && 奖品类型为商场商品 && 线上邮寄
	 */
	canShowOrderDetail(row: any) {
	  return row.prizeFlag == 1 &&
			   row.status == 1 &&
			   row.prizeType == 1 && row.verifyType ==1;
	}

}
</script>

<style lang="scss">
.header__tag {
	border-radius: 0;
	margin-right: 10px;
}

.fixed {
	@include flex(space-between);
	position: fixed;
	bottom: 10px;
	width: 990px !important;
	z-index: 10;
	background: #fff;
	padding: 10px 0;
}
</style>