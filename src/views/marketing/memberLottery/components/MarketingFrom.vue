<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 17:16:33
  2025.5.30有改动的页面
  代发货搜索
-->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="90px">

      <el-row>
        <el-col :span="10">
          <el-form-item label="客户名称">
            <el-input v-model="form.userName" placeholder="请输入客户名称"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="客户号码">
            <el-input v-model="form.userPhone" placeholder="请输入客户号码"></el-input>
          </el-form-item>
        </el-col>

      </el-row>

      <el-row>
        <!-- <el-col :span="10">
          <el-form-item label="状态">
            <el-select v-model="form.status" placeholder="请选择" style="width: 100%" size="small">
              <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="10">
          <el-form-item label="审核状态">
            <el-select v-model="form.approvalStatus" placeholder="请选择" style="width: 100%" size="small">
              <el-option v-for="item in approvalStatusList" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="10">
          <el-form-item label="活动名称">
            <el-input v-model="form.mainPrizeName" placeholder="请输入活动名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="中奖时间">
            <el-date-picker v-model="form.date" type="daterange" range-separator="-" start-placeholder="开始时间"
              end-placeholder="结束时间" style="width: 100%;" @change="chooseTimes">
            </el-date-picker>
          </el-form-item>
        </el-col>

      </el-row>

      <!-- <el-row>
        <el-col :span="10">
          <el-form-item label="有效期">
            <el-date-picker v-model="form.date" type="daterange" range-separator="-" start-placeholder="开始时间"
              end-placeholder="结束时间" style="width: 100%;" @change="chooseTimes">
            </el-date-picker>
          </el-form-item>
        </el-col>


      </el-row> -->

      <el-row>
        <el-col :span="10">
          <el-form-item label="奖项名称">
            <el-input v-model="form.levelName" placeholder="请输入奖项名称" @input="handleFieldChange"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="商品名称">
            <el-input v-model="form.productName" placeholder="请输入商品名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="10">
          <el-form-item label="奖品类型">
            <el-select v-model="form.prizeType" placeholder="请选择">
              <el-option v-for="item in prizeTypeOption" :key="item.value" :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col  :span="10">
          <el-form-item label="奖品名称">
            <el-input v-model="form.prizeName" placeholder="请输入奖品名称"></el-input>
          </el-form-item>

        </el-col>
      </el-row>

      <el-row>
        <el-col :span="10">
          <el-form-item label="核销人名称">
            <el-input v-model="form.verifyNickName" placeholder="请输入奖品名称"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" @click="emitSeach">搜索</el-button>
      </el-form-item>
    </el-form>

  </m-card>
</template>

<script lang="ts">
/* eslint-disable indent */
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import DateUtil from "@/store/modules/date";

/** 订单顶部查询表单 */
@Component({
  components: {
    PageManage
  }
})
export default class OrderFrom extends Vue {

  @Prop({ default: () => ({}) }) value!: any;
  form = {
    date: '',
    createStartTime: '',
    createEndTime: '',

    current: 1,
    size: 20,
    total: 0,
    userPhone: '',
    userName: '',
    mainPrizeName:'',
    productName: '',
    prizeName: '',
    prizeType:"",
    levelName: '',
    verifyNickName:"",
  };
  prizeTypeOption=[
    {
      value: "",
      label: "全部"
    },
    {
      value: "1",
      label: "商品"
    },
    {
      value: "2",
      label: "优惠券"
    }
  ]

  mounted() {
    this.syncFromValue();
  }

  // 监听父组件传递的value变化
  @Watch('value', { immediate: true, deep: true })
  onValueChange() {
    this.syncFromValue();
  }

  // 同步父组件的值到form
  syncFromValue() {
    if (this.value) {
      Object.keys(this.form).forEach(key => {
        if (this.value[key] !== undefined) {
          this.form[key] = this.value[key];
        }
      });
    }
  }

  // chooseTimes(data: any) {
  //   this.form.startTime = data ? this.dateConversion(data[0]) : "";
  //   this.form.endTime = data ? this.dateConversion(data[1]) : "";
  //   // console.log('时间', data[0], this.dateConversion(data), this.searchType);
  // }

  /**
    * 选择有效期
    * @param data 
    */
  chooseTimes(data: any) {
    this.form.createStartTime = data ? this.dateConversion(data[0]) + " 00:00:00" : "";
    this.form.createEndTime = data ? this.dateConversion(data[1]) + " 23:59:59" : "";
    console.log("this.searchType", this.form);

  }
  dateConversion(value: Date) {
    const date = new DateUtil("").getYMDs(value);
    return date;
  }
  // dateConversion(value: Date) {
  //   // const date = new DateUtil("").getYMDHMSs(value);
  //   const date = new DateUtil("").getYMD(value);
  //   return date;
  // }

  /** 字段变化时同步数据 */
  handleFieldChange() {
    this.$emit("input", this.form); // 支持v-model
  }

  /** 触发父级查询 */
  emitSeach() {
    console.log(this.form);
    this.$emit("input-set", this.form);
    this.$emit("input", this.form); // 支持v-model
  }


}
</script>

<style lang="scss" scoped>
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}
</style>
