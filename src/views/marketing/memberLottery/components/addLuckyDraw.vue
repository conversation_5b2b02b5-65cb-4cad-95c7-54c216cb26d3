<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-01 15:47:16
-->
<template>
	<div class="goodForm">

		<el-form :model="formModel" :rules="formRules" ref="formModel" label-width="100px" :disabled="disabled">

			<div class="baseMsg">
				<el-form-item label="优惠券名称" prop="couponName">
					<el-input v-model="formModel.couponName" :maxlength="128" style="width: 550px"
						placeholder="请输入优惠券名称"></el-input>
				</el-form-item>
				<el-form-item label="优惠券面额" prop="promotion">
					<el-input v-model="formModel.promotion" style="width: 550px" maxlength="30"
						placeholder="请输入优惠券面额"></el-input>
				</el-form-item>
				<el-form-item label="显示时间" prop="time">
					<div style="display: flex;align-items: center;justify-content: flex-start;">
						<el-date-picker v-model="formModel.displayStartTime" type="datetime" style="width:200px;"
							prefix-icon="el-icon-alarm-clock" placeholder="请选择开始时间" @change="chooseTime">
						</el-date-picker>
						<i style="margin: 0 10px;font-size: 30px;color: rgb(187, 187, 187);" class="el-icon-minus"></i>
						<el-date-picker v-model="formModel.displayEndTime" type="datetime" style="width:200px;"
							prefix-icon="el-icon-alarm-clock" placeholder="请选择开始时间" @change="chooseTimeEad">
						</el-date-picker>
					</div>
				</el-form-item>
				<el-form-item label="订单满" prop="fullAmount">
					<el-input v-model="formModel.fullAmount" style="width: 150px" maxlength="30"
						placeholder="请输入金额"></el-input>
					<span class="useableTimes__ci">元可使用</span>
				</el-form-item>
				<!-- 到期类型:100->指定时间;101->购买之日起计算; -->
				<el-form-item label="有效期" prop="times">
					<div style="display: flex;align-items: center;justify-content: flex-start;">
						<el-date-picker v-model="formModel.startTime" type="datetime" style="width:200px;"
							prefix-icon="el-icon-alarm-clock" placeholder="请选择日期" @change="chooseStartTime">
						</el-date-picker>
						<i style="margin: 0 10px;font-size: 30px;color: rgb(187, 187, 187);" class="el-icon-minus"></i>
						<el-date-picker v-model="formModel.endTime" type="datetime" style="width:200px;"
							prefix-icon="el-icon-alarm-clock" placeholder="请选择日期" @change="chooseEndTime">
						</el-date-picker>
					</div>
				</el-form-item>
				<el-form-item label="领取次数" prop="receiveTimes">
					<el-input v-model="formModel.receiveTimes" style="width: 550px" maxlength="30"
						placeholder="请输入领取次数"></el-input>
				</el-form-item>
				<el-form-item label="优惠券类型" prop="couponType">
					<el-select v-model="formModel.couponType" placeholder="优惠券类型" @change="changeCouponType">
						<el-option v-for="item in couponTypeList" :key="item.id" :label="item.label"
							:value="item.id"></el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="优惠商品" prop="products" v-if="showGoods">
					<el-button type="primary" plain size="small" @click="addTemPackage">+添加商品</el-button>
					<div class="valueName">
						<el-table :data="formModel.products" ref="inventorySumTable">
							<el-table-column prop="option" label="操作" width="140">
								<template slot-scope="scope">
									<el-button type="text" style="color: red;"
										@click="deletePackageProducts(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
							<el-table-column prop="productName" label="商品名称" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="specs" label="商品规格" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="price" label="实售价" width="180" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</div>

				</el-form-item>

				<el-form-item label="优惠品类" prop="categorys" v-if="showCategorys">
					<el-button type="primary" plain size="small" @click="addCategorys">+添加品类</el-button>
					<div class="valueName">
						<el-table :data="formModel.categorys" ref="inventorySumTable">
							<el-table-column prop="option" label="操作" width="140">
								<template slot-scope="scope">
									<el-button type="text" style="color: red;"
										@click="deleteCategorys(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
							<el-table-column prop="modeName" label="商品专区" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="categoryParentName" label="商品一级分类" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="categoryName" label="商品二级分类" width="180" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</div>
				</el-form-item>

				<el-form-item label="指定发券对象" prop="grantType">
					<el-select v-model="formModel.grantType" placeholder="请选择指定发券对象" @change="changeGrantType">
						<el-option v-for="item in this.grantTypeList" :key="item.id" :label="item.label"
							:value="item.id"></el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="标签客户" prop="accounts" v-if="showTag">
					<el-button type="primary" plain size="small" @click="addTags">+添加标签</el-button>
					<div class="valueName">
						<el-table :data="formModel.accounts" ref="inventorySumTable">
							<el-table-column prop="option" label="操作" width="140">
								<template slot-scope="scope">
									<el-button type="text" style="color: red;"
										@click="deleteAccount(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
							<el-table-column prop="tagName" label="标签名称" width="180" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="accountNum" label="客户数量" width="180" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</div>
				</el-form-item>
				<el-form-item label="会员等级" prop="accounts" v-if="showMember">
					<el-select multiple v-model="formModel.memberLevels" placeholder="会员等级">
						<el-option v-for="item in this.memberLevelList" :key="item.id" :label="item.memberLevel"
							:value="item.id"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="指定客户" prop="accounts" v-if="showAccount">
					<el-button type="primary" plain size="small" @click="addAccounts">+添加客户</el-button>
					<div class="valueName">
						<el-table :data="formModel.accounts" ref="inventorySumTable">
							<el-table-column prop="option" label="操作" width="120">
								<template slot-scope="scope">
									<el-button type="text" style="color: red;"
										@click="deleteAccount(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
							<el-table-column prop="nikeName" label="客户昵称" width="100" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="phone" label="客户电话号码" width="100" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="memberLevel" label="客户会员等级" width="100" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="tagName" label="标签名称" width="100" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</div>
				</el-form-item>
				<ChooseGoods ref="chooseGoods" @handleGoods="handleGoods">
				</ChooseGoods>
				<ChooseCategory ref="chooseCategory" @handleCategorys="handleCategorys">
				</ChooseCategory>
				<ChooseTag ref="chooseTag" @handleTag="handleTag">
				</ChooseTag>
				<ChooseAccount ref="chooseAccount" @handleAccount="handleAccount">
				</ChooseAccount>
				<el-form-item label="备注" prop="remark">
					<el-input v-model="formModel.remark" style="width: 550px" placeholder="填写您的备注内容"></el-input>
				</el-form-item>

				<el-form-item label="样板展示" prop="times">
					<div style="display: flex;align-items: center;justify-content: flex-start;">
						<youhuiCertificate :widePicImg='widePic' :color='color1' @certificateImg="certificateImgs">
						</youhuiCertificate>
						<div class="upload">
							{{ color1 }}
							<el-color-picker v-model="color1"></el-color-picker>
						</div>
					</div>
				</el-form-item>
				<el-form-item label="参与商户" prop="remark" v-if="mainFlag">
					<el-radio v-model="formModel.shopFlag" :label="0"><el-button size="mini">所有商户</el-button></el-radio>
				</el-form-item>
				<el-form-item label="" prop="remark" v-if="mainFlag">
					<el-radio v-model="formModel.shopFlag" :label="1">
						<el-button size="mini" @click="dialogShow"
							style="background:#07CAD5 ;color: #ffffff ;">自选商户</el-button></el-radio>
				</el-form-item>
			</div>
		</el-form>
		<div class="primary__bottom">
			<el-button @click="editSecUnit" v-if="!disabled">取 消</el-button>
			<el-button type="primary" @click="submitForm()" v-if="!disabled">提交</el-button>
			<el-button @click="newly" v-if="disabled">返 回</el-button>
		</div>
		<el-dialog :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
			<el-form ref="form" :model="searchType" label-width="90px">
				<el-row>
					<el-col :span="12">
						<el-form-item label="商家名称">
							<el-input v-model="searchType.name" placeholder="请输入商家名称" style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系人电话">
							<el-input v-model="searchType.phone" placeholder="请输入联系人电话"
								style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="商家类型">
							<el-cascader v-model="name" :props="props" :options="options"
								@change="handleChange"></el-cascader>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系人名称">
							<el-input v-model="searchType.contacts" placeholder="请输入联系人名称"
								style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-button @click="getShopList" type="primary" size="mini" round>搜索</el-button>
					</el-col>
				</el-row>
				<div style="border: 1px solid #ccc;width: 100%;margin: 20px 0;"></div>
				<el-row>
					<el-col :span="24">
						<el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
							:row-key="getRowKeys" @selection-change="handleSelectionChange">
							<el-table-column type="selection" :reserve-selection="true">
							</el-table-column>
							<el-table-column label="序号" type="index">
							</el-table-column>
							<el-table-column width="55">
							</el-table-column>
							<el-table-column label="商家名称" width="160">
								<template slot-scope="scope">{{ scope.row.name }}</template>
							</el-table-column>
							<el-table-column prop="contacts" label="联系人名称" width="120">
							</el-table-column>
							<el-table-column prop="phone" label="联系人电话" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="categoryName" label="商家类型" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</el-col>
				</el-row>
			</el-form>
			<!-- 设置分类 -->
			<div class="listBottom">
				<PageManage :pageSize="searchType.size" :pageNum="searchType.current" :total="total"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"
					style="margin-top: 0px">
				</PageManage>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="primaryButton">确 定</el-button>
			</span>
		</el-dialog>

	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";

@Component({
	components: {
	
	}
})
export default class NewGoodFormOpints extends Vue {
	@Prop({})
	from!: string;
	color1 = ''
	/////////////////////
	// 存放数据的对象
	formModel = { shopFlag: 0, products: [], categorys: [], accounts: [], memberLevels: [] } as formModelType;
	/** 基本信息验证 */
	formRules = {
		name: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
		goodsCode: [{ required: true, message: "请输入商品编码", trigger: "blur" }],
		points: [{ required: true, message: "需要用多少积分兑换本商品", trigger: "blur" }, { required: true, message: "需要用多少现金兑换本商品", trigger: "blur" }],
		amount: [{ required: true, message: "请输入总数量", trigger: "blur" }],
		showCategoryIds: [
			{ required: true, message: "请选择分类", trigger: "blur" }
		]
	};
	tableData = []
	selectionList = []
	commodityCode = "";
	createTime = ''
	showRegion = false;
	shopTicketList = []
	widePic = ''
	dialogVisible = false;
	showGoods = false
	showTag = false
	showMember = false
	showCategorys = false
	showAccount = false
	/** 搜索类型 */
	couponTypeList = [
		{
			id: 0,
			label: "普通券",
		},
		{
			id: 1,
			label: "新人券",
		},
		{
			id: 2,
			label: "商品优惠券",
		},
		{
			id: 3,
			label: "品类优惠券",
		},
		{
			id: 4,
			label: "满返券",
		},
	];
	grantTypeList = [
		{
			id: 0,
			label: '全部客户',
		},
		{
			id: 1,
			label: '标签客户',
		},
		{
			id: 2,
			label: '会员等级',
		},
		{
			id: 3,
			label: '指定客户',
		},
	]
	// searchType : SearchKeyType = {};
	searchType = {
		current: 1,
		size: 10
	} as SearchKeyType;

	name = [];
	options = []
	props = {
		expandTrigger: 'hover',
		value: 'shopsCategoryId',
		label: 'name',
		children: 'shopsCategoryVos'
	}
	total = 0;
	disabled = false;
	partnerList: Array<partnerListType> = []
	multipleSelection = []
	memberLevelList = []

	mainFlag = 0

	mounted() {
		
	}

	
}
</script>

<style lang="scss">
@import "@/assets/styles/goods/index.scss";

.upload {
	display: flex;
	align-items: center;
	flex-direction: column;
	margin-left: 10px;

	.upload-demo {
		margin-top: 10px;
	}
}

.useableTimes__ci {
	margin-left: 10px;
}

.goodForm::-webkit-scrollbar {
	display: none;
}

.w-e-text-container {
	height: 532px !important;
	/*!important是重点，因为原div是行内样式设置的高度300px*/
}

.valueName {
	margin-top: 10px;
	width: 620px;
	padding: 20px 20px;
	border: 1px solid #d7d7d7;
}

.primary__bottom {
	margin-top: 30px;
	text-align: center;

	.el-button {
		width: 100px;
		font-size: 16px;
		font-weight: 700;
	}
}
</style>
