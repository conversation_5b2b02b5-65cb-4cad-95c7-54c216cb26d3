<!--
 * @description: 奖项汇总页面
 * @Author: AI Assistant
 * @Date: 2025-01-11
-->
<template>
  <div class="prize-summary-container">
    <!-- 查询表单 -->
    <PrizeSummaryForm :query="query" @search="handleSearch" @reset="handleReset" />

    <div style="margin-bottom: 20px"></div>
    <!-- 奖项汇总表格 -->
    <PrizeSummaryTable
      :data="tableData"
      :loading="loading"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @view-detail="handleViewDetail"
      :current-page="query.current"
      :page-size="query.size"
      :total="query.total"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import PrizeSummaryForm from "./components/PrizeSummaryForm.vue";
import PrizeSummaryTable from "./components/PrizeSummaryTable.vue";
import { getSummaryPage,getItemPage } from "@/api/marketing/index";

@Component({
  components: {
    PrizeSummaryForm,
    PrizeSummaryTable
  }
})
export default class PrizeSummaryIndex extends Vue {
  
  // 查询参数
  query = {
    current: 1,
    size: 20,
    total: 0,
    mainPrizeName: '', // 活动方案名称
    levelName: '', // 奖项名称
  };
  
  // 表格数据
  tableData = [];
  
  // 加载状态
  loading = false;
  
  created() {
    this.getDataList();
  }
  
  // 获取数据列表
  async getDataList() {
    this.loading = false; // 暂时不用
    try {
      const response = await getSummaryPage(this.query);
      if (response && response.code === 200) {
        this.tableData = response.data.list || [];
        this.query.total = response.data.total || 0;
      }
    } catch (error) {
      console.error('获取奖项汇总失败:', error);
      this.$message.error('获取奖项汇总失败');
    } finally {
      this.loading = false;
    }
  }
  
  // 搜索
  handleSearch(searchData: any) {
    // 合并查询参数
    Object.assign(this.query, searchData, { current: 1 });
    this.getDataList();
  }
  
  // 重置
  handleReset() {
    this.query = {
      ...this.query,
      current: 1,
      mainPrizeName: '',
      levelName: ''
    };
    this.getDataList();
  }
  
  // 查看详情 - 跳转到会员抽奖列表
  handleViewDetail(row: any) {
    this.$router.push({
      path: '/marketing/memberLottery',
      query: {
        levelName: row.levelName,
        mainPrizeName: row.mainPrizeName,
        prizeName: row.prizeName
      }
    });
  }
  
  // 分页大小改变
  handleSizeChange(size: number) {
    this.query.size = size;
    this.query.current = 1;
    this.getDataList();
  }
  
  // 当前页改变
  handleCurrentChange(current: number) {
    this.query.current = current;
    this.getDataList();
  }
}
</script>

<style lang="scss" scoped>
.prize-summary-container {
  padding: 20px;
  
  .m-card {
    margin-top: 20px;
  }
}
</style>
