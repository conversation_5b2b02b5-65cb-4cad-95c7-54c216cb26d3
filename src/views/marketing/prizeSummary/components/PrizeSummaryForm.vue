<!--
 * @description: 奖项汇总查询表单
 * @Author: AI Assistant
 * @Date: 2025-01-11
-->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="120px">
      
      <el-row>
        <el-col :span="10">
          <el-form-item label="活动方案名称">
            <el-input v-model="form.mainPrizeName" placeholder="请输入活动方案名称" clearable></el-input>
          </el-form-item>
        </el-col>
        
        <el-col :span="10">
          <el-form-item label="奖项名称">
            <el-input v-model="form.levelName" placeholder="请输入奖项名称" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item>
        <el-button type="primary" @click="emitSearch">搜索</el-button>
        <el-button @click="emitReset">重置</el-button>
      </el-form-item>
    </el-form>
  </m-card>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";

@Component
export default class PrizeSummaryForm extends Vue {
  
  @Prop({ default: () => ({}) }) query!: any;
  
  form = {
    mainPrizeName: '',
    levelName: ''
  };
  
  mounted() {
    this.initFormFromQuery();
  }
  
  // 监听query变化
  @Watch('query', { immediate: true, deep: true })
  onQueryChange() {
    this.initFormFromQuery();
  }
  
  // 从query初始化form
  initFormFromQuery() {
    if (this.query) {
      this.form.mainPrizeName = this.query.mainPrizeName || '';
      this.form.levelName = this.query.levelName || '';
    }
  }
  
  // 触发搜索
  emitSearch() {
    const searchData = {
      mainPrizeName: this.form.mainPrizeName,
      levelName: this.form.levelName
    };
    this.$emit('search', searchData);
  }
  
  // 触发重置
  emitReset() {
    this.form = {
      mainPrizeName: '',
      levelName: ''
    };
    this.$emit('reset');
  }
}
</script>

<style lang="scss" scoped>
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;
}
</style>
