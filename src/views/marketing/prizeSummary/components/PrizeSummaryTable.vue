<!--
 * @description: 奖项汇总表格
 * @Author: AI Assistant
 * @Date: 2025-01-11
-->
<template>
  <div class="prize-summary-table">
    <el-table 
      :data="data" 
      border 
      style="width: 100%" 
      v-loading="loading"
      :summary-method="getSummaries" show-summary
      element-loading-text="数据加载中...">
      
      <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
      </el-table-column>
      
      <el-table-column label="活动方案名称" min-width="200" prop="mainPrizeName" show-overflow-tooltip>
      </el-table-column>
      
      <el-table-column label="奖项名称" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.levelName }}
        </template>
      </el-table-column>
      <el-table-column label="奖品名称" min-width="250" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.prizeName }}
        </template>
      </el-table-column>
      <el-table-column label="中奖次数" width="120" prop="winningCount" align="center">
        <template slot-scope="scope">
          {{ scope.row.winningCount || 0 }}
        </template>
      </el-table-column>
      
      <el-table-column label="中奖人次" width="120" prop="winningPersonCount" align="center">
        <template slot-scope="scope">
          {{ scope.row.winningPersonCount || 0 }}
        </template>
      </el-table-column>

      <!-- <el-table-column label="总参与人数" width="120" align="center">
        <template slot-scope="scope">
          {{ scope.row.totalParticipantCount || 0 }}
        </template>
      </el-table-column> -->

      <el-table-column label="核销人数" width="120" prop="verifyPersonCount" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.verifyType === 2">
            {{ scope.row.verifyPersonCount || 0 }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleViewDetail(scope.row)">
            详情
          </el-button>
        </template>
      </el-table-column>
      
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";

@Component
export default class PrizeSummaryTable extends Vue {
  
  @Prop({ default: () => [] }) data!: any[];
  @Prop({ default: false }) loading!: boolean;
  @Prop({ default: 1 }) currentPage!: number;
  @Prop({ default: 20 }) pageSize!: number;
  @Prop({ default: 0 }) total!: number;

  getSummaries(param: any) {
    const { columns, data } = param;
    const sums: any[] = [];
    columns.forEach((column: any, index: number) => {
      if (index === 0) {
        sums[index] = '合计';
        return;
      }
      if (column.property === 'winningCount' || column.property === 'winningPersonCount'
      || column.property === 'verifyPersonCount' 
       ) {
        const values = data.map((item: any) => Number(item[column.property]));
        const total = values.reduce((prev: any, curr: any) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          }
          return prev;
        }, 0);
        const totalStr = total.toFixed(0);
        sums[index] = totalStr;
      }
    });
    return sums;
  }

  // 查看详情 - 跳转到会员抽奖列表
  handleViewDetail(row: any) {
    this.$emit('view-detail', row);
  }
  
  // 分页大小改变
  handleSizeChange(size: number) {
    this.$emit('size-change', size);
  }
  
  // 当前页改变
  handleCurrentChange(current: number) {
    this.$emit('current-change', current);
  }
}
</script>

<style lang="scss" scoped>
.prize-summary-table {
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
