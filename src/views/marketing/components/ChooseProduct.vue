<template>
  <el-dialog :visible.sync="dialogVisible" highlight-current-row width="70%" :before-close="handleClose">
    <el-form ref="form" :model="searchType" label-width="90px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="商品名称">
            <el-input v-model="searchType.productName" placeholder="请输入商品名称"
                      style="width: 200px;"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="展示分类">
            <el-select v-model="searchType.showCategoryId" style="width: 200px" placeholder="请选择分类"
                       :popper-append-to-body="false">
              <el-option label="全部" :value="''" />
              <el-option-group v-for="group in temAllShowList" :key="group.showCategoryId"
                               :label="group.name">
                <el-option v-for="item in group.showCategoryVos" :key="item.showCategoryId"
                           :label="item.name" :value="item.showCategoryId" />
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品状态">
            <el-select v-model="searchType.status" placeholder="请选择状态" style="width: 150px" clearable>
              <el-option label="全部" :value="''" />
              <el-option v-for="tag in statusList" :key="tag.value" :label="tag.key" :value="tag.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button @click="searchGoods" type="primary" size="mini" round
                     style="margin-top: 2px;">搜索</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table ref="multipleTable" :data="goodList" tooltip-effect="dark" style="width: 100%"
              :row-key="getRowKeys" @selection-change="handleSelectionChange" @current-change="handleCurrentChange" :highlight-current-row="isSingleSelect">
      <el-table-column v-if="!isSingleSelect" type="selection" :reserve-selection="true">
      </el-table-column>
      <el-table-column v-else type="radio" width="55">
        <template slot-scope="scope">
          <el-radio v-model="selectedProductId" :label="getRowKeys(scope.row)">&nbsp;</el-radio>
        </template>
      </el-table-column>
      <el-table-column label="序号" type="index">
      </el-table-column>
      <el-table-column width="20">
      </el-table-column>
      <el-table-column prop="productName" label="商品名称" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="specs" label="商品规格" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="categoryName" label="商品分类" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="price" label="实售价" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="status" label="商品状态" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.status == '0'">下架</span>
          <span v-if="scope.row.status == '1'">上架</span>
        </template>
      </el-table-column>
    </el-table>
    <PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
                @handleCurrentChange="handlePageCurrentChange" />
    <span slot="footer" class="dialog-footer">
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="primaryButton">确 定</el-button>
        </span>
  </el-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import {
  getAddProductPackageVo,
  queryShowCategoryListAll
} from "@/api/good/goods";

interface ProductItem {
  productId: string | number;
  skuId: string | number;
  [key: string]: any;
}

@Component({
  components: {
    PageManage,
  }
})
export default class ChooseProduct extends Vue {
  @Prop({ default: false }) isSingleSelect!: boolean;

  dialogVisible = false;

  selectedArray: ProductItem[] = [];

  selectedProduct: ProductItem | null = null;

  selectedProductId: string | null = null;

  selectedProducts: ProductItem[] = [];

  temAllShowList: any[] = [];

  searchType = {
    productName: "",
    showCategoryId: "",
    status: "",
  }

  statusList = [
    { key: '上架', value: '1' },
    { key: '下架', value: '0' },
  ]

  current = 1

  size = 10

  total = 0

  products: ProductItem[] = []

  goodList: ProductItem[] = []

  mounted() {
    this.getAllCategoryList();
  }

  // 打开弹窗并恢复选择状态
  openDialog(productId?: string | number, skuId?: string | number, selectedProducts?: any[]) {
    // 如果传入了selectedProducts，直接使用
    if (selectedProducts) {
      this.selectedProducts = selectedProducts;
    }
    // 否则如果传入了productId和skuId，构建selectedProducts
    else if (productId && skuId) {
      this.selectedProducts = [{
        productId: productId,
        skuId: skuId,
        productName: '' // 这里可以后续从商品数据中获取
      }];
    } else {
      this.selectedProducts = [];
    }
    
    // 先查询商品数据，然后打开弹窗
    this.getGoodsList();
  }

  // 设置表格选中状态
  setTableSelection() {
    if (this.selectedProducts.length === 0) return;
    
    this.$nextTick(() => {
      if (this.isSingleSelect) {
        // 单选模式
        if (this.selectedProducts.length > 0) {
          const selectedProduct = this.selectedProducts[0];
          this.selectedProduct = selectedProduct;
          this.selectedProductId = this.getRowKeys(selectedProduct);
        }
      } else {
        // 多选模式
        this.selectedArray = [...this.selectedProducts];
        const table = this.$refs.multipleTable as any;
        if (table) {
          this.selectedProducts.forEach(product => {
            const rowKey = this.getRowKeys(product);
            const row = this.goodList.find(item => this.getRowKeys(item) === rowKey);
            if (row) {
              table.toggleRowSelection(row, true);
            }
          });
        }
      }
    });
  }

  async getAllCategoryList() {
    try {
      const { data } = await queryShowCategoryListAll({});
      this.temAllShowList = JSON.parse(JSON.stringify(data)) || [];
    } catch (error) {
      console.error('获取分类列表失败:', error);
    }
  }

  handleClose(done: () => void) {
    this.selectedArray = [];
    this.selectedProduct = null;
    this.selectedProductId = null;
    if (!this.isSingleSelect) {
      (this.$refs.multipleTable as any).clearSelection();
    }
    done();
  }

  searchGoods() {
    this.current = 1;
    this.size = 10;
    this.getGoodsList();
  }

  getGoodsList() {
    let params: any = { ...this.searchType };
    params.current = this.current;
    params.size = this.size;

    let dataList = this.products;
    let skuIds: (string | number)[] = [];
    if (dataList != null && dataList.length > 0) {
      dataList.forEach(element => {
        skuIds.push(element.skuId);
      });
    }
    params.skuIds = skuIds;

    getAddProductPackageVo(params).then((res) => {
      this.goodList = res.data.list;
      this.total = res.data.total;
      this.dialogVisible = true;
      // 设置表格选中状态
      this.setTableSelection();
    }).catch((err) => {
      this.$message.error(err);
    });
  }

  getRowKeys(row: ProductItem) {
    return row.productId + '-' + row.skuId; //唯一性
  }

  // 多选模式下的选择变更
  handleSelectionChange(val: ProductItem[]) {
    if (!this.isSingleSelect) {
      this.selectedArray = val;
    }
  }

  // 单选模式下的选择变更
  handleCurrentChange(currentRow: ProductItem | null) {
    if (this.isSingleSelect && currentRow) {
      this.selectedProduct = currentRow;
      this.selectedProductId = this.getRowKeys(currentRow);
    }
  }

  handleSizeChange(val: number) {
    this.size = val;
    this.getGoodsList();
  }

  handlePageCurrentChange(val: number) {
    this.current = val;
    this.getGoodsList();
  }

  close() {
    this.selectedArray = [];
    this.selectedProduct = null;
    this.selectedProductId = null;
    this.dialogVisible = false;
    if (!this.isSingleSelect) {
      (this.$refs.multipleTable as any).clearSelection();
    }
  }

  primaryButton() {
    if (this.isSingleSelect) {
      if (!this.selectedProduct) {
        this.$message.warning('请选择一个商品');
        return;
      }
      this.dialogVisible = false;
      this.$emit("handleProduct", [this.selectedProduct]);
      this.selectedProduct = null;
      this.selectedProductId = null;
    } else {
      if (this.selectedArray.length === 0) {
        this.$message.warning('请至少选择一个商品');
        return;
      }
      this.dialogVisible = false;
      this.products = this.products.concat(this.selectedArray);
      this.$emit("handleProduct", this.products);
      this.selectedArray = [];
      this.products = [];
      (this.$refs.multipleTable as any).clearSelection();
    }
  }
}
</script>

<style lang="scss">
</style>
