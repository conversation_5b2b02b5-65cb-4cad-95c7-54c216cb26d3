<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 10:45:18
-->
<template>
  <div>
    <MarketingFrom v-model="query" slot="form" @input-set="inputSet" :query.sync="query" />

<!--    <m-container :pagination-visible="false" :current.sync="query.current" :size.sync="query.size"
                 :total.sync="query.total" >
      &lt;!&ndash;      <MarketingFrom v-model="query" slot="form" @input-set="inputSet" :query.sync="query" />&ndash;&gt;
      <template slot="content">

      </template>
    </m-container>-->
    <el-tabs v-model="query.status" @tab-click="handleTabClick">
      <el-tab-pane label="生效中" name="104"> </el-tab-pane>
      <el-tab-pane label="未生效" name="101"> </el-tab-pane>
      <el-tab-pane label="已失效" name="300"> </el-tab-pane>
      <el-tab-pane label="草稿" name="0"></el-tab-pane>
      <el-tab-pane label="停止" name="400"> </el-tab-pane>
      <el-tab-pane label="全部" name="-1" ></el-tab-pane>
    </el-tabs>
    <div class="topLine">
      <div class="topLine__left">
        <SetClassify  @table-function="tableFunction" @sort-change="handleSortChange" ref="setClass" style="margin-left: 10px;" :is-item="false"
                      :is-value="false">设置分类</SetClassify>
      </div>
    </div>
    <MarketingTable @table-function="tableFunction" :data="dataList" :query.sync="query" @input-set="inputSet"
                    :checked-item.sync="checkedItem" :controlOption="getControlOption(query.orderStatus)" />

    <!-- 审核弹窗 -->
    <el-dialog title="审核营销活动" :visible.sync="approvalFlag" width="30%">
      <el-form :model="approvalForm" ref="approvalForm" label-width="100px">
        <el-form-item label="审核状态" prop="auditStatus" :rules="[{ required: true, message: '请选择审核状态', trigger: 'change' }]">
          <el-select v-model="approvalForm.auditStatus" placeholder="请选择审核状态" style="width: 100%">
            <el-option v-for="item in auditOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核说明" prop="auditReason" :rules="auditReasonRules">
          <el-input type="textarea" :rows="3" placeholder="请输入审核说明" v-model="approvalForm.auditReason" maxlength="200" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeAuditDialog">取 消</el-button>
        <el-button type="primary" @click="approvalHandler">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import MarketingFrom from "./components/MarketingFrom.vue";
import MarketingTable from "./components/MarketingTable.vue";
import SetClassify from "./components/SetClassify.vue";
import { filterEmpty } from "./common/order";
import { getPage, stop, getDetail, audit,copy ,deleteSalePrize} from "@/api/marketing/index"
import PageManage from "@/components/PageManage.vue";
import { copyRewardScheme } from "@/api/reward/reward";


Component.registerHooks(["beforeRouteEnter", "beforeRouteUpdate"]);

@Component({
  components: {
    PageManage,
    MarketingFrom,
    MarketingTable,
    SetClassify,
  },
})
export default class DeliveryOrder extends Vue {


  checkedItem = null

  detailVisible = false;

  detailType = "1";

  // 审核相关属性
  approvalFlag = false;
  approvalForm = {
    ids: [] as number[],
    auditStatus: '',
    auditReason: ''
  };
  auditOptions = [
    { label: '审核通过', value: 101 },
    { label: '拒绝审核', value: 200 }
  ];

  created() {
    this.getPage(this.query);
  }



  //-----------------------------------------------
  query: any = {
    current: 1,
    status: "104",
    auditStatus: "",
    size: 20,
    total: 0,
    createUserName: '',
    mobile: '',
    startTime: '',
    endTime: '',
    sortType: '', // 排序类型
    sortOrder: '', // 排序方向
  };

  dataList = [];



  getPage(param: any) {
    getPage(param).then(res => {
      console.log("res111111=", res.data.list);
      this.dataList = res.data.list;
      this.query.total = res.data.total;
    }).catch(err => {
      this.$message.error(err)
    })
  }

  handleTabClick({ name: orderStatus }: { name: string }) {
    if (orderStatus != "-1") {
      Object.assign(this.query,  {
        status: orderStatus,
        current: 1,
      });
    } else {
      Object.assign(this.query, {
        status: null,
        current: 1,
      });
    }
    // 触发数据重新加载
    this.inputSet(this.query);
  }

  // 处理排序变化
  handleSortChange(sortData: any) {
    this.query.sortType = sortData.sortType;
    this.query.sortOrder = sortData.sortOrder;
    this.query.current = 1; // 重置到第一页
    this.getPage(this.query);
  }

  closeDetail() {
    console.log("closeDetail=");
  }
  inputSet(form: any) {
    console.log("inputSet=", form);
     this.query = Object.assign( this.query, filterEmpty({ ...form }));
     this.getPage(this.query );
  }


  //-----------------------------------------------

  getControlOption(orderStatus: any) {
    console.log("handleTabClick=");
  }

  tableFunction(name: string, data: any, isLogistics: boolean) {
    console.log("name", name, "data", data);
    switch (name) {
      case "add":
        return this.addLuckyDraw(data);
      case "reSubmit":
        return this.editLuckyDraw(data);
      case "edit":
        return this.editLuckyDraw(data);
      case "audit":
        return this.auditActivity(data);
      case "stop":
        return this.stopActivity(data);
      case "detail":
        return this.detailLuckyDraw(data);
      case "batchCopy":
        return this.batchCopy(data);
      case "batchAudit":
        return this.batchAudit(data);
      case "delete":
        return this.deleteActivity(data);
    }
  }

  // 新增抽奖活动
  addLuckyDraw(data: any) {
    this.$router.push("/marketing/marketingList/addLuckyDraw",data)
  }

  // 编辑抽奖活动
  editLuckyDraw(data: any) {
    this.$router.push({
      path: "/marketing/marketingList/addLuckyDraw",
      query: {
        id: data.id
      }
    });
  }

  // 查看抽奖活动详情
  detailLuckyDraw(data: any) {
    this.$router.push({
      path: "/marketing/marketingList/addLuckyDraw",
      query: {
        id: data.id,
        isDisable: '1'
      }
    });
  }



  // 审核活动
  auditActivity(data: any) {
    if (!data || !data.id) {
      this.$message.error('缺少活动信息');
      return;
    }
    if (!data || data.status!==101) {
      this.$message.error('该活动状态不能审核');
      return;
    }

    this.resetApprovalForm();
    this.approvalForm.ids = [data.id];
    this.approvalFlag = true;
  }

  // 批量审核
  batchAudit() {
    if (!this.checkedItem || this.checkedItem.length === 0) {
      this.$message.error('请选择要审核的活动');
      return;
    }
    let canAudit = true;
    this.checkedItem.forEach((item: any) => {
      if (item.status != 101) {
        this.$message.error('包含活动状态不能审核的活动，无法批量审核');
        canAudit=false;
        return;
      }
    });
    if (canAudit) {
      this.resetApprovalForm();
      this.approvalForm.ids = this.checkedItem.map((item: any) => item.id);
      this.approvalFlag = true;
    }
  }
  // 删除活动
  deleteActivity(data: any) {
    this.$confirm("确定删除该活动吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      deleteSalePrize(data.id).then(() => {
        this.getPage(this.query);
        this.$message.success("删除成功");
      }).catch(err => {
        this.$message.error(err)
      });
    }).catch(

    )
  }
  // 停用活动
  stopActivity(data: any) {
    this.$confirm("确定停用该活动吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      stop(data.id).then(() => {
        this.getPage(this.query);
        this.$message.success("停用成功");
      }).catch(err => {
        this.$message.error(err)
      });
    }).catch(

    )
  }

  batchCopy() {
    if (!this.checkedItem || this.checkedItem.length === 0) {
      // 请选择要审核的提现申请
      this.$alert('请选择要复制的活动方案', '复制活动方案', {
        confirmButtonText: '确定'
      });
    } else {
      this.$confirm(
        `是否复制活动方案!`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        },
      ).then(() => {
        /*    var ids = "";
            this.checkedItem.forEach((item, index) => {
              if (ids.length > 0) {
                ids += ','
              }
              ids += item.id
            })*/
        let params = this.checkedItem.map(item => {
          return item.id
        });
        copy(params).then(res => {
          this.$message.success("复制成功");
          this.getPage(this.query);
        }).catch(err => {
          this.$message.error(err || "网络错误");
        });
      });



    }
  }
  /**
   * 查看详情
   */
  async handleSeeDetail(orderData: DeliveryOrderList, isLogistics: boolean) {
    console.log("orderData", orderData);
    console.log("isLogistics", isLogistics);

    //
  }

  // 审核处理
  async approvalHandler() {
    try {
      // 表单验证
      const valid = await this.validateApprovalForm();
      if (!valid) {
        return;
      }

      const params = {
        ids: this.approvalForm.ids,
        auditStatus: this.approvalForm.auditStatus,
        auditReason: this.approvalForm.auditReason
      };

      const response = await audit(params);

      if (response && response.code === 200) {
        const statusText = this.approvalForm.auditStatus == 101 ? '审核通过' : '审核拒绝';
        this.$message.success(`${statusText}成功`);

        // 关闭弹窗
        this.closeAuditDialog();

        // 刷新列表
        this.getPage(this.query);
      } else {
        const errorMessage = response.msg ;
        this.$message.error(errorMessage);
      }
    } catch (error) {
      console.error('审核失败:', error);
      this.$message.error(error);
    }
  }

  // 验证审核表单
  async validateApprovalForm(): Promise<boolean> {
    if (!this.approvalForm.auditStatus) {
      this.$message.error('请选择审核状态');
      return false;
    }

    if (this.approvalForm.auditStatus == 200 && (!this.approvalForm.auditReason || this.approvalForm.auditReason.trim() === '')) {
      this.$message.error('拒绝审核时，审核原因不能为空');
      return false;
    }

    return true;
  }

  // 关闭审核弹窗
  closeAuditDialog() {
    this.approvalFlag = false;
    this.resetApprovalForm();
  }

  // 重置审核表单
  resetApprovalForm() {
    this.approvalForm = {
      ids: [],
      auditStatus: '',
      auditReason: ''
    };
  }

  // 审核原因验证规则
  get auditReasonRules() {
    return [
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (this.approvalForm.auditStatus == 200 && (!value || value.trim() === '')) {
            callback(new Error('拒绝审核时，审核原因不能为空'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ];
  }


}
</script>

<style lang="scss" scoped>
.container--content {
  padding-bottom: 0 !important;
}

.topLine {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  &__left {
    display: flex;
  }

  &__right {
    width: 450px;
    display: flex;
    justify-content: space-around;
  }
}
</style>
