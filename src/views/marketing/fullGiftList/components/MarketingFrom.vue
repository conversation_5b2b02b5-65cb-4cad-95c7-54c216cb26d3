<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 17:16:33
  2025-7-16


 搜索页面
-->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="90px">

      <el-row>
        <el-col :span="6">
          <el-form-item label="方案名称">
            <el-input v-model="form.name" placeholder="请输入方案名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="有效期">
            <el-date-picker v-model="form.date" type="daterange" range-separator="-" start-placeholder="开始时间"
              end-placeholder="结束时间" style="width: 100%;" @change="chooseTimes">
            </el-date-picker>
          </el-form-item>
        </el-col>
        

      </el-row>

    
      <el-form-item>
        <el-button type="primary" @click="emitSeach">搜索</el-button>
      </el-form-item>
    </el-form>

  </m-card>
</template>

<script lang="ts">
/* eslint-disable indent */
import { Vue, Component, Watch } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import DateUtil from "@/store/modules/date";

/** 订单顶部查询表单 */
@Component({
  components: {
    PageManage
  }
})
export default class OrderFrom extends Vue {
  form = {
    startTime: '',
    endTime: '',

    current: 1,
    size: 10,
    total: 0,

    name: '',
    status: '',
  };

  approvalStatusList = [
    {
      value: "",
      label: "全部"
    },
    {
      value: "100",
      label: "待审核"
    },
    {
      value: "101",
      label: "已审核"
    },
    {
      value: "200",
      label: "审核不通过"
    },
  ]
  statusList = [
    {
      value: "",
      label: "全部"
    },
    {
      value: '0',
      label: "草稿"
    },
    {
      value: 100,
      label: "待审核"
    },
    {
      value: 101,
      label: "审核通过"
    },
    {
      value: 300,
      label: "失效"
    },
    {
      value: 400,
      label: "停止"
    }
  ];

  // 奖品类型选项
  prizeTypeList = [
    {
      value: "",
      label: "全部"
    },
    {
      value: 1,
      label: "商城商品"
    },
    {
      value: 2,
      label: "优惠券"
    }
  ];

  // 奖品发放方式选项
  verifyTypeList = [
    {
      value: "",
      label: "全部"
    },
    {
      value: 1,
      label: "线上邮寄"
    },
    {
      value: 2,
      label: "线下核销"
    }
  ];

  mounted() {

  }


  /**
    * 选择有效期
    * @param data 
    */
  chooseTimes(data: any) {

    this.form.startTime = data ? this.dateConversion(data[0]) + " 00:00:00" : "";
    this.form.endTime = data ? this.dateConversion(data[1]) + " 23:59:59" : "";
    console.log("this.form", this.form);
  }
  dateConversion(value: Date) {
    const date = new DateUtil("").getYMDs(value);
    return date;
  }


  /** 触发父级查询 */
  emitSeach() {
    console.log(this.form);
    this.$emit("input-set", this.form);
  }


}
</script>

<style lang="scss" scoped>
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}
</style>
