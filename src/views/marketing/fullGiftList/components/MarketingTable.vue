<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 14:08:55
 2025.5.28有改动的页面
-->
<template>
  <!-- 设置分类 -->
  <div>
    <template>
      <el-table ref="multipleTable" :data="data" border tooltip-effect="dark" style="width: 100%" height="670"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" fixed="left">
        </el-table-column>

        <el-table-column label="方案名称" min-width="200" prop="name">
        </el-table-column>

       
            
        <el-table-column label="有效期" min-width="260" prop="content">
          <template slot-scope="{row}">
            {{ row.startTime }} - {{ row.endTime }}
          </template>
        </el-table-column>

        <el-table-column label="经手人" min-width="100" prop="auditPlatformUserName">
        </el-table-column>
        <el-table-column label="审核时间" min-width="160" prop="auditTime">
         
        </el-table-column>

        <el-table-column label="状态" min-width="100" prop="status">
          <template slot-scope="{row}">
            <el-tag type="warning" v-if="row.status == 0">草稿</el-tag>
            <el-tag type="warning" v-if="row.status == 100">待审核</el-tag>
            <el-tag type="success" v-if="row.status == 101">审核通过</el-tag>
            <el-tag type="danger" v-if="row.status == 200">审核不通过</el-tag>
            <el-tag type="danger" v-if="row.status == 400">停止</el-tag>
          </template>
        </el-table-column>

        <!-- <el-table-column label="审核状态" min-width="100" prop="status">
          <template slot-scope="{row}">
            <el-tag type="warning" v-if="row.auditStatus == 100">待审核</el-tag>
            <el-tag type="success" v-if="row.auditStatus == 101">审核通过</el-tag>
            <el-tag type="danger" v-if="row.auditStatus == 200">审核不通过</el-tag>
          </template>
        </el-table-column> -->

        
        <el-table-column label="备注信息" min-width="220" prop="auditReason">
        </el-table-column>


        <el-table-column fixed="right" label="操作" width="160">
          <template slot-scope="scope">

            <el-button-group class="fix">
              <el-button plain @click="emitFun('detail', scope.row)" type="primary">详情</el-button>


              <el-button plain class="dropdown__fix more" type="primary">
                <el-dropdown size="mini" trigger="hover">
                  <span class="dropdown__fix--more">...</span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-if="scope.row.status == 0"
                      @click.native="emitFun('edit', scope.row)">编辑</el-dropdown-item>                      

                    <el-dropdown-item v-if="scope.row.status == 101"
                      @click.native="emitFun('stop', scope.row)">停用</el-dropdown-item>

                    <el-dropdown-item v-if="scope.row.status == 100"
                      @click.native="emitFun('audit', scope.row)">审核</el-dropdown-item>

                    <el-dropdown-item v-if="scope.row.status == 0"
                      @click.native="emitFun('remove', scope.row)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </el-button>

            </el-button-group>

          </template>
        </el-table-column>
      </el-table>

    </template>

    <div class="order__control--bottom fixed" style="width: 100%">

      <el-pagination small layout="total,  prev, pager, next,  sizes" :current-page.sync="form.current"
        :size.sync="form.size" :page-size.sync="form.size" :page-sizes="[10, 20, 50, 100]" :total.sync="form.total"
        @current-change="handleCurrentChange" @size-change="handleSizeChange">
      </el-pagination>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import {
  getDeliverOrderStatusName,
  getDeliveryTypeName,
  isClose,
  strFilter,
} from "../common/order";
import { getAfterStatusName, getAfterName } from "../common/afterSale";

@Component({ filters: { strFilter } })
export default class OderTable extends Vue {
  /** 表格数据 */
  @Prop({ type: Array })
  data!: any[];

  /** 表格内查询按钮 */
  @Prop()
  controlOption!: any[];

  /** 查询条件 */
  @Prop()
  query!: any;

  /** 父级已选条码 */
  @Prop()
  checkedItem!: Array<{ orderId: string }>;
  multipleSelection: [];
  /** 本地form 主要用户页码切换 */
  get form() {
    return this.query;
  }


  /** 已选表格选项 */
  get tableCheckedItem() {
    return this.checkedItem || [];
  }

  set tableCheckedItem(v) {
    this.$emit("update:checked-item", v);
  }
  /**
   * @method handleSizeChange
   * @description 每页 条
   */
  handleSizeChange(val: number) {
    this.form.size = val;
    this.$emit("input-set", this.form);
  }

  /**
   * @method handleCurrentChange
   * @description 当前页
   */
  handleCurrentChange(val: number) {
    this.form.current = val;
    console.log('当前页', val);
    this.$emit("input-set", this.form);
  }


  /** 触发父级方法 */
  emitFun(name: string, data?: any, status?: boolean) {
    // 阻止未选中元素的批量操作
    // if (!data && !this.checkedItem.length && name != 'deliverMessage') {
    // 	return this.$message.info("请先选择条目");
    // }
    this.$emit("table-function", name, data, status);
  }


  /**
   * 多选
   */
  handleSelectionChange(val: any) {
    this.multipleSelection = []
    val.forEach((item: any) => {
      this.multipleSelection.push(item.id);
    });
    this.$emit("update:checked-item", val);
    // console.log("fdf=",this.multipleSelection);
    // console.log("val=",val);

  }

}
</script>

<style lang="scss">
.header__tag {
  border-radius: 0;
  margin-right: 10px;
}

.fixed {
  @include flex(space-between);
  position: fixed;
  bottom: 10px;
  width: 990px !important;
  z-index: 10;
  background: #fff;
  padding: 10px 0;
}
</style>

<style lang="scss">
@import "@/assets/styles/mixins/mixins.scss";

@include b(setClassify) {
  display: flex;
  line-height: 30px;
  // border: 1px solid #dcdfe6;
  overflow: hidden;
  margin-left: 20px;
  border-radius: 50px;
  background-color: #409EFF;
  color: #ffffff;
  border: 1px solid #409EFF;
  position: relative;

  @include e(title) {
    text-align: center;
    padding: 0 20px;
    cursor: pointer;
  }

  @include e(icon) {
    width: 40px;
    text-align: center;
    cursor: pointer;
    vertical-align: middle;
    color: #ffffff;
    font-weight: bold;
  }
}
</style>