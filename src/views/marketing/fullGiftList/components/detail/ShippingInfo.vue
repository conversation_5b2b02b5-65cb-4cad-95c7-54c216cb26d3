<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 14:05:11
发货信息页面
2025.5.28有改动的页面
-->
<template>
  <div class="ShippingInfo">
    <!-- <el-row :gutter="10">
      <el-col :span="24" style="margin-bottom: 15px;">
        <b>发货信息</b>
      </el-col>
      <el-col :span="7">收货人：{{ '煤炭' }}</el-col>
      <el-col :span="7">电话号码：{{ '13755448624' }}</el-col>
      <el-col :span="7">发货时间：{{ '2025.5.28' }}</el-col>
    </el-row>
    <el-row>
      <el-col :span="7">发货状态：{{ '部分发货' }}</el-col>
      <el-col :span="17">收货地址：{{ '广西壮族自治区南宁市西乡塘区中华路82号' }}</el-col>
    </el-row> -->

    <el-row>
      <el-table :data="detail" border style="width: 100%">
        <el-table-column prop="receiverName" label="收货人" width="180" align="center" fixed="left">
        </el-table-column>
        <el-table-column prop="receiverPhone" label="电话号码" width="180" align="center">
        </el-table-column>
        <el-table-column prop="address" label="收货地址" width="180" align="center">
          <template slot-scope="{row}">
            {{ row.receiverProvince }}
            {{ row.receiverCity }}
            {{ row.receiverRegion }}
            {{ row.receiverDetailAddress }}
          </template>
        </el-table-column>
        <el-table-column prop="deliveryTimeStr2" label="发货时间" width="180" align="center">
        </el-table-column>
        <el-table-column prop="address" label="发货状态" width="180" align="center">
        </el-table-column>
        <el-table-column prop="address" label="发货内容" width="300" align="center" fixed="right">
          <template slot-scope="{row}">
            <div v-for="(item, i) in row.itemList" :key="i">
              <div class="table__goods">
                <div class="table__goods--image">
                  <img v-if="item.productPic" :src="item.productPic"  :alt="item.productName"/>
                  <img v-else src="@/assets/images/certificateImg.png" />
                </div>
                <div class="table__goods--info" style="text-align: left;">
                  <div class="goods--name">
                    {{ item.productName }}
                  </div>
                  <!-- <div class="goods--specs" style="text-align: left;">
                    <span class="l">{{ item.productQuantity }}</span>
                  </div> -->
                  <!-- <div class="goods--specs" style="color: orange; text-align: left;">
                    <span class="l">
                      55
                    </span>
                  </div> -->
                  <div class="goods--price">
                    <!-- <span> ￥{{ 10 }} </span> -->
                    <span class="specs">
                      <span class="r">×{{ item.productQuantity }}</span>
                    </span>
                  </div>
                </div>
              </div>
              <el-divider v-if="i != 1" style="margin: 10px 0;"></el-divider>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";

@Component({
  components: {

  },
})
export default class Info extends Vue {
  @Prop({
    default() {
      return {};
    },
  })
  detail!: any;

  tableData = [{
    date: '2016-05-02',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1518 弄'
  }, {
    date: '2016-05-04',
    name: '王小虎',
    address: '上海市普陀区金沙江路 1517 弄'
  },]

}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/order/detail.scss";

.ShippingInfo {
  .el-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-col {
    // border-radius: 4px;
  }
}
</style>
