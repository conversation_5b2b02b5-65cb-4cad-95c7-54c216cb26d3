<!--
 * @description: 奖项列表组件
-->
<template>
	<div class="prize-item-list">
		<el-table :data="prizeItemList" ref="prizeItemTable" border style="width: 100%" max-height="580px">
			<el-table-column :align="'center'" type="index" width="50">
			</el-table-column>
			<el-table-column :align="'center'" label="" width="80">
				<template slot-scope="scope">
					<el-button @click="addPrizeItemRow(scope.$index, scope.row)" type="text" size="medium"
						icon="el-icon-plus"></el-button>
					<el-button type="text" size="medium" @click.prevent="removePrizeItemRow(scope.$index)"
						icon="el-icon-minus"></el-button>
				</template>
			</el-table-column>
			<el-table-column label="奖项名称" width="150">
				<template slot-scope="scope">
					<el-input v-model="scope.row.levelName" placeholder="请输入奖项名称" :disabled="disabled"></el-input>
				</template>
			</el-table-column>
			<el-table-column label="排序" width="100">
				<template slot-scope="scope">
					<el-input-number v-model="scope.row.seqBy" :min="1" :max="999" :controls="false"
						class="input_number" :disabled="disabled"></el-input-number>
				</template>
			</el-table-column>
			<el-table-column label="是否奖品" width="120">
				<template slot-scope="scope">
					<el-select v-model="scope.row.prizeFlag" placeholder="请选择" :disabled="disabled">
						<el-option label="否" :value="0"></el-option>
						<el-option label="是" :value="1"></el-option>
					</el-select>
				</template>
			</el-table-column>
			<el-table-column label="是否间隔奖项" width="140">
				<template slot-scope="scope">
					<el-select v-model="scope.row.spacingFlag" placeholder="请选择" :disabled="disabled">
						<el-option label="否" :value="0"></el-option>
						<el-option label="是" :value="1"></el-option>
					</el-select>
				</template>
			</el-table-column>
			<el-table-column label="奖品类型" width="120">
				<template slot-scope="scope">
					<el-select v-model="scope.row.prizeType" placeholder="请选择" :disabled="disabled" v-if="scope.row.prizeFlag === 1">
						<el-option v-for="item in prizeItemTypeOptions" :key="item.value" :label="item.label"
							:value="item.value"></el-option>
					</el-select>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<el-table-column label="奖品名称" width="150">
				<template slot-scope="scope">
					<div v-if="scope.row.prizeFlag === 1">
						<!-- 商品类型时显示选择按钮 -->
						<el-button
							v-if="scope.row.prizeType === 1"
							type="text"
							@click="selectProduct(scope.$index)"
							:disabled="disabled"
							class="product-select-btn">
							{{ scope.row.prizeName || '请选择商品' }}
						</el-button>
						<!-- 优惠券类型时显示选择按钮 -->
						<el-button
							v-else-if="scope.row.prizeType === 2"
							type="text"
							@click="selectCoupon(scope.$index)"
							:disabled="disabled"
							class="product-select-btn">
							{{ scope.row.prizeName || '请选择优惠券' }}
						</el-button>
						<!-- 其他类型时显示输入框 -->
						<el-input
							v-else
							v-model="scope.row.prizeName"
							placeholder="请输入奖品名称"
							:disabled="disabled">
						</el-input>
					</div>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<el-table-column label="中奖概率(%)" width="120">
				<template slot-scope="scope">
					<el-input-number v-model="scope.row.winningRate" :min="0" :max="100" :precision="2"
						:controls="false" class="input_number" :disabled="disabled"></el-input-number>
				</template>
			</el-table-column>
			<el-table-column label="发放方式" width="120">
				<template slot-scope="scope">
					<el-select v-model="scope.row.verifyType" placeholder="请选择" :disabled="disabled" v-if="scope.row.prizeFlag === 1">
						<el-option v-for="item in verifyTypeOptions" :key="item.value" :label="item.label"
							:value="item.value"></el-option>
					</el-select>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<el-table-column label="中奖人次" width="120">
				<template slot-scope="scope">
					<el-input-number v-model="scope.row.winningPersonTimes" :min="0" :controls="false"
						class="input_number" :disabled="disabled"></el-input-number>
				</template>
			</el-table-column>
<!--			<el-table-column label="奖品图片" width="120">
				<template slot-scope="scope">
					<el-upload
						class="avatar-uploader-small"
						action="#"
						:show-file-list="false"
						:before-upload="beforeUploadPrize"
						:http-request="(params) => uploadPrizeImageInline(params, scope.$index)"
						v-if="scope.row.prizeFlag === 1">
						<img v-if="scope.row.pic" :src="scope.row.pic" class="avatar-small">
						<i v-else class="el-icon-plus avatar-uploader-icon-small"></i>
					</el-upload>
					<span v-else>-</span>
				</template>
			</el-table-column>-->
		</el-table>

		<!-- 商品选择对话框 -->
		<ChooseProduct ref="chooseProduct" @handleProduct="handleProductSelect" />

		<!-- 优惠券选择对话框 -->
		<ChooseCoupon ref="chooseCoupon" @handleCoupon="handleCouponSelect" />
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import {
	ShopSalePrizeItemVo,
	prizeItemTypeOptions,
	verifyTypeOptions,
	decimalToPercent
} from "./prizeTypes";
import ChooseProduct from "./ChooseProduct.vue";
import ChooseCoupon from "./ChooseCoupon.vue";

@Component({
	components: {
		ChooseProduct,
		ChooseCoupon
	}
})
export default class PrizeItemList extends Vue {

	@Prop() initData!: ShopSalePrizeItemVo[];
	@Prop() disabled!: boolean;

	// 内部数据
	prizeItemList: ShopSalePrizeItemVo[] = [];
	prizeItemKey = 0;
	currentSelectingIndex = -1; // 当前正在选择商品/优惠券的行索引

	// 选项数据
	prizeItemTypeOptions = prizeItemTypeOptions;
	verifyTypeOptions = verifyTypeOptions;

	mounted() {
		this.initPrizeItemList();
	}

	// 获取奖项列表数据
	getPrizeItemList(): ShopSalePrizeItemVo[] {
		return this.prizeItemList.map(item => ({
			...item,
			winningRate: item.winningRate !== undefined && item.winningRate !== null
				? item.winningRate / 100
				: 0
		}));
	}

	// 设置奖项列表数据
	setPrizeItemList(data: ShopSalePrizeItemVo[]) {
		if (data && data.length > 0) {
			// 将后端的小数转换为百分比显示
			this.prizeItemList = data.map(item => ({
				...item,
				winningRate: item.winningRate !== undefined && item.winningRate !== null
					? decimalToPercent(item.winningRate)
					: 0
			}));
		} else {
			this.prizeItemList = [];
			this.initPrizeItemList();
		}
	}

	// 初始化奖项列表
	initPrizeItemList() {
		// 如果有初始数据，使用初始数据
		if (this.initData && this.initData.length > 0) {
			this.setPrizeItemList(this.initData);
			return;
		}

		// 否则创建默认的一行数据
		this.prizeItemKey = 1;
		this.prizeItemList = [{
			arrKey: this.prizeItemKey,
			levelName: '',
			seqBy: 1,
			spacingFlag: 0,
			prizeFlag: 0,
			verifyType: 1,
			prizeType: 1,
			winningRate: 0,
			winningPersonTimes: 0,
			prizeName: '',
			pic: ''
		}];
	}

	// 添加奖项行
	addPrizeItemRow(index: number, row: any) {
		this.prizeItemKey++;
		const newPrizeItem = {
			arrKey: this.prizeItemKey,
			levelName: '',
			seqBy: 1,
			spacingFlag: 0,
			prizeFlag: 0,
			verifyType: 1,
			prizeType: 1,
			winningRate: 0,
			winningPersonTimes: 0,
			prizeName: '',
			pic: ''
		};

		this.prizeItemList.push(newPrizeItem);
	}

	// 删除奖项行
	removePrizeItemRow(index: number) {
		// 当只有一行数据的时候不允许删除
		if (this.prizeItemList.length <= 1) {
			this.$message.warning('至少需要保留一个奖项');
			return;
		}

		this.prizeItemList.splice(index, 1);

		// 重新调整后续行的 arrKey
		for (let i = index; i < this.prizeItemList.length; i++) {
			if (this.prizeItemList[i].arrKey) {
				this.prizeItemList[i].arrKey--;
			}
		}
		this.prizeItemKey--;
	}

/*
	// 图片上传相关方法
	beforeUploadPrize(file: File) {
		const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
		const isLt2M = file.size / 1024 / 1024 < 2;

		if (!isJPG) {
			this.$message.error('上传头像图片只能是 JPG/PNG 格式!');
		}
		if (!isLt2M) {
			this.$message.error('上传头像图片大小不能超过 2MB!');
		}
		return isJPG && isLt2M;
	}

	// 内联图片上传方法
	uploadPrizeImageInline(params: any, index: number) {
		// 这里应该调用实际的上传接口
		// 暂时模拟上传成功
		setTimeout(() => {
			if (this.prizeItemList && this.prizeItemList[index]) {
				this.prizeItemList[index].pic = 'https://example.com/uploaded-prize.jpg';
			}
			this.$message.success('上传成功');
		}, 1000);
	}
*/

	// 选择商品
	selectProduct(index: number) {
		if (this.disabled) return;

		this.currentSelectingIndex = index;
    this.$refs.chooseProduct.searchGoods();
		(this.$refs.chooseProduct as any).dialogVisible = true;

	}

	// 处理商品选择结果
	handleProductSelect(products: any[]) {
		if (this.currentSelectingIndex >= 0 && products && products.length > 0) {
			const selectedProduct = products[0]; // 取第一个选中的商品
			const currentItem = this.prizeItemList[this.currentSelectingIndex];

			// 更新奖项信息
			currentItem.prizeName = selectedProduct.productName ;
			currentItem.productId = selectedProduct.productId;
			currentItem.skuId = selectedProduct.skuId;

			/*// 如果商品有图片，也可以设置到奖品图片
			if (selectedProduct.pic) {
				currentItem.pic = selectedProduct.pic;
			}*/
		}

		this.currentSelectingIndex = -1;
	}

	// 选择优惠券
	selectCoupon(index: number) {
		if (this.disabled) return;

		this.currentSelectingIndex = index;
		const currentItem = this.prizeItemList[index];
		const currentCouponId = currentItem.couponId;
		(this.$refs.chooseCoupon as any).openDialog(currentCouponId);
	}

	// 处理优惠券选择结果
	handleCouponSelect(coupons: any[]) {
		if (this.currentSelectingIndex >= 0 && coupons && coupons.length > 0) {
			const selectedCoupon = coupons[0]; // 取第一个选中的优惠券
			const currentItem = this.prizeItemList[this.currentSelectingIndex];

			// 更新奖项信息
			currentItem.prizeName = selectedCoupon.couponName;
			currentItem.couponId = selectedCoupon.id;

			// 如果优惠券有背景图片，也可以设置到奖品图片
			if (selectedCoupon.backPic) {
				currentItem.pic = selectedCoupon.backPic;
			}
		}

		this.currentSelectingIndex = -1;
	}

	// 验证方法
	validate() {
		// 验证奖项列表
		if (!this.prizeItemList || this.prizeItemList.length === 0) {
			this.$message.error('请至少添加一个奖项');
			return false;
		}

		// 验证必填字段
		for (let i = 0; i < this.prizeItemList.length; i++) {
			const item = this.prizeItemList[i];
			if (!item.levelName) {
				this.$message.error(`第${i + 1}行奖项名称不能为空`);
				return false;
			}
			if (item.winningRate === undefined || item.winningRate < 0 || item.winningRate > 100) {
				this.$message.error(`第${i + 1}行中奖概率必须在0-100之间`);
				return false;
			}
		}

		// 验证中奖概率总和
		const totalRate = this.prizeItemList.reduce((sum, item) => sum + (item.winningRate || 0), 0);
		if (totalRate > 100) {
			this.$message.error('所有奖项的中奖概率总和不能超过100%');
			return false;
		}

		return true;
	}
}
</script>

<style lang="scss" scoped>
.prize-item-list {
	.el-table {
		border: 1px solid #ebeef5;
	}
}

// 小尺寸图片上传样式
.avatar-uploader-small {
	/deep/ .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		width: 60px;
		height: 60px;

		&:hover {
			border-color: #409EFF;
		}
	}

	.avatar-uploader-icon-small {
		font-size: 20px;
		color: #8c939d;
		width: 60px;
		height: 60px;
		line-height: 60px;
		text-align: center;
	}

	.avatar-small {
		width: 60px;
		height: 60px;
		display: block;
	}
}

// 表格内输入框样式
.input_number {
	width: 100%;

	/deep/ .el-input__inner {
		text-align: center;
	}
}

// 商品选择按钮样式
.product-select-btn {
	width: 100%;
	text-align: left;
	padding: 0 11px;
	color: #606266;
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	height: 32px;
	line-height: 30px;

	&:hover {
		color: #409EFF;
		border-color: #c6e2ff;
		background-color: #ecf5ff;
	}

	&:focus {
		color: #409EFF;
		border-color: #409EFF;
	}
}
</style>
