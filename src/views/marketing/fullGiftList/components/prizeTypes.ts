/*
 * @description: 营销活动抽奖相关类型定义
 * @Author: hh
 * @Date: 2025-01-14
 */



/**
 * 营销活动抽奖明细VO
 */
export interface ShopSalePrizeItemVo {
  /** 主键ID */
  id?: number;
  
  /** 主表ID */
  mainId?: number;
  
  /** 奖项名称 */
  levelName?: string;
  
  /** 排序 */
  seqBy?: number;
  
  /** 是否间隔奖项：0-否，1-是 */
  spacingFlag?: number;
  
  /** 是否奖品：0-否，1-是 */
  prizeFlag?: number;
  
  /** 奖品发放方式：1-线上邮寄，2-线下核销 */
  verifyType?: number;
  
  /** 奖品有效期开始时间 */
  startTime?: string;
  
  /** 奖品有效期结束时间 */
  endTime?: string;
  
  /** 奖品类型：1-商城商品，2-优惠券 */
  prizeType?: number;
  
  /** 商品ID */
  productId?: number;
  
  /** 商品规格ID */
  skuId?: number;
  
  /** 商品名称 */
  productName?: string;
  
  /** 优惠券ID */
  couponId?: number;
  
  /** 奖品名称 */
  prizeName?: string;
  
  /** 中奖概率 */
  winningRate?: number;
  
  /** 奖品图片 */
  pic?: string;
  
  /** 中奖参与人次 */
  winningPersonTimes?: number;
}

/**
 * 活动类型选项
 */
export const prizeTypeOptions = [
  { label: '大转盘', value: 1 },
  { label: '九宫格盲盒', value: 2 }
];

/**
 * 奖品类型选项
 */
export const prizeItemTypeOptions = [
  { label: '商城商品', value: 1 },
  { label: '优惠券', value: 2 }
];

/**
 * 奖品发放方式选项
 */
export const verifyTypeOptions = [
  { label: '线上邮寄', value: 1 },
  { label: '线下核销', value: 2 }
];

/**
 * 状态选项
 */
export const statusOptions = [
  { label: '草稿', value: 0 },
  { label: '待审核', value: 100 },
  { label: '审核通过', value: 101 },
  { label: '审核不通过', value: 200 },
  { label: '失效', value: 300 },
  { label: '停止', value: 400 }
];

/**
 * 中奖概率转换工具函数
 */

/**
 * 将百分比转换为小数（提交后端时使用）
 * @param percent 百分比值（如：10 表示 10%）
 * @returns 小数值（如：0.1）
 */
export function percentToDecimal(percent: number): number {
  if (percent === null || percent === undefined || isNaN(percent)) {
    return 0;
  }
  return Number((percent / 100).toFixed(4));
}

/**
 * 将小数转换为百分比（从后端获取数据时使用）
 * @param decimal 小数值（如：0.1）
 * @returns 百分比值（如：10 表示 10%）
 */
export function decimalToPercent(decimal: number): number {
  if (decimal === null || decimal === undefined || isNaN(decimal)) {
    return 0;
  }
  return Number((decimal * 100).toFixed(2));
}
