<!--
 * @description: 优惠券选择组件（多选版）
 * @Author: hh
 * @Date: 2025-01-14
-->
<template>
    <el-dialog :visible.sync="dialogVisible" highlight-current-row width="60%" :before-close="handleClose" title="选择优惠券">
        <el-form ref="form" :model="searchType" label-width="90px">
            <el-row>
                <el-col :span="6">
                    <el-form-item label="优惠券名称">
                        <el-input v-model="searchType.couponName" placeholder="请输入优惠券名称"
                            style="width: 180px;"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="优惠券类型">
                        <el-select v-model="searchType.couponType" style="width: 180px" placeholder="请选择类型"
                            :popper-append-to-body="false">
                            <el-option label="全部" :value="''" />
                            <el-option v-for="item in couponTypeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="9" >
                    <el-button @click="searchCoupons" type="primary" size="mini" round>搜索</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-table 
            ref="multipleTable" 
            :data="couponList" 
            tooltip-effect="dark" 
            style="width: 100%"
            :row-key="getRowKeys" 
            @selection-change="handleSelectionChange"
            @select-all="handleSelectAll"
            @select="handleSelect">
            <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
            <el-table-column label="序号" type="index"></el-table-column>
            <el-table-column label="优惠券名称" width="200" prop="couponName" show-overflow-tooltip></el-table-column>
            <el-table-column label="优惠券类型" width="120">
                <template slot-scope="scope">
                    {{ getCouponTypeName(scope.row.couponType) }}
                </template>
            </el-table-column>
            <el-table-column label="满额" width="100" prop="fullAmount">
                <template slot-scope="scope">
                    {{ scope.row.fullAmount ? `￥${scope.row.fullAmount}` : '-' }}
                </template>
            </el-table-column>
            <el-table-column label="减额/折扣" width="100" prop="promotion">
                <template slot-scope="scope">
                    {{ scope.row.promotion ? `￥${scope.row.promotion}` : '-' }}
                </template>
            </el-table-column>
            <el-table-column label="有效期" width="180">
                <template slot-scope="scope">
                    <div v-if="scope.row.startTime && scope.row.endTime">
                        {{ formatDate(scope.row.startTime) }} ~ {{ formatDate(scope.row.endTime) }}
                    </div>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
                <template slot-scope="scope">
                    {{ getStatusName(scope.row.status) }}
                </template>
            </el-table-column>
        </el-table>
        <PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handlePageCurrentChange" />
        <span slot="footer" class="dialog-footer">
            <el-button @click="close">取 消</el-button>
            <el-button type="primary" @click="primaryButton">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import { pageCoupon } from "@/api/certificateApi/certificateApi";

@Component({
    components: {
        PageManage,
    }
})
export default class ChooseCoupon extends Vue {
    dialogVisible = false;
    selectedCoupons: any[] = []; // 存储多选的优惠券
    selectedCouponIds: any[] = []; // 存储多选的优惠券ID
    searchType = {
        couponName: "",
        couponType: "",
        status: "",
        current: 1,
        size: 10
    }
    
    // 优惠券类型选项
    couponTypeList = [
        { label: '普通券', value: 0 },
        { label: '新人券', value: 1 },
        { label: '商品优惠券', value: 2 },
        { label: '品类优惠券', value: 3 },
        { value: 4, label: "满返券" },
    ];
    
    // 状态选项
    statusList = [
        { label: '未生效', value: 100 },
        { label: '已生效', value: 101 },
        { label: '已过期', value: 104 },
        { label: '驳回', value: 200 },
        { label: '终止', value: 300 }
    ];
    
    current = 1
    size = 10
    total = 0
    couponList = []
    
    mounted() {
        // 移除自动加载，只在对话框打开时加载数据
    }
    
    handleClose(done) {
        this.selectedCoupons = [];
        this.selectedCouponIds = [];
        done();
    }
    
    searchCoupons() {
        this.current = 1;
        this.size = 10;
        const currentSelectedIds = this.selectedCouponIds;
        this.loadCouponList().then(() => {
            this.setSelectedCoupons(currentSelectedIds);
        });
    }

    // 加载优惠券数据
    loadCouponList() {
        let params = { ...this.searchType };
        params.current = this.current;
        params.size = this.size;
        params.status="101";
        // 删除空值参数
        for (const key in params) {
            if (!params[key] && params[key] !== 0) {
                delete params[key];
            }
        }

        return pageCoupon(params).then((res) => {
            this.couponList = res.data.list || [];
            this.total = res.data.total || 0;
        }).catch((err) => {
            this.$message.error(err);
            throw err;
        });
    }
    
    // 打开对话框并加载数据
    openDialog(currentCouponIds: number[] = []) {
        if (this.couponList.length === 0) {
            this.loadCouponList().then(() => {
                this.setSelectedCoupons(currentCouponIds);
            });
        } else {
            this.setSelectedCoupons(currentCouponIds);
        }
        this.dialogVisible = true;
    }
    
    getRowKeys(row) {
        return row.id;
    }

    // 设置已选中的优惠券（多选）
    setSelectedCoupons(couponIds: number[] = []) {
        this.$nextTick(() => {
            const table = this.$refs.multipleTable as any;
            if (table && couponIds.length > 0) {
                this.couponList.forEach(row => {
                    if (couponIds.includes(row.id)) {
                        table.toggleRowSelection(row, true);
                    }
                });
            }
        });
    }

    // 处理多选变化
    handleSelectionChange(selectedRows) {
        // this.selectedCoupons = this.selectedCoupons.concat(selectedRows)
        this.selectedCoupons = selectedRows;
        this.selectedCouponIds = selectedRows.map(item => item.id);
    }

    // 处理全选
    handleSelectAll(selection) {
        this.handleSelectionChange(selection);
    }

    // 处理单个选择
    handleSelect(selection, row) {
        this.handleSelectionChange(selection);
    }
    
    handleSizeChange(val) {
        this.size = val;
        const currentSelectedIds = this.selectedCouponIds;
        this.loadCouponList().then(() => {
            this.setSelectedCoupons(currentSelectedIds);
        });
    }

    handlePageCurrentChange(val) {
        this.current = val;
        const currentSelectedIds = this.selectedCouponIds;
        this.loadCouponList().then(() => {
            this.setSelectedCoupons(currentSelectedIds);
        });
    }
    
    close() {
        this.selectedCoupons = [];
        this.selectedCouponIds = [];
        this.dialogVisible = false;
        this.$refs.multipleTable.clearSelection();

    }
    
    primaryButton() {
        if (this.selectedCoupons.length === 0) {
            this.$message.warning('请至少选择一张优惠券');
            return;
        }
        
        this.dialogVisible = false;
        // 发送所有选中的优惠券
        this.$emit("handleCoupon", this.selectedCoupons);
        this.selectedCoupons = [];
        this.selectedCouponIds = [];
        this.$refs.multipleTable.clearSelection();

    }
    
    // 获取优惠券类型名称
    getCouponTypeName(type: number) {
        const typeItem = this.couponTypeList.find(item => item.value === type);
        return typeItem ? typeItem.label : '-';
    }
    
    // 获取状态名称
    getStatusName(status: number) {
        const statusItem = this.statusList.find(item => item.value === status);
        return statusItem ? statusItem.label : '-';
    }
    
    // 格式化日期
    formatDate(date: string | Date) {
        if (!date) return '-';
        const d = new Date(date);
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    }
}
</script>

<style lang="scss">
</style>