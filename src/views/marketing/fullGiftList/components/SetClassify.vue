<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:47:50
-->
<template>
  <!-- 设置分类 -->
  <div class="all">
    <div class="setClassify" >
      <el-button type="primary" @click="emitFun('add')" round>新增满减满赠活动</el-button>
    </div>
    <!-- <div class="setClassify" v-if="isSupper || copyButton">
      <el-button type="primary" @click="emitFun('batchCopy')" round>复制抽奖方案</el-button>
    </div>
    <div class="setClassify" v-if="isSupper || toExamineButton">
      <el-button type="primary" @click="emitFun('batchAudit')" round>批量审核</el-button>
    </div> -->

  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
@Component({

})
export default class SetClassify extends Vue {
  name = "SetClassify";

  menuName = "抽奖活动方案";

  buttonList = [];

  isSupper = 0;

  addButtonCode = "marketing.add";

  addButton = false;

  copyButtonCode = "marketing.copy";

  copyButton = false;

  toExamineButtonCode = "marketing.toExamine";

  toExamineButton = false;

  mounted() {
    this.buttonAuth();
  }
  buttonAuth() {
    this.isSupper = this.$STORE.userStore.userInfo.isSupper
    let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

    let buttonList = [];
    authMenuButtonVos.forEach(element => {
      buttonList.push(element.buttonCode);
    });
    this.buttonList = buttonList

    var addButtonData = buttonList.find(e => e == this.addButtonCode);

    if (addButtonData != null && addButtonData != undefined) {
      this.addButton = true;
    }

    var copyButtonData = buttonList.find(e => e == this.copyButtonCode);

    if (copyButtonData != null && copyButtonData != undefined) {
      this.copyButton = true;
    }
    var toExamineButtonData = buttonList.find(e => e == this.toExamineButtonCode);

    if (toExamineButtonData != null && toExamineButtonData != undefined) {
      this.toExamineButton = true;
    }

  }

  /** 触发父级方法 */
  emitFun(name: string, data?: any, status?: boolean) {
    this.$emit("table-function", name, data, status);
  }

  /**
   * 发布奖励方案
   */
  handleClick() {
    this.$router.push({
      name: "addLuckyDraw",
    });
  }
 /* handleCommandsx(val: any) {
    this.sort = val.item.text
    this.sortcommand = val.item.command
    this.sortIndex = ''
    // console.log('222222', val);

  }
  commandSort(val: any) {
    if (this.sortcommand) {
      this.sortIndex = val
      this.$emit("commandsx", val, this.sortcommand);
    } else {
      this.$message.error("请先选择排序");
    }
  }
*/
}
</script>

<style lang="scss">
@import "@/assets/styles/mixins/mixins.scss";

@include b(setClassify) {
  display: flex;
  line-height: 30px;
  // border: 1px solid #dcdfe6;
  overflow: hidden;
  margin-left: 20px;
  border-radius: 50px;
  background-color: #409EFF;
  color: #ffffff;
  border: 1px solid #409EFF;
  position: relative;

  @include e(title) {
    text-align: center;
    padding: 0 20px;
    cursor: pointer;
  }

  @include e(icon) {
    width: 40px;
    text-align: center;
    cursor: pointer;
    vertical-align: middle;
    color: #ffffff;
    font-weight: bold;
  }
}

.all {
  display: flex;
  align-items: center;
  margin: 10px 0;
  .el-dropdown-link {
    height: 32px;
    width: 100px;
    line-height: 20px;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 1);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    text-align: left;
    border: 1px solid rgba(187, 187, 187, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    margin-left: 17px;

    .el-dropdown {
      width: 80px;

      span {
        cursor: pointer;

      }
    }

    .dropdownsx {
      display: flex;
      flex-direction: column;

      .el-icon1 {
        color: #409EFF;
      }

      .el-icon2 {
        color: #409EFF;
      }
    }
  }

}

.setClassify__icon::after {
  color: #ffffff;
  content: "|";
  position: absolute;
  left: -5px;
  bottom: 1px;
}

.commandClass {
  height: 90px;
  overflow: overlay;
}

.commandClass::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0);
}
</style>
