<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:52:07
 2025-7-16
-->
<template>
	<m-card class="form" :needToggle="true">
		<el-form ref="form" :model="searchType" label-width="100px">



			<el-row>
				<el-col :span="6">
					<el-form-item label="方案名称">
						<el-input v-model="form.name" placeholder="请输入方案名称"></el-input>
					</el-form-item>
				</el-col>

				<el-col :span="6">
					<el-form-item label="有效期">
						<el-date-picker v-model="value0" type="daterange" range-separator="-" start-placeholder="开始时间"
							end-placeholder="结束时间" style="width: 100%;" @change="chooseTime">
						</el-date-picker>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="状态">
						<el-select v-model="form.status" placeholder="请选择状态" style="width: 100%;">
							<el-option label="全部" :value="null" />
							<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<!-- 
				<el-tab-pane label="待审核" name="100"> </el-tab-pane>
      <el-tab-pane label="审核通过" name="101"> </el-tab-pane>
      <el-tab-pane label="审核不通过" name="200"> </el-tab-pane>
      <el-tab-pane label="草稿" name="0"></el-tab-pane>
      <el-tab-pane label="失效" name="300"> </el-tab-pane>
      <el-tab-pane label="停止" name="400"> </el-tab-pane>
      <el-tab-pane label="全部" name="-1"></el-tab-pane> -->

			</el-row>

			<!-- <el-row>
				
				<el-col :span="6">
					<el-form-item label="商品名称">
						<el-input v-model="form.shopName" placeholder="请输入商品名称"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="6">
					<el-form-item label="商家信息">
							<el-input v-model="form.shopName" @focus="getShopList" readonly placeholder="请输入商家信息" clearable></el-input>
						</el-form-item>
				</el-col>
			</el-row> -->



			<el-form-item>
				<el-button type="primary" @click="search">搜索</el-button>
			</el-form-item>
		</el-form>

		<el-dialog :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
			<el-table ref="multipleTable" :data="shopList" tooltip-effect="dark" style="width: 100%" :row-key="getRowKeys"
				@selection-change="handleSelectionChange">
				<el-table-column type="selection" :reserve-selection="true">
				</el-table-column>
				<el-table-column label="序号" type="index">
				</el-table-column>
				<el-table-column width="55">
				</el-table-column>
				<el-table-column label="商家名称" width="120">
					<template slot-scope="scope">{{ scope.row.name }}</template>
				</el-table-column>
				<el-table-column prop="categoryName" label="所属分类" show-overflow-tooltip>
				</el-table-column>
				<el-table-column prop="contacts" label="联系人" width="120">
				</el-table-column>
				<el-table-column prop="phone" label="联系电话" show-overflow-tooltip>
				</el-table-column>
			</el-table>
			<PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
				@handleCurrentChange="handleCurrentChange" style="margin-top: 0px">
			</PageManage>
			<span slot="footer" class="dialog-footer">
				<!-- <el-button @click="toggleSelection([selectionList[0],tableData[0]])">切换第二、第三行的选中状态</el-button> -->
				<el-button @click="close">取 消</el-button>
				<el-button type="primary" @click="primaryButton">确 定</el-button>
				<el-button type="primary" @click="reset">清 空</el-button>
			</span>
		</el-dialog>

	</m-card>
	<!-- </div> -->
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { SearchState, SearchKeyType } from "./searchType";
import DateUtil from "@/store/modules/date";
import { DatePickerOptions } from "element-ui/types/date-picker";
import { getShopsPartnerList, getQueryPageList } from "@/api/shopsPartner/shopsPartner";
import PageManage from "@/components/PageManage.vue";
import { getRegionList } from "@/api/good/goods";

// import { watch } from "vue";

@Component({
	components: {
		PageManage
	}
})
export default class Searchs extends Vue {
	name = "Searchs";
	@Prop({})
	status!: string;


	couponTypeOption = [{
		value: '',
		label: '全部'
	}, {
		value: 0,
		label: '普通券'
	}, {
		value: 1,
		label: '新人券'
	}, {
		value: 2,
		label: "商品优惠券",
	},
	{
		value: 3,
		label: "品类优惠券",
	}];
	// 审核状态:100->待审核;101->审核通过;200->驳回
	options = [
		{
			value: 100,
			label: '待审核'
		}, 
		{
			value: 101,
			label: '审核通过'
		},
		{
			value: 200,
			label: '审核不通过'
		},
		{
			value: 300,
			label: '失效'
		},
		{
			value: 400,
			label: '停止'
		},
	];
	showCateList = [];
	form = {
		startTime: '',
		endTime: '',

		current: 1,
		size: 10,
		total: 0,

		name: '',
		status: null,
		queryShopIds: '',
		shopName: '',
		area: '',
	};

	searchType: any = {
		receiveTimes: '',
		couponName: '',
		displayStartTime: '',
		displayEndTime: '',
		startTimeEnd: '',
		startTimeBegin: '',
		newPeopleFlag: '',
		promotion: '',
		prizeType: ''
	}
	value0 = ''
	value1 = ''

	areas = [];
	shopList = [];
	current = 1;
	size = 10;
	total = 0;
	dialogVisible = false;
	selectedArray = []

	created() {
		this.getRegionList();

	}
	/**
	 * 获取所有专区
	 */
	async getRegionList() {
		this.form.area = ""
		const param = {
			current: 1,
			size: 20,
		};
		getRegionList(param).then(res => {
			this.areas = res.data.list
		})
	}
	handleClose(done: any) {
		this.selectedArray = []
		done();
	}
	close() {
		this.selectedArray = []
		this.dialogVisible = false;
	}
	reset() {
		this.selectedArray = []
		this.form.queryShopIds = "";
		this.form.shopName = "";
		this.$refs.multipleTable.clearSelection();
		this.dialogVisible = false;
	}
	primaryButton() {
		this.form.queryShopIds = this.selectedArray.map(row => row.shopId).join(",");
		this.form.shopName = this.selectedArray.map(row => row.name).join("，");
		this.dialogVisible = false;
	}
	handleSelectionChange(val) {
		this.selectedArray = val
	}
	getRowKeys(row) {
		return row.id //唯一性
	}
	/**
	* @method handleSizeChange
	* @description 每页 条
	*/
	handleSizeChange(val: number) {
		this.size = val;
		this.getShopList()
	}
	/**
 * @method handleCurrentChange
 * @description 当前页
 */
	handleCurrentChange(val: number) {
		this.current = val;
		this.getShopList()
		// this.getPageTicket();
	}
	/**
 * 获取店铺列表
 */
	getShopList() {
		let params = {}
		params.prohibitStatus = "0"
		params.current = this.current
		params.size = this.size
		getQueryPageList(params).then((res) => {
			this.shopList = res.data.list
			this.total = res.data.total
			this.dialogVisible = true
		}).catch((err) => {
			this.$message.error(err)
		})

	}

	chooseTime(data: any) {
		this.form.startTime = data ? this.dateConversion(data[0]) : "";
		this.form.endTime = data ? this.dateConversion(data[1]) : "";
		console.log("this.form", this.form);

	}


	dateConversion(value: Date) {
		const date = new DateUtil("").getYMDHMSs(value);
		return date;
	}

	search() {
		this.$emit("searchBy", this.form);
	}
}
</script>

<style lang="scss" scoped>
.el-form-item .el-input {
	width: 224px;
}

.el-form-item .el-button {
	width: 90px;
}

/deep/ .el-form-item .el-input {
	width: 100%;
}

@include b(form) {
	transform-origin: left top;
	overflow: hidden;
	position: relative;
	transition: all 0.3s ease 0s;

	&.show {
		height: 150px;
		margin-bottom: 20px;
	}

	&.hide {
		margin-bottom: 20px;
		height: 50px;

		.form__btn {
			width: 940px;
			height: 50px;
			background: #f9f9f9;
			line-height: 50px;
			// margin-top: 20px
		}
	}

	@include e(btn) {
		width: 100%;
		position: absolute;
		bottom: 0;
		text-align: center;
		padding-bottom: 20px;

		span {
			cursor: pointer;
		}
	}
}

.page {
	// height: 270px;
	background-color: #f9f9f9;
	margin-bottom: 20px;
}

@include b(search) {
	display: flex;
	flex-wrap: wrap;

	@include e(item) {
		padding: 20px 40px 10px 40px;
		display: flex;
		justify-content: space-between;
		align-items: center;

		@include m(text) {
			width: 60px;
		}
	}

	@include e(icon) {
		width: 40px;
		text-align: center;
		border-left: 1px solid #dcdfe6;
		cursor: pointer;
		vertical-align: middle;
	}
}

@include b(searchButton) {
	margin: 20px 30px;
}
</style>
