<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-01 15:47:16
-->
<template>
	<div class="goodForm">
		<keep-alive>
			<el-form :model="formModel" :rules="formRules" ref="formModel" label-width="100px">

				<div class="baseMsg">
					<el-form-item label="优惠券名称" prop="couponName">
						<el-input v-model="formModel.couponName" :maxlength="128" style="width: 550px"
							placeholder="请输入优惠券名称"></el-input>
					</el-form-item>
					<el-form-item label="优惠券面额" prop="promotion">
						<el-input v-model="formModel.promotion" style="width: 550px" maxlength="30"
							placeholder="请输入优惠券面额"></el-input>
					</el-form-item>
					<el-form-item label="显示时间" prop="time">
						<div style="display: flex;align-items: center;justify-content: flex-start;">
							<el-date-picker v-model="formModel.displayStartTime" type="datetime" style="width:200px;"
								prefix-icon="el-icon-alarm-clock" placeholder="请选择开始时间" @change="chooseTime">
							</el-date-picker>
							<i style="margin: 0 10px;font-size: 30px;color: rgb(187, 187, 187);"
								class="el-icon-minus"></i>
							<el-date-picker v-model="formModel.displayEndTime" type="datetime" style="width:200px;"
								prefix-icon="el-icon-alarm-clock" placeholder="请选择开始时间" @change="chooseTimeEad">
							</el-date-picker>
						</div>
					</el-form-item>
					<el-form-item label="订单满" prop="fullAmount">
						<el-input v-model="formModel.fullAmount" style="width: 150px" maxlength="30"
							placeholder="请输入金额"></el-input>
						<span class="useableTimes__ci">元可使用</span>
					</el-form-item>
					<!-- 到期类型:100->指定时间;101->购买之日起计算; -->
					<el-form-item label="有效期" prop="times">
						<div style="display: flex;align-items: center;justify-content: flex-start;">
							<el-date-picker v-model="formModel.startTime" type="datetime" style="width:200px;"
								prefix-icon="el-icon-alarm-clock" placeholder="请选择日期" @change="chooseStartTime">
							</el-date-picker>
							<i style="margin: 0 10px;font-size: 30px;color: rgb(187, 187, 187);"
								class="el-icon-minus"></i>
							<el-date-picker v-model="formModel.endTime" type="datetime" style="width:200px;"
								prefix-icon="el-icon-alarm-clock" placeholder="请选择日期" @change="chooseEndTime">
							</el-date-picker>
						</div>
					</el-form-item>
					<el-form-item label="领取次数" prop="receiveTimes">
						<el-input v-model="formModel.receiveTimes" style="width: 550px" maxlength="30"
							placeholder="请输入领取次数"></el-input>
					</el-form-item>
					<el-form-item label="优惠券类型" prop="couponType">
						<el-select v-model="formModel.couponType" placeholder="优惠券类型">
							<el-option v-for="item in couponTypeList" :key="item.id" :label="item.label"
								:value="item.id"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="备注" prop="remark">
						<el-input v-model="formModel.remark" style="width: 550px" placeholder="填写您的备注内容"></el-input>
					</el-form-item>

					<el-form-item label="样板展示" prop="times">
						<div style="display: flex;align-items: center;justify-content: flex-start;">
							<youhuiCertificate :widePicImg='widePic' :color='color1' @certificateImg="certificateImgs">
							</youhuiCertificate>
							<div class="upload">
								{{ color1 }}
								<el-color-picker v-model="color1"></el-color-picker>
							</div>
						</div>
					</el-form-item>
					<el-form-item label="参与商户" prop="remark">
						<el-radio v-model="formModel.shopFlag" :label="0"><el-button
								size="mini">所有商户</el-button></el-radio>
					</el-form-item>
					<el-form-item label="" prop="remark">
						<el-radio v-model="formModel.shopFlag" :label="1">
							<el-button size="mini" @click="dialogShow"
								style="background:#07CAD5 ;color: #ffffff ;">自选商户</el-button></el-radio>
					</el-form-item>
					<el-form-item style="margin-top: 50px;margin-left: 150px;">
						<el-button @click="editSecUnit">取 消</el-button>

						<el-button type="primary" @click="submitForm()">提交</el-button>
						<!-- <el-button type="primary" @click="showRegion = false">立即创建</el-button> -->
					</el-form-item>
				</div>
			</el-form>
		</keep-alive>
		<el-dialog :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
			<el-form ref="form" :model="searchType" label-width="90px">
				<el-row>
					<el-col :span="12">
						<el-form-item label="商家名称">
							<el-input v-model="searchType.name" placeholder="请输入商家名称" style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系人电话">
							<el-input v-model="searchType.phone" placeholder="请输入联系人电话"
								style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="商家类型">
							<el-cascader v-model="name" :props="props" :options="options"
								@change="handleChange"></el-cascader>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系人名称">
							<el-input v-model="searchType.contacts" placeholder="请输入联系人名称"
								style="width: 200px;"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-button @click="getShopList" type="primary" size="mini" round>搜索</el-button>
					</el-col>
				</el-row>
				<div style="border: 1px solid #ccc;width: 100%;margin: 20px 0;"></div>
				<el-row>
					<el-col :span="24">
						<el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%"
							:row-key="getRowKeys" @selection-change="handleSelectionChange">
							<el-table-column type="selection" :reserve-selection="true">
							</el-table-column>
							<el-table-column label="序号" type="index">
							</el-table-column>
							<el-table-column width="55">
							</el-table-column>
							<el-table-column label="商家名称" width="160">
								<template slot-scope="scope">{{ scope.row.name }}</template>
							</el-table-column>
							<el-table-column prop="contacts" label="联系人名称" width="120">
							</el-table-column>
							<el-table-column prop="phone" label="联系人电话" show-overflow-tooltip>
							</el-table-column>
							<el-table-column prop="categoryName" label="商家类型" show-overflow-tooltip>
							</el-table-column>
						</el-table>
					</el-col>
				</el-row>
			</el-form>
			<!-- 设置分类 -->
			<div class="listBottom">
				<PageManage :pageSize="searchType.size" :pageNum="searchType.current" :total="total"
					@handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"
					style="margin-top: 0px">
				</PageManage>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="primaryButton">确 定</el-button>
			</span>
		</el-dialog>

	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { AddSubmitFormType, partnerListType, formModelType } from "./newGoodType";
import { readFile, advanceGetImage } from "@/store/modules/voidImg";
import { ElUploadInternalFileDetail } from "element-ui/types/upload";
import PageManage from "@/components/PageManage.vue";
import { upLoad } from "@/api/index";
import DateUtil from "@/store/modules/date";
import { SearchState, SearchKeyType } from "../searchType";
import { getIdCoupon, addCoupon, editCoupon, getAllShops, getShopList } from "@/api/certificateApi/certificateApi"
import youhuiCertificate from "./youhuiCertificate";
@Component({
	components: {
		PageManage,
		youhuiCertificate
	}
})
export default class NewGoodFormOpints extends Vue {
	@Prop({})
	from!: string;
	color1 = ''
	/////////////////////
	// 存放数据的对象
	formModel = { shopFlag: 0 } as formModelType;
	/** 基本信息验证 */
	formRules = {
		name: [{ required: true, message: "请输入商品名称", trigger: "blur" }],
		goodsCode: [{ required: true, message: "请输入商品编码", trigger: "blur" }],
		points: [{ required: true, message: "需要用多少积分兑换本商品", trigger: "blur" }, { required: true, message: "需要用多少现金兑换本商品", trigger: "blur" }],
		amount: [{ required: true, message: "请输入总数量", trigger: "blur" }],
		showCategoryIds: [
			{ required: true, message: "请选择分类", trigger: "blur" }
		]
	};
	tableData = []
	selectionList = []
	commodityCode = "";
	createTime = ''
	showRegion = false;
	shopTicketList = []
	widePic = ''
	dialogVisible = false;
	/** 搜索类型 */
	couponTypeList = [
		{
			id: 0,
			label: "普通券",
		},
		{
			id: 1,
			label: "新人券",
		}, {
			id: 2,
			label: "商品优惠券",
		},
		{
			id: 3,
			label: "品类优惠券",
		},
		{
			id: 4,
			label: "满返券",
		},
	];
	// searchType : SearchKeyType = {};
	searchType = {
		current: 1,
		size: 10
	} as SearchKeyType;
	name = [];
	options = []
	props = {
		expandTrigger: 'hover',
		value: 'shopsCategoryId',
		label: 'name',
		children: 'shopsCategoryVos'
	}
	total = 0;
	partnerList: Array<partnerListType> = []
	multipleSelection = []


	mounted() {
		this.getIdCoupon();
		getAllShops({}).then((res) => {
			this.options = res.data
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	getRowKeys(row) {
		return row.id //唯一性
	}
	handleClose(done: any) {
		done();
	}
	/**选择商家信息 */
	handleSelectionChange(val) {
		console.log(val);
		this.selectionList = val;
	}
	/**点击自选商户的确定按钮 */
	primaryButton() {
		let dataList = this.selectionList;
		let ids = [];
		dataList.forEach(data => {
			let arr = {}
			arr.shopsPartnerId = data.id
			ids.push(arr);
		})
		this.formModel.partnerList = ids
		this.formModel.shopFlag = 1
		this.dialogVisible = false
		//重置复选选项
		this.selectionList = []
		this.$refs.multipleTable.clearSelection();
	}

	dialogShow() {
		this.getShopList()
		this.dialogVisible = true;
		let partnerList = this.formModel.partnerList

		this.$nextTick(function () {
			if (partnerList != null && partnerList != undefined && partnerList.length > 0) {
				partnerList.forEach(data => {
					let row = {};
					row.id = data.shopsPartnerId
					this.$refs.multipleTable.toggleRowSelection(row, true);
				})
			}
		})

	}



	/**
	 * 上传图片
	 */
	async addUploadProductImg(file: ElUploadInternalFileDetail) {
		const whiteList = ["image/jpeg", "image/jpg", "image/png"];

		const isLt1M = file.size < 1 * 1024 * 1024;
		if (!whiteList.includes(file.raw.type)) {
			this.$message.error("上传文件只能是 JPG或PNG 格式!");
			return;
		}
		if (!isLt1M) {
			this.$message.error("上传文件大小不能超过 1MB!");
			return;
		}

		const res = await upLoad({
			file: file.raw
		});
		this.widePic = res.data;
		this.formModel.backPic = res.data;
		console.log('eeeeeeee上传大图片', this.widePic);
		// this.productImgList.push({
		//   img: res.data
		// });
	}
	certificateImgs(backColor: string) {
		this.formModel.backColor = backColor
		console.log('66666666666', this.widePic, this.formModel);
	}

	handleChange(value: any) {
		this.searchType.shopsCategoryId = value[1]
	}
	/**
	 * 显示开始时间
	 * @param data
	 */
	chooseTime(data: any) {
		this.formModel.displayStartTime = data ? this.dateConversion(data) : "";
	}
	/**
	 * 显示结束时间
	 * @param data
	 */
	chooseTimeEad(data: any) {
		this.formModel.displayEndTime = data ? this.dateConversion(data) : "";
	}

	/**
	 * 有效期开始时间
	 * @param data
	 */
	chooseStartTime(data: any) {
		this.formModel.startTime = data ? this.dateConversion(data) : "";
	}
	/**
	 * 有效期结束时间
	 * @param data
	*/
	chooseEndTime(data: any) {
		this.formModel.endTime = data ? this.dateConversion(data) : "";
	}
	dateConversion(value: Date) {
		const date = new DateUtil("").getYMDHMSs(value);
		return date;
	}
	getShopList() {
		getShopList(this.searchType).then((res) => {
			this.tableData = res.data.list
			this.total = res.data.total
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	// 提交信息表单
	submitForm() {
		console.log(this.formModel.displayStartTime);
		if (this.formModel.couponName == null || this.formModel.couponName == undefined || this.formModel.couponName == "") {
			this.$message.error('优惠券名称不能为空')
			return
		}
		if (this.formModel.promotion == null || this.formModel.promotion == undefined) {
			this.$message.error('优惠券面额不能为空')
			return
		}
		if (!this.formModel.displayStartTime || !this.formModel.displayEndTime) {
			this.$message.error('显示时间不能为空')
			return
		}
		if (this.formModel.fullAmount == null || this.formModel.fullAmount == undefined) {
			this.$message.error('订单满xx元可使用不能为空')
			return
		}
		if (!this.formModel.startTime || !this.formModel.endTime) {
			this.$message.error('有效期时间不能为空')
			return
		}
		if (this.formModel.receiveTimes == null || this.formModel.receiveTimes == undefined) {
			this.$message.error('领取次数不能为空')
			return
		}
		if (this.formModel.couponType == null || this.formModel.couponType == undefined) {
			this.$message.error('优惠券类型不能为空')
			return
		}
		if (!this.formModel.backColor) {
			this.formModel.backColor = "#d63040"
		}
		if (this.$route.params.id || this.$route.query.id) {
			this.setEditCoupon()
		} else {
			this.setAddCoupon()
		}
	}
	/**
* 确定退出发布积分商品页面
*/

	editSecUnit() {
		this.$confirm(
			`确定退出${this.$route.query.id ? '编辑' : '发布'}优惠券页面?退出后，未保存的信息将不会保留!`,
			"提示",
			{
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			},
		).then(() => {
			this.$router.go(-1)
		});


	}
	/**
	 * 根据id查看通行票活动
	 */
	async getIdCoupon() {
		const goodId = this.$route.query.id || this.$route.params.id;
		if (!goodId) {
			return;
		}
		const { data } = await getIdCoupon({ 'id': goodId });
		if (data.shopFlag) {
			data.shopFlag = 1
		} else {
			data.shopFlag = 0
		}
		this.color1 = data.backColor
		this.widePic = data.backPic
		this.formModel = data
		console.log(this.formModel);
	}
	/**
	添加优惠证
	*/
	setAddCoupon() {
		addCoupon(this.formModel).then((res) => {
			this.$message({
				type: 'success',
				message: '创建成功!'
			});
			this.$router.go(-1)
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/** 编辑通行票活动*/
	setEditCoupon() {

		editCoupon(this.formModel).then((res) => {
			// console.log('reee12,', res);
			this.$message({
				type: 'success',
				message: '成功!'
			});
			this.$router.go(-1)
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.searchType.size = val;
		this.getShopList()
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.searchType.current = val;
		console.log('当前页', val);
		this.getShopList()
		// this.getPageTicket();
	}
}
</script>

<style lang="scss">
@import "@/assets/styles/goods/index.scss";

.upload {
	display: flex;
	align-items: center;
	flex-direction: column;
	margin-left: 10px;

	.upload-demo {
		margin-top: 10px;
	}
}

.useableTimes__ci {
	margin-left: 10px;
}

.goodForm::-webkit-scrollbar {
	display: none;
}

.w-e-text-container {
	height: 532px !important;
	/*!important是重点，因为原div是行内样式设置的高度300px*/
}
</style>
