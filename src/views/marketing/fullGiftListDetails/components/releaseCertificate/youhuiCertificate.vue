<template>
	<div class="box">

		<div class="box_img ">
			<div>
				<div class="box_img__list" :style="{'background':colorPicker?colorPicker:'#d63040'}">

					<!-- <div class="goBusiness flex-center-center">
						使用商家
					</div> -->


					<img class="goBusiness__xinOne" style="height: 120px;" mode="heightFix" :src="xinOne">
					</img>
					<div class="goBusiness__xintwo">
						<img style="width:200px;" mode="widthFix" :src="xintwo">
						<div class="ticketName flex-center-center">
							<div class="nowrapOne ">
								{{data.couponName||'优惠券名称'}}
							</div>
						</div>
						</img>
					</div>


					<div class="price ">
						<!-- <div class="price__num flex-start-center">
							<div class="id">{{data.price||'100'}}
								<div class="view">
									<span>元</span>

								</div>
							</div>

						</div> -->

						<div class="buy">
							立即领取
						</div>
					</div>

					<div class="full__reduction">

						<div class="full__reduction__name">
							<!-- 订单满{{item.fullAmount}}使用 -->
							订单满{{data.fullAmount||'1000'}}可减{{data.promotion||'300'}}
						</div>
						<div class="limitation">
							限{{data.endTime||'2021-50-20 50:60:20'}}前使用
						</div>
						<!-- <div v-else-if="data.expiredDays" class="limitation">
							购买后可使用：{{data.expiredDays||'10'}}天
						</div> -->
						<!-- <div class="limitation">
							可使用：{{data.useableTimes||'10'}}次
						</div> -->
					</div>
					<!-- <div class="imgss">
						<img class="meToShares" :src="require('./one.png')" mode="widthFix"> </img>
						<div class="">
							<span>点我</span>
							<span style="margin-top: 5px;">分享</span>
						</div>
					</div> -->
				</div>

			</div>
		</div>
		<van-toast id="van-toast" :forbidClick="true"></van-toast>
	</div>
</template>

<script lang="ts">
	import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
	// import Toast from '@/wxcomponents/vant-weapp/toast/toast'
	// import { createTicketOrder } from '@/api/modules/certificate'
	@Component({})
	// shopTicketinfo
	export default class youhuiCertificate extends Vue {
		@Prop() color! : string;

		@Watch("color")
		getNewModeId() {
			this.colorPicker = this.color;
			
			this.$emit('certificateImg', this.colorPicker)
			console.log('获取商品数组信息', this.color, this.backPic);

		}

		@Prop() list! : string;

		@Watch("list")
		widePicImgId() {
			this.data = this.list;

			// this.$emit('certificateImg',this.colorPicker,this.backPic)
			console.log('backPic数组信息', this.data);

		}

		colorPicker = '#d63040'
		data = {}
		xinOne = require('./xinOne.png')
		xintwo = require('./xintwo.png')
		img1 = '@/as'
		options = {
			items: ''
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		background: white;
		display: flex;
		justify-content: center;
		color: #101010;

		.box_img {
			width: 450px;



			.box_img__for {
				background: #ccc;
			}

			.box_img__list {
				width: 100%;
				margin-top: 1px;
				height: 120px;
				display: flex;
				justify-content: flex-start;
				border-radius: 0 68px 68px 0;
				position: relative;
				color: #d63040;

				

				.goBusiness {
					font-size: 10px;
					color: #ffe7c7;
					width: 60px;
					height: 20px;
					border-radius: 0 10px 10px 0;
					background: #d63040;
					position: absolute;
					top: 10px;
					left: 0px;
					z-index: 36;
					margin-bottom: 10px;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.goBusiness__xinOne {
					position: absolute;
					top: 0px;
					left: -1px;
					z-index: 20;

				}

				.goBusiness__xintwo {
					position: absolute;
					top: -10px;
					right: 86px;
					z-index: 30;
					width: 200px;
					height: 30px;

					img {
						vertical-align: bottom;
					}

					.ticketName {
						width: 200px;
						position: absolute;
						top: 0px;
						right: 0px;
						height: 30px;
						display: flex;
						justify-content: center;
						align-items: center;

						div {
							font-size: 20px;
							width: 160px;
							text-align: center;
							display: -webkit-box;
							// 限制在一个块元素显示的文本的行数
							-webkit-line-clamp: 1;
							// 设置或检索伸缩盒对象的子元素的排列方式
							-webkit-box-orient: vertical;
							// 溢出隐藏
							overflow: hidden;
							// 使用省略号代替文本超出部分
							text-overflow: ellipsis;
							// 设置每行高度
							// line-height: 20px;
							// 允许在单词内换行
							word-break: break-all;
						}
					}
				}

				.price {
					// margin: 30px 0 0 20px;
					width: 150px;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					z-index: 30;
					margin-top:20px;

					// 4 96=1
					.price__num {

						// :style="{'transform': 'scale('+item.scale+')'}"
						.id {
							position: relative;
							font-size: 46px;
							font-weight: 700;
						}

						.view {
							height:32px;
							position: absolute;
							right: -14px;
							z-index: 100;
							top: 0px;
							display: flex;
							align-items: center;
							justify-content: center;

							span {
								display: block;
								width: 20px;
								height: 20px;
								border-radius: 50%;
								background: #d63040;
								color: #ffe7c7;
								font-size: 12px;
								line-height: 20px;
								text-align: center;

							}

						}

					}

					.buy {
						width: 70px;
						height: 30px;
						font-size: 12px;
						line-height: 30px;
						color: #ffe7c7;
						border-radius: 13px;
						margin-right: 20px;
						margin-bottom: 10px;
						background: #d63040;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}


				.full__reduction {
					width: 300px;
					margin-top: 30px;
					margin-left: 30px;
					color: #ffe7c7;



					.full__reduction__name {
						font-size: 24px;
						font-weight: 700;
					}

					.limitation {
						font-size: 14px;
						line-height: 14px;
						margin-top:5px;
					}

				}

				.image {
					height: 100%;
					position: absolute;
					top: 0px;
					right: 0px;
					margin-left: 10px;

					div {
						position: absolute;
						left: 4px;
						top: 37px;
						font-size: 20px;
						width: 90px;
						color: #c53f45;
						height: 90px;
						flex-direction: column;
					}
				}
			}

			.imgCover {
				vertical-align: bottom;
				object-fit: cover;
				background-size: 100% 100%;
				background-color: #ff0000;
			}

		}
		
        .imgss{
			width:100px;
			height: 100%;
			position: absolute;
			top: 0px;
			right: 15px;
			margin-left: 10px;
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			
			div {
				position: absolute;
				left: 0px;
				top: 14px;
				font-size: 12px;
				line-height: 12px;
				display: flex;
				align-items: center;
				justify-content: center;
				width:78px;
				color: #c53f45;
				height: 90px;
				flex-direction: column;
				span{
					display: block;
				}
			}
		}
		.meToShares {
			font-size: 26px;
			width: 70px;
			height: 70px;
			border-radius: 50%;
			color: #c53f45;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			vertical-align: bottom;
		}
	}
</style>