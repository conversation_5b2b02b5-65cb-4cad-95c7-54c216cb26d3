<!-- 2025-7-16 -->
<template>
	<div>
		<!-- 顶部搜索条件 -->
		<Search @searchBy="getSearch" ref="Search" :status="chooseStatus" :searchTypeProp="searchType"></Search>
		<!-- <el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane v-for="(item, index) in list" :key="index" :label="item.modeName" :name="item.modeName"
				:status="item.status"></el-tab-pane>
		</el-tabs> -->
		<!-- <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="生效中" name="104"> </el-tab-pane>
      <el-tab-pane label="未生效" name="101"> </el-tab-pane>
      <el-tab-pane label="已失效" name="300"> </el-tab-pane>
      <el-tab-pane label="草稿" name="0"></el-tab-pane>
      <el-tab-pane label="停止" name="400"> </el-tab-pane>
      <el-tab-pane label="全部" name="-1"></el-tab-pane>
    </el-tabs> -->
		<div class="topLine">
			<div class="topLine__left">
				<!-- 创建优惠券 -->
				<!-- <SetClassify @commandsx="commandValsx" ref="setClass" :goodIds="chooseStatus" style="margin-left: 20px"
					:is-item="false" :is-value="false"/> -->
			</div>
		</div>
		<!-- 商品列表 -->
		<PagingList @goodId="getGoodId" ref="pagingList" @getShowProList="getShowProList" :changeId="chooseStatus">
		</PagingList>
		<!-- 设置分类 -->
		<div class="listBottom">
			<PageManage :pageSize="pageSize" :pageNum="pageNum" :total="total" @handleSizeChange="handleSizeChange"
				@handleCurrentChange="handleCurrentChange" style="margin-top: 0px"></PageManage>
		</div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Ref } from "vue-property-decorator";
import Search from "./components/Search.vue";
import PagingList from "./components/pagingList.vue";
import SetClassify from "./components/goodsComp/SetClassify.vue";
import { SearchKeyType } from "./components/searchType";
import PageManage from "@/components/PageManage.vue";
import { GoodDetailInfo } from "./goodType";
@Component({
	components: {
		Search,
		SetClassify,
		PagingList,
		PageManage
	}
})
export default class ListApart extends Vue {
	// @Prop({})
	// chooseStatus!: string;
	// @Watch("chooseStatus")
	// getNewModeId() {
	//     this.status = this.chooseStatus;
	//     console.log('获取商品数组信息',this.changeId,this.chooseStatus);

	// }
	/** 获取商品数组信息 */
	@Ref()
	readonly pagingList!: any;
	@Ref()
	readonly Search!: any;

	status = "";
	searchType: any = {};
	pageSize = 0;
	pageNum = 0;
	total = 0;
	// 状态:100->未生效;101->已生效;104->已过期;200->驳回;300->终止
	list: any = [{ modeName: '已生效', status: 101 },
	{ modeName: '未生效', status: 100 },
	{ modeName: '已过期', status: 104 },
	{ modeName: '已停用', status: 300 },
	{ modeName: '全部', status: '' }
	]
	activeName = '-1';
	chooseStatus: any = null;
	/**
	 * 顶部专区选择
	 */
	handleClick(tab: { index: number }) {
		this.chooseStatus = this.list[tab.index].status || '';
	}

	handleTabClick({ name: orderStatus }: { name: string }) {
    if (orderStatus != "-1") {
			this.chooseStatus = orderStatus
    } else {
			this.chooseStatus = ''
    }

		this.pagingList.query.status = this.chooseStatus;
		this.pagingList.getShopFullDonation();
  }
	/**
	 * 获取搜索条件
	 */
	getSearch(data: SearchKeyType) {
		this.pagingList.query = data;
		// this.searchType.current = 1;
		console.log('获取搜索条件', data);

		// 缓存搜索条件
		this.getPagingList();
	}
	/** 展示分类获取已选择的分类  */
	getShowProList(data: GoodDetailInfo[]) {
		this.showGetList = data || [];
		// this.pagingList = this.$refs.pagingList as GoodsList;
		this.total = this.pagingList.total;
		// console.log('fffff',this.total);
		this.pageSize = this.pagingList.searchType.size as number;
		this.pageNum = this.pagingList.searchType.current as number;
	}
	/** 根据选择批量操作 */
	commandValsx(val: string, sortcommand: string) {
		let types: any = {}
		types[sortcommand] = val;
		this.searchType = types;
		for (let key in this.pagingList.searchType) {
			if (key == 'endTimeSort' || key == 'priceSort' || key == 'startTimeSort' || key == 'useableTimesSort') {
				this.$delete(this.pagingList.searchType, key)
			}
		}
		console.log('0000', this.pagingList.searchType);
		this.getPagingList();

	}
	/**
	 * 获取商品列表
	 */
	getPagingList() {
		
		this.pagingList.getShopFullDonation();
	}
	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.pagingList.query.size = val;
		this.Search.form.size = val;
		this.pagingList.getShopFullDonation();
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.pagingList.query.current = val;
		this.Search.form.current = val;
		console.log('当前页', val);

		this.pagingList.getShopFullDonation();
	}
	/**
	 * 获取选中商品ids数组
	 */
	getGoodId(data: string[]) {
		// this.goodIds = data;
	}
}

</script>

<style></style>
