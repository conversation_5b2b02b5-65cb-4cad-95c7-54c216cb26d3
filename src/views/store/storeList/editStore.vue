<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-01 15:47:16
-->
<template>
	<div class="goodForm">
		<keep-alive>
			<el-form :model="formModel" ref="formModel" label-width="100px">

				<div class="baseMsg">
					<el-form-item label="商家名称" prop="name">
						<el-input v-model="formModel.name" style="width: 550px;text-align: center;"
							placeholder="请输入商家名称"></el-input>
					</el-form-item>
					<el-form-item label="统一社会信用代码" prop="shopCode" label-width="126px">
						<el-input v-model="formModel.shopCode" style="width: 524px" maxlength="30"
							placeholder="请输入统一社会信用代码"></el-input>
					</el-form-item>
					<el-form-item label="联系人" prop="contacts">
						<el-input v-model="formModel.contacts" style="width: 550px;" placeholder="请输入联系人"></el-input>
					</el-form-item>
					<el-form-item label="联系人电话" prop="phone">
						<el-input v-model="formModel.phone" style="width: 550px;" placeholder="请输入联系人电话"></el-input>
					</el-form-item>
					<el-form-item label="商户地区" prop="ticketName">
						<div style="display: flex;align-items: center;">
							<div class="province__city">
								{{ formModel.provinceName }}{{ formModel.cityName }}{{ formModel.areaName }}
							</div>
							<el-button size="small" type="primary" style="margin-left: 10px;"
								@click="updateAddress2">修改地址</el-button>

						</div>
						<!-- <el-input style="width: 550px;" placeholder="请输入商户地区">{{formModel.ticketName}}</el-input> -->
					</el-form-item>
					<el-form-item label="详细地址" prop="address">
						<el-input v-model="formModel.address" style="width: 550px;" placeholder="请输入详细地址"></el-input>
					</el-form-item>
					<el-form-item label="门牌号" prop="houseNumber">
						<el-input v-model="formModel.houseNumber" style="width: 550px;"
							placeholder="请输入详细地址"></el-input>
					</el-form-item>
					<el-form-item label="所属分类" prop="categoryName">
						<el-select v-model="formModel.shopsCategoryIds" style="width: 550px" multiple
							placeholder="请选择分类" :popper-append-to-body="false">
							<el-option-group v-for="group in temShowList" :key="group.shopsCategoryId"
								:label="group.name">
								<el-option v-for="item in group.shopsCategoryVos" :key="item.shopsCategoryId"
									:label="item.name" :value="item.shopsCategoryId"></el-option>
							</el-option-group>
						</el-select>
						<!-- <el-cascader clearable v-model="formModel.categoryName" style="width:550px;" :props="props"
							:options="options"  @change="handleChange"></el-cascader> -->
					</el-form-item>
					<el-form-item class="business__license" label="营业执照" prop="ticketName">
						<div style="display: flex;justify-content: flex-start;">
							<div v-if="formModel.businessLicense" class="demo-image__preview">
								<el-image :src="formModel.businessLicense" :preview-src-list="srcLicenseList">
								</el-image>
								<span
									style="font-size: 12px;color: #ccc;position: absolute;bottom: 0px;margin-left: 10px;">点击图片可预览</span>
							</div>
							<div v-else class="noImg"></div>
							<el-upload v-if="previousPage != 'entryList'" class="upload-demo" action
								:on-change="uploadLicenses" :auto-upload="false" :show-file-list='false'>
								<el-button size="small" type="primary">点击上传</el-button>

							</el-upload>
						</div>
					</el-form-item>
					<el-form-item class="business__license" label="店铺logo" prop="ticketName">
						<div style="display: flex;justify-content: flex-start;">
							<div v-if="formModel.logo" class="demo-image__preview">
								<el-image :src="formModel.logo" :preview-src-list="srcLicenseList">
								</el-image>
								<span
									style="font-size: 12px;color: #ccc;position: absolute;bottom: 0px;margin-left: 10px;">点击图片可预览</span>
							</div>
							<div v-else class="noImg"></div>
							<el-upload v-if="previousPage != 'entryList'" class="upload-demo" action
								:on-change="uploadLogo" :auto-upload="false" :show-file-list='false'>
								<el-button size="small" type="primary">点击上传</el-button>

							</el-upload>
						</div>
					</el-form-item>
					<el-form-item label="法人身份证" prop="ticketName">
						<div class="flex__corporate">
							<div class="corporate">
								<div class="ID__card">
									<div v-if="formModel.cardIdUp">
										<el-image :src="formModel.cardIdUp" :preview-src-list="cardList">
										</el-image>
									</div>
									<!--  -->
									<!-- <el-image :src="formModel.cardIdUp" :preview-src-list="srcLicenseList">
									</el-image> -->
									<div v-else></div>
								</div>
								<div class="center__img">
									<div>正面</div>
									<el-upload v-if="previousPage != 'entryList'" class="upload-demo__xia" action
										:auto-upload="false" :on-change="uploadFront" :show-file-list='false'>
										<el-button size="small" type="primary">点击上传</el-button>

									</el-upload>
								</div>

							</div>

							<div class="corporate">
								<div class="ID__card">
									<el-image v-if="formModel.cardIdDown" :src="formModel.cardIdDown"
										:preview-src-list="cardList">
									</el-image>
									<span v-if="formModel.cardIdDown"
										style="font-size: 12px;color: #ccc;">点击图片可预览</span>
									<div v-else></div>
									<!-- <el-button type="primary" :preview-src-list="srcList">预览</el-button> -->
								</div>
								<div class="center__img2">
									<div>反面</div>
									<el-upload v-if="previousPage != 'entryList'" class="upload-demo__xia" action
										:auto-upload="false" :on-change="uploadOpposite" :show-file-list='false'>
										<el-button size="small" type="primary">点击上传</el-button>

									</el-upload>
								</div>

							</div>

						</div>

					</el-form-item>
					<!-- 	<el-form-item label="驳回原因" prop="ticketName">
						<div>
							资料填写不完成，未通过审核。
						</div>
						</el-form-item> -->

					<el-form-item style="margin-top: 50px;margin-left: 150px;">
						<el-button @click="editSecUnit">取 消</el-button>

						<el-button v-if="previousPage == 'entryList'" type="danger"
							@click="submitForm(formModel)">驳回</el-button>
						<el-button type="primary" @click="submitForm(formModel)">提交</el-button>
						<!-- <el-button type="primary" @click="showRegion = false">立即创建</el-button> -->
					</el-form-item>
				</div>
			</el-form>
		</keep-alive>
		<el-dialog :visible.sync="addDialog" width="580px" :before-close="handleClose">
			<div slot="title" class="digTitle">
				修改商户地区
			</div>
			<AddModel ref="addModel" :currentItem="currentItem" :addDialog="addDialog"></AddModel>
			<span slot="footer" class="dialog-footer">
				<el-button @click="handleClose">取 消</el-button>
				<el-button type="primary" @click="sureAdd">确认</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Ref } from "vue-property-decorator";
import { AddSubmitFormType } from "./newGoodType";
import DateUtil from "@/store/modules/date";
import { getAllShops } from "@/api/certificateApi/certificateApi"
import { ShopsPartnerId, editShopsPartner } from "@/api/storeApi/storeApi"
import { ElUploadInternalFileDetail } from "element-ui/types/upload";
import { upLoad } from "@/api/index";
import AddModel from "./components/edit/AddModel.vue";
import { updateAddress } from "@/api/order";
// import tonghuiCertificate from '../components/'
@Component({
	components: {
		// tonghuiCertificate
		AddModel,
	}
})
export default class NewGoodFormOpints extends Vue {
	@Prop({})
	from!: string;
	@Ref()
	readonly addModel!: AddModel;
	@Watch('formModel')
	formModelas() {
		console.log('eeeeeww', this.formModel);

	}
	color1 = '#409EFF'

	/** 添加弹窗 */
	addDialog = false;
	/** 编辑内容 */
	currentItem: any = {};
	// 存放数据的对象
	formModel = {} as Partial<AddSubmitFormType>;
	commodityCode = "";
	createTime = ''
	showRegion = false;
	shopTicketList = []
	srcLicenseList: Array<string> = []
	cardListUp = []
	cardList = [
		'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
		'https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg'
	]
	temShowList = [];
	props = {
		// expandTrigger: 'hover',
		multiple: true,
		emitPath: false,
		checkStrictly: false,
		value: 'shopsCategoryId',//指定选项的值为选项对象的某个属性值
		label: 'name',//指定选项标签为选项对象的某个属性值
		children: 'shopsCategoryVos',//指定选项的子选项为选项对象的某个属性值

	}
	// url = ''
	url = ''
	srcList = []
	FrontOfIDCard = []
	previousPage = ''
	// @Ref()
	// readonly componentRef!: HTMLFormElement;


	mounted() {
		// this.$nextTick(() => {
		//     this.getGoodDetail();

		// })
		if (this.$route.params.page == 'entryList') {
			this.previousPage = 'entryList'
		}
		this.getShopsPrompt();
		this.getTemShowList()


	}
	updateAddress2() {
		this.addDialog = true;
		// console.log("this.addModel.addressOption",this.addModel);
		// this.addModel.addressOption.province = this.formModel.provinceName;
		// this.addModel.addressOption.city =this.formModel.cityName;
		// this.addModel.addressOption.country =this.formModel.areaName;
	}
	/**
  * 添加地址
  */
	sureAdd() {
		const passFlag = this.saveAddress(this.addModel);
		if (!passFlag) {
			return;
		}
		this.addModel.addressOption.countryId = this.addModel.countryId;
		console.log("this.addModel.addressOption", this.addModel.addressOption);

		// {{ formModel.provinceName }}{{ formModel.cityName }}{{ formModel.areaName }}
		this.formModel.provinceName = this.addModel.addressOption.province;
		this.formModel.cityName = this.addModel.addressOption.city;
		this.formModel.areaName = this.addModel.addressOption.country;

		this.formModel.provinceCode = this.addModel.addressOption.provinceId;
		this.formModel.cityCode = this.addModel.addressOption.cityId;
		this.formModel.areaCode = this.addModel.addressOption.countryId;

		this.handleClose();
	}
	handleClose() {
		this.currentItem = {};
		this.addDialog = false;
	}

	/**
  * 保存地址
  */
	saveAddress(addModel: any) {
		if (
			!addModel.addressOption.cityId ||
			!addModel.countryId ||
			!addModel.addressOption.provinceId
		) {
			this.$message.error("请输入地区信息");
			return false;
		}
		return true;
	}
	dialogShow() {
		getAllShops({}).then((res) => {
			// this.options = res.data
			this.options = JSON.parse(JSON.stringify(res.data)) || [];
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/**
* 获取展示分类
*/
	async getTemShowList() {
		// const param = {
		//   saleMode: this.saleMode
		// };
		const { data } = await getAllShops({});
		this.temShowList = JSON.parse(JSON.stringify(data)) || [];
	}
	handleChange(value: any) {
		this.formModel.categoryName = value[1]
		console.log('vxcv', value);
	}
	/**
   * 选择商品属性
   */
	selectTemAttsList(temAttsId: number) {
		console.log("selectTemAttsList", temAttsId);
	}
	// 选择日期
	chooseStartTime(data: any) {
		this.formModel.startTime = data ? this.dateConversion(data) : "";

	}
	chooseEndTime(data: any) {
		this.formModel.endTime = data ? this.dateConversion(data) : "";

	}
	/**
	 * 开始时间
	 * @param data 
	 */
	chooseTime(data: any) {

		this.formModel.displayStartTime = data ? this.dateConversion(data) : "";
	}
	/**
	 * 结束时间
	 * @param data 
	 */
	chooseTimeEad(data: any) {
		this.formModel.displayEndTime = data ? this.dateConversion(data) : "";

		console.log('时间', this.dateConversion(data));

	}

	dateConversion(value: Date) {
		const date = new DateUtil("").getYMDHMSs(value);
		console.log('时间', date);

		return date;
	}



	// 提交信息表单
	submitForm(formName: string) {

		editShopsPartner(this.formModel).then((res) => {
			this.$message.success(res.data)
			this.$router.go(-1)
			// console.log('11111111111111', formName,res);
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/**
* 确定退出发布积分商品页面
*/

	editSecUnit() {
		console.log('未保存的信息将不会保留');
		this.$router.go(-1)

		// this.$confirm(
		//     `确定退出发布积分商品页面?退出后，未保存的信息将不会保留!`,
		//     "提示",
		//     {
		//         confirmButtonText: "确定",
		//         cancelButtonText: "取消",
		//         type: "warning",
		//     },
		// ).then(() => {
		//     this.$message({
		//     type: 'success',
		//     message: '成功!'
		//   });
		//     this.$router.go(-1)
		// });


	}

	uploadFront(file: ElUploadInternalFileDetail) {
		this.addUploadProductImg(file, 2)
	}
	uploadOpposite(file: ElUploadInternalFileDetail) {
		this.addUploadProductImg(file, 3)
	}
	/**
* 上传图片
*/
	async uploadLogo(file: ElUploadInternalFileDetail) {
		const whiteList = ["image/jpeg", "image/jpg", "image/png"];

		const isLt1M = file.size < 1 * 1024 * 1024;
		if (!whiteList.includes(file.raw.type)) {
			this.$message.error("上传文件只能是 JPG或PNG 格式!");
			return;
		}
		if (!isLt1M) {
			this.$message.error("上传文件大小不能超过 1MB!");
			return;
		}

		const res = await upLoad({
			file: file.raw
		});

		this.$set(this.formModel, 'logo', res.data)
		this.$set(this.srcLicenseList, 0, res.data)
		console.log('mmmmmmmmmss', this.formModel.logo);
		// this.FrontOfIDCard[0]=res.data
		// this.url=res.data
	}
	/**
* 上传图片
*/
	async uploadLicenses(file: ElUploadInternalFileDetail) {
		const whiteList = ["image/jpeg", "image/jpg", "image/png"];

		const isLt1M = file.size < 1 * 1024 * 1024;
		if (!whiteList.includes(file.raw.type)) {
			this.$message.error("上传文件只能是 JPG或PNG 格式!");
			return;
		}
		if (!isLt1M) {
			this.$message.error("上传文件大小不能超过 1MB!");
			return;
		}

		const res = await upLoad({
			file: file.raw
		});

		this.$set(this.formModel, 'businessLicense', res.data)
		this.$set(this.srcLicenseList, 0, res.data)
		console.log('mmmmmmmmmss', this.formModel.businessLicense);
		// this.FrontOfIDCard[0]=res.data
		// this.url=res.data
	}
	/**
	 * 上传图片
	 */
	async addUploadProductImg(file: ElUploadInternalFileDetail, val: number) {
		const whiteList = ["image/jpeg", "image/jpg", "image/png"];

		const isLt1M = file.size < 1 * 1024 * 1024;
		if (!whiteList.includes(file.raw.type)) {
			this.$message.error("上传文件只能是 JPG或PNG 格式!");
			return;
		}
		if (!isLt1M) {
			this.$message.error("上传文件大小不能超过 1MB!");
			return;
		}

		const res = await upLoad({
			file: file.raw
		});


		if (val == 2) {
			this.$set(this.formModel, 'cardIdUp', res.data)
			this.$set(this.cardList, 0, res.data)
		}
		else if (val == 3) {
			this.$set(this.formModel, 'cardIdDown', res.data)
			this.$set(this.cardList, 1, res.data)
		}
		console.log('wwwwwww', val, this.cardList, res.data);
		// this.FrontOfIDCard[0]=res.data
		// this.url=res.data
	}

	/**
	 * 获取商品详情
	 */
	async getShopsPrompt() {
		const goodId = this.$route.query.id || this.$route.params.id;
		if (!goodId) {
			return;
		}
		const { data } = await ShopsPartnerId({ 'id': goodId });
		this.formModel = data
		this.srcLicenseList[0] = data.businessLicense
		this.cardList[0] = data.cardIdUp
		this.cardList[1] = data.cardIdDown
		// this.formModel.name = data.name
		// this.formModel.goodsCode = data.goodsCode
		// // this.formModel.attributeId=data.name
		// this.formModel.unitId = data.unit
		// this.formModel.time = data.name
		// this.formModel.points = data.points
		// this.formModel.price = data.place
		// this.formModel.num = data.name
		// this.formModel.limitationPeople = data.name
		// this.formModel.notes = data.name
		console.log('获取商品详情', data);

	}
}
</script>

<style lang="scss">
@import "@/assets/styles/goods/index.scss";

.province__city {
	width: 550px;
	height: 30px;
	background-color: #FFFFFF;
	background-image: none;
	border-radius: 4px;
	border: 1px solid #DCDFE6;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	color: #606266;
	text-align: center;
	font-size: inherit;
}

.el-input__inner {
	text-align: center;
}

.business__license {

	height: 220px;

	.demo-image__preview {

		// width: 560px;
		// text-align: center;
		.el-image {
			width: 550px;
			height: 220px;
		}
	}


}

.noImg {
	width: 550px;
	height: 220px;
	border: 1px solid rgb(187, 187, 187);
}

.flex__corporate {
	display: flex;
	justify-content: flex-start;

	.corporate {

		// background-color: aqua;
		// margin-left: 10px;
		// display: flex;
		// flex-direction: column;
		// align-items: center;
		.center__img {
			width: 270px;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.center__img2 {
			width: 270px;
			display: flex;
			flex-direction: column;
			align-items: center;
			// margin-top: -12px;
		}

		.ID__card {
			height: 220px;

			// background-color: aqua;
			.el-image {
				width: 270px;
				margin-right: 10px;
				height: 220px;
			}

			div {
				width: 270px;
				margin-right: 10px;
				height: 220px;
				border: 1px solid rgb(187, 187, 187);

			}
		}
	}
}

// .upload {
// 	display: flex;
// 	align-items: center;
// 	flex-direction: column;
// 	margin-left: 10px;

.upload-demo {
	// height: 200px;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: flex-end;
	margin-left: 10px;
	margin-bottom: 40px;
}

.upload-demo__xia {
	// margin-top: 160px;
	// margin-left: -85px;
}

// .upload-demo {
// 	margin-top: 160px;
// 	margin-left: -85px;
// }
// }

.useableTimes__ci {
	margin-left: 10px;
}

.goodForm::-webkit-scrollbar {
	display: none;
}

.w-e-text-container {
	height: 532px !important;
	/*!important是重点，因为原div是行内样式设置的高度300px*/
}
</style>