<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:47:50
-->
<template>
  <!-- 设置分类 -->
  <div class="all">

    <el-button type="primary" @click="importShops" v-if="isSupper||batchImportButton">批量导入</el-button>
    <el-button type="primary" style="margin-left: 10px" @click="exportData">导出列表</el-button>
    <el-dialog :visible.sync="uploadDialog" width="650px">
      <div slot="title" class="diaTitle">上传文件</div>
      <ImportDialog @close="closeDialog" ref="ImportDialog"></ImportDialog>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop,Ref} from "vue-property-decorator";
import ImportDialog from "./ImportDialog";
import { exportShopsPartner } from "@/api/storeApi/storeApi";

@Component({
  components: {
    // GoodCategory,
    ImportDialog
  },
})
export default class SetClassifyS extends Vue {
  name = "SetClassifyS";
  	/** 获取商品数组信息 */
	@Ref()
	readonly ImportDialog!: ImportDialog;

  @Prop()
  goodIds!: number[];

  uploadDialog = false;

  csvName='NO'

  menuName = "商家信息";

  batchImportButtonCode = "storeList.batchImport";

  batchImportButton = false;

  buttonList = [];

  isSupper = 0;

  mounted() {
    this.buttonAuth();
  }

  buttonAuth() {

    this.isSupper = this.$STORE.userStore.userInfo.isSupper
    
    console.log('页面错误原因',this.$STORE.userStore.userInfo.authMenuButtonVos);
    
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter((i:any) => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

    var batchImportButtonData = buttonList.find(e => e == this.batchImportButtonCode);

    if (batchImportButtonData != null && batchImportButtonData != undefined) {
      this.batchImportButton = true;
    }

  }
  /**
   * 导入商家
   */
  importShops() {
    this.uploadDialog = true;
    if(this.ImportDialog){
      this.ImportDialog.csvName=''
    }
    
    
  }

  /**
   * 关闭弹窗
   */
  closeDialog() {
    this.uploadDialog = false;
  }

  /**
   * 导出数据
   */
  exportData() {
    this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 获取搜索参数
      const searchParams = { ...this.$parent.searchType };

      // 删除请求参数中的空值
      const params = {};
      for (const key in searchParams) {
        if (searchParams[key] !== '' && searchParams[key] !== null && searchParams[key] !== undefined) {
          params[key] = searchParams[key];
        }
      }

      exportShopsPartner(params).then((res) => {
        var blob = new Blob([res.data], {
          type: "application/x-msdownload;charset=UTF-8",
        });
        // 创建一个blob的对象链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        // 把获得的blob的对象链接赋值给新创建的这个 a 链接
        let now = new Date();
        let timestamp = now.getTime();
        link.setAttribute('download', '商家信息_' + timestamp + '.xls'); // 设置下载文件名
        document.body.appendChild(link);

        // 触发下载
        link.click();
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$message.success('导出成功');
      }).catch((err) => {
        this.$message.error("导出失败: " + err);
      });
    }).catch(() => {
      // 用户取消导出
    });
  }

}
</script>

<style lang="scss">
@import "@/assets/styles/mixins/mixins.scss";

.all {
  display: flex;
  justify-content: flex-start;

}
</style>
