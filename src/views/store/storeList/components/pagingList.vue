<template>
	<div style="margin-top: 20px">
		<m-table :data.sync="ticketList" :checked-item.sync="tableCheckedItem" slot="content" class="imgView">
			<m-table-column prop="name" label="商家名称" :showsSlection="true" width="220">
				<template v-slot="{ row }">
					<div class="goodList">
						{{ row.name }}
					</div>
				</template>
			</m-table-column>
			<m-table-column prop="contacts" label="联系人" width="90">
				<template v-slot="{ row }">
					<span>{{ row.contacts }}</span>
				</template>
			</m-table-column>

			<m-table-column prop="phone" label="联系人电话" width="100">
				<template v-slot="{ row }">
					<span>{{ row.phone }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="useableTimes" label="所属分类" width="140">
				<template v-slot="{ row }">
					<el-tooltip class="item" effect="dark" :content="row.categoryName" placement="left-start">
						<div class="nowrd" style="text-align: center;">
							{{ row.categoryName }}
						</div>
					</el-tooltip>
				</template>
			</m-table-column>
			<m-table-column prop="startTime" label="加入时间" width="90">
				<template v-slot="{ row }">
					<span>{{ row.createTime }}</span>
				</template>
			</m-table-column>
      <m-table-column prop="mainFlag" label="主店铺"  width="90">
        <template v-slot="{ row }">
          <span v-if="row.mainFlag == 1" style="color: #23c910 ;">是</span>
          <span v-else >否</span>
          <!-- <span v-else-if="row.projectStatus==5">进行中</span> -->
        </template>
      </m-table-column>
			<!-- 	string禁用状态 0正常 1 禁用 -->
			<m-table-column prop="prohibitStatus" label="状态"  width="90">
				<template v-slot="{ row }">
					<span v-if="row.prohibitStatus == 0" style="color: #23c910 ;">启用</span>
					<span v-else-if="row.prohibitStatus == 1" style="color: #ff0000 ;">停用</span>
					<!-- <span v-else-if="row.projectStatus==5">进行中</span> -->
				</template>
			</m-table-column>
			<m-table-column prop="userName" label="操作"  width="90">
				<template v-slot="{ row }">
					<div class="center">
						<!-- <el-button type="primary" size="mini" round>...</el-button> -->
						<set-drop v-if="isSupper||editButton" setName="编辑" @setClick="edit(row)" :dropdownList="itemDropList(row)"
							@command="getDropdown($event, row)" />
						<set-drop v-else setName=""  :dropdownList="itemDropList(row)"
							@command="getDropdown($event, row)" />
					</div>
				</template>
			</m-table-column>
		</m-table>
		<!-- 新增子用户 模态框 -->
		<el-dialog title="新增用户" :visible.sync="addUserFlag" width="30%">
			<!-- <template v-for="item in userList" :key="item.tagId"> -->
			<el-form :model="addForm" ref="addForm" label-width="100px">

				<el-form-item label="用户名称" prop="nikeName" :rules="[
					{ required: true, message: '用户名称不能为空' }
				]">
					<el-input v-model="addForm.nikeName" autocomplete="off"></el-input>
				</el-form-item>
				<el-form-item label="手机号" prop="phone" :rules="[
					{ required: true, message: '手机号不能为空' }
				]">
					<el-input v-model="addForm.phone"></el-input>
				</el-form-item>

				<el-form-item label="密码" prop="passwd" :rules="[
					{ required: true, message: '密码不能为空' }
				]">
					<el-input type="password" v-model="addForm.passwd"></el-input>
				</el-form-item>

				<el-form-item label="状态">
					<el-select v-model="addForm.forbidStatus" placeholder="请选择">
						<el-option v-for="item in statusList" :key="item.value" :label="item.key" :value="item.value">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="角色">
					<el-select v-model="addForm.roleIds" multiple placeholder="请选择">
						<el-option v-for="item in optionList" :key="item.value" :label="item.label" :value="item.value">
						</el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<!-- </template> -->
			<span slot="footer" class="dialog-footer">
				<el-button @click="addUserFlag = false;">取 消</el-button>
				<el-button type="primary" @click="addHandler()">确 定</el-button>
			</span>
		</el-dialog>
		<!-- <el-collapse accordion>
		  <el-collapse-item class="a11">
		    <template slot="title">
		      一致性 Consistency
		    </template>
		    <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
		    <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div>
		  </el-collapse-item>
		  <el-collapse-item title="反馈 Feedback">
		    <div>控制反馈：通过界面样式和交互动效让用户可以清晰的感知自己的操作；</div>
		    <div>页面反馈：操作后，通过页面元素的变化清晰地展现当前状态。</div>
		  </el-collapse-item>
		  <el-collapse-item title="效率 Efficiency">
		    <div>简化流程：设计简洁直观的操作流程；</div>
		    <div>清晰明确：语言表达清晰且表意明确，让用户快速理解进而作出决策；</div>
		    <div>帮助用户识别：界面简单直白，让用户快速识别而非回忆，减少用户记忆负担。</div>
		  </el-collapse-item>
		  <el-collapse-item title="可控 Controllability">
		    <div>用户决策：根据场景可给予用户操作建议或安全提示，但不能代替用户进行决策；</div>
		    <div>结果可控：用户可以自由的进行操作，包括撤销、回退和终止当前操作等。</div>
		  </el-collapse-item>
		</el-collapse> -->
	</div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import { SearchKeyType } from "./searchType";
import SetDrop from "./goodsComp/SetDrop.vue";
import { ApiSkuType, GoodDetailInfo } from "../goodType";
import { getShopList, startStore, stopStore, SetMainStore } from "@/api/storeApi/storeApi"
import { getRoleList, addAccountInfo } from "@/api/platformUser/platformUser";
// import SetDrop from "@/views/customer/common/SetDrop.vue";
@Component({
	components: {
		SetDrop
	}
})
export default class pagingList extends Vue {
	@Prop({})
	changeId!: string;

	@Watch("changeId")
	getSaleMode() {
		this.searchType.prohibitStatus = this.changeId;
		console.log('bbbbbbbb', this.searchType, this.changeId);

		this.getPageList();
	}
	searchType = {
		current: 1,
		size: 10
	} as SearchKeyType;
	ticketList = []
	total = 0;
	tableCheckedItem = [];
	//主店铺新增弹出框
	addUserFlag = false;
	//新增弹出框
	addForm = {}
	//角色选项
	optionList = [];
	//状态选项
	statusList = [
		{ key: '正常', value: 0 },
		{ key: '禁用', value: 1 },
	];
	submitFlag = true;


	menuName = "商家信息";

	buttonList = [];

	isSupper = 0;

	editButtonCode = "storeList.edit";

	editButton = false;

	enableButtonCode = "storeList.enable";

	enableButton = false;

	deactivateButtonCode = "storeList.deactivate";

	deactivateButton = false;


	mainStoreButtonCode = "storeList.mainStore";

	mainStoreButton = false;

	addUserButtonCode = "storeList.addUser";

	addUserButton = false;


	get itemDropList() {
		return (row: GoodDetailInfo) => {
			return [
				{
					text: "启用",
					command: "enable",
					show: (row.prohibitStatus != 0)&&(this.isSupper||this.enableButton),
					disabled: false
				},
				{
					text: "停用",
					command: "deactivate",
					show: (row.prohibitStatus != 1)&&(this.isSupper||this.deactivateButton),
					disabled: false
				},
				{
					text: "设置为主店铺",
					command: "mainStore",
					show: this.isSupper||this.mainStoreButton,
					disabled: false
				},
				{
					text: "添加主账号",
					command: "addUser",
					show: (row.accountMainNum == 0)&&(this.isSupper||this.addUserButton),
					disabled: false
				}
			];
		};
	}
	mounted() {
		this.searchType.prohibitStatus = this.changeId;
		// console.log("进入到goodlist",);
		//  加载搜索缓存
		var cache = JSON.parse(
			sessionStorage.getItem("cache_storeListLog_search_form") || "{}"
		);

		console.log('获取商品列表查询参数11', this.searchType, cache);
		this.searchType = Object.assign(this.searchType, cache) as SearchKeyType;

		this.getPageList();

		this.buttonAuth();

	}

	buttonAuth() {
		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var editButtonData = buttonList.find(e => e == this.editButtonCode);

		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}

		var enableButtonData = buttonList.find(e => e == this.enableButtonCode);

		if (enableButtonData != null && enableButtonData != undefined) {
			this.enableButton = true;
		}

		var deactivateButtonData = buttonList.find(e => e == this.deactivateButtonCode);

		if (deactivateButtonData != null && deactivateButtonData != undefined) {
			this.deactivateButton = true;
		}

		var mainStoreButtonData = buttonList.find(e => e == this.mainStoreButtonCode);

		if (mainStoreButtonData != null && mainStoreButtonData != undefined) {
			this.mainStoreButton = true;
		}

		var addUserButtonData = buttonList.find(e => e == this.addUserButtonCode);

		if (addUserButtonData != null && addUserButtonData != undefined) {
			this.addUserButton = true;
		}

	}

	getPageList() {
		// 删除请求链接里面的空值
		for (const key in this.searchType) {
			if (!this.searchType[key]) {
				this.$delete(this.searchType, key)
			}
		}
		// console.log('4555555555555',this.searchType);
		getShopList(this.searchType).then((res) => {
			this.ticketList = res.data.list;
			this.total = res.data.total;
			this.$emit("getShowProList", this.ticketList);
			console.log('eeeeeeeee获取商品列表e', this.total, res.data.list);
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/**
	 * 获取下拉框
	 */
	getDropdown(val: string | number, row: GoodDetailInfo) {
		// if (Number(val) > 9) {
		console.log('获取下拉框', val, ',', row);
		if (val == 'enable') {
			this.getGoEnable(row)
			this.getPageList();
		}
		if (val == 'deactivate') {
			this.goStopStore(row)
			this.getPageList();
		}
		if (val == 'mainStore') {
			this.goSetMainStore(row)
			this.getPageList();
		}
		if (val == 'addUser') {
			this.addUser(row);
		}
	}
	addUser(row: GoodDetailInfo) {
		this.addForm = {
			nikeName: '',
			phone: '',
			passwd: '',
			forbidStatus: 0,
			roleIds: '',
			shopId: row.shopId,
			accountType: 0,
		}
		this.addUserFlag = true
		this.getRoleList();
	}
	//用户新增的提交方法
	addHandler() {
		if (this.submitFlag) {
			this.submitFlag = false;
			addAccountInfo(this.addForm).then(res => {
				this.$message.success("新增成功");
				// 关闭模态框
				this.addUserFlag = false;
				this.submitFlag = true;
				// 提交数据成功，重新获取一次数据进行渲染
				this.getPageList();
			}).catch(err => {
				this.submitFlag = true;
				this.$message.error(err || "网络错误");
			});
		}
	}
	getRoleList() {
		getRoleList({}).then(res => {
			let optionList = [];
			res.data.forEach(element => {
				let option = {};
				option.value = element.id
				option.label = element.roleName
				optionList.push(option);
			});
			this.optionList = optionList
		})
	}
	/**启用*/
	getGoEnable(row: GoodDetailInfo) {
		console.log('eeee', row.id);
		startStore(row.id).then((res) => {
			this.$message.success(res.msg || '启用成功')
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/**停用商家*/
	goStopStore(row: GoodDetailInfo) {
		console.log('eeee', row.id);
		stopStore(row.id).then((res) => {
			this.$message.success(res.msg || '停用成功')
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/**设为主店铺*/
	goSetMainStore(row: GoodDetailInfo) {
		console.log('eeee', row.id);
		SetMainStore(row.id).then((res) => {
			this.$message.success(res.msg || '设为主店铺成功')
		}).catch((err) => {
			this.$message.error(err)
		})
	}
	/**
	 * 编辑商品
	 */
	edit(item: { id: string; }) {
		this.$router.push({
			name: "EditStore",
			query: {
				id: item.id
			},
			params: {
				id: item.id
			}
		});
	}

}
</script>

<style lang="scss" scoped>
// .el-collapse-item__arrow{
//  margin-right: 20px;
//  margin-top: 20px;
// }
// /deep/ .el-collapse-item__header{
//      display: -webkit-box;
//  flex-direction: row-reverse;
//  position: relative;
// }
// .el-icon-arrow-right{
//  po
// }
.mouseEnter {
	// background-color: red;
	border: 1px solid #ecf6ff;
}

.mouseEnter:hover {
	// background-color: green;
	border: 1px solid #d7e0e8;
}

.pop--button {
	display: flex;
	justify-content: flex-end;
	margin-right: 10px;
}

.goodList {
	width: 200px;
	display: flex;
	justify-content: center;
	text-align: center;
	padding-right: 20px;
	overflow: hidden;




}

.upDown {
	display: flex;
	align-items: center;
	justify-content: center;

	&__goodUp {
		display: flex;
		width: 50px;
		height: 20px;
		justify-content: center;
		align-items: center;
		border-radius: 4px;
		color: white;
		margin-right: 10px;
	}

	&__goodDown {
		margin-left: 10px;
		color: #2d8cf0;
		cursor: pointer;
	}
}

.commandClass {
	height: 150px;
	overflow: overlay;
}

.commandClass::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
	border-radius: 10px;
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 0;
	background: rgba(0, 0, 0, 0);
}

.center {
	display: flex;
	justify-content: center;
	font-size: 30px;
	font-weight: 700;
}

.digTitle {
	font-size: 17px;
	font-weight: bold;
}

.nowrd {
	// 强制一行内显示文本，默认值为normal自动换行
	white-space: nowrap;
	// 超出的部分隐藏
	overflow: hidden;
	// 使用省略号代替文本超出部分
	text-overflow: ellipsis;
	margin-left: 10px;
	width: 120px;
}
</style>
