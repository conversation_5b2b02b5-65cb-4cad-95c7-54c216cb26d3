<template>
	<div>
		<!-- 顶部搜索条件 -->
		<Search
		  @searchBy="getSearch"
		  ref="Search"
		  :status="chooseStatus"
		  :searchTypeProp="searchType"
		></Search>
		<el-tabs v-model="activeName" @tab-click="handleClick">
		  <el-tab-pane v-for="(item, index) in list" :key="index" :label="item.modeName" :name="item.modeName"
		    :status="item.status"></el-tab-pane>
		</el-tabs>
		<div class="topLine">
		  <div class="topLine__left" style="margin-right: 20px; margin-left: 20px;">
		    <!-- 设置分类 -->
		    <SetClassify ref="setClass"
		      :goodIds="chooseStatus"
		      :is-item="false"
		      :is-value="false"
		      >设置分类
        </SetClassify>
		  </div>
      <div class="topLine__left">
        <OperationCenter/>
      </div>
		</div>
		<!-- 商品列表 -->
		<PagingList
		  @goodId="getGoodId"
		  ref="pagingList"
		  @getShowProList="getShowProList"
		  :changeId="chooseStatus"
		></PagingList>
		<!-- 设置分类 -->
		 <div class="listBottom">
		       <PageManage
		  :pageSize="pageSize"
		  :pageNum="pageNum"
		  :total="total"
		  @handleSizeChange="handleSizeChange"
		  @handleCurrentChange="handleCurrentChange"
		  style="margin-top: 0px"
		></PageManage>
		 </div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Ref } from "vue-property-decorator";
import Search from "./components/Search.vue";
import PagingList from "./components/pagingList.vue";
import SetClassify from "./components/goodsComp/SetClassify.vue";
import { SearchKeyType } from "./components/searchType";
import PageManage from "@/components/PageManage.vue";
import { GoodDetailInfo } from "./goodType";
import OperationCenter from "./components/operationCenter.vue";
  @Component({
    components: {
      Search,
	  SetClassify,
	  PagingList,
	  PageManage, OperationCenter
    }
  })
export default class ListApart extends Vue{
  // @Prop({})
  // chooseStatus!: string;
  // @Watch("chooseStatus")
  // getNewModeId() {
  //     this.status = this.chooseStatus;
  //     console.log('获取商品数组信息',this.changeId,this.chooseStatus);
        
  // }
	/** 获取商品数组信息 */
	@Ref()
	readonly pagingList!: PagingList;
	
	status = "";

	searchType: SearchKeyType = {};

	pageSize = 0;

	pageNum = 0;

	total = 0;

  showGetList: GoodDetailInfo[] = [];

	// 禁用状态 0正常 1 禁用
	list:any=[{modeName:'启用',status:'0'},
	  {modeName:'停用',status:'1'},
	  {modeName:'全部',status:''}
	]

	activeName ='启用';

	chooseStatus = "";

	mounted() {
	  this.chooseStatus=this.list[0].status
	}

	/**
	 * 顶部专区选择
	 */
	handleClick(tab: { index: number }) {
	  this.chooseStatus = this.list[tab.index].status || "";
	}

	/**
	 * 获取搜索条件
	 */
	getSearch(data: SearchKeyType) {
	  this.searchType = data;
	  this.searchType.current = 1;
	  console.log('获取搜索条件',data);
	  
	  // 缓存搜索条件
	  sessionStorage.setItem(
	    "cache_storeListLog_search_form",
	    JSON.stringify(this.searchType)
	  );
	  
	  this.getPagingSearch()
	}

	/**
	 * 合并获取条件搜索列表
	 */
	getPagingSearch() {
	  this.pagingList.searchType = Object.assign(
	    this.pagingList.searchType,
	    this.searchType
	  );
	  this.pagingList.getPageList();
	}

	/** 展示分类获取已选择的分类  */
	getShowProList(data: GoodDetailInfo[]) {
	  this.showGetList = data || [];
	  // this.pagingList = this.$refs.pagingList as GoodsList;
	  this.total = this.pagingList.total;
	  console.log('fffff',this.total);
	  this.pageSize = this.pagingList.searchType.size as number;
	  this.pageNum = this.pagingList.searchType.current as number;
	}

	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	 handleSizeChange(val: number) {
	    this.pagingList.searchType.size = val;
	    this.pagingList.getPageList();
	}
	
	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
	    this.pagingList.searchType.current = val;
	    console.log('当前页',val);
	    
	    this.pagingList.getPageList();
	}

	/**
	 * 获取选中商品ids数组
	 */
	getGoodId(data: string[]) {
	  // this.goodIds = data;
	}
}
	
</script>

<style lang="scss" scoped>
	.listBottom {
	  margin-top: 20px;
	  display: flex;
	  align-items: center;
	  justify-content:flex-end;
	  position: fixed;
	  bottom: 10px;
	  width: 990px !important;
	  background-color: white;
	  padding: 10px 0px;
	  z-index: 10;
	}
  .topLine {
    display: flex;
    align-items: center;
  }
  .topLine__left {
    display: flex;
    align-items: center;
  }
</style>