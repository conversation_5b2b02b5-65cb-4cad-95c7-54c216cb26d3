<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:47:50
-->
<template>
  <!-- 设置分类 -->
  <div class="all">
    <div class="setClassify" style="margin-left: 0;">
      <el-button type="primary" @click="exportData">导出列表</el-button>
    </div>
    <!--    <div class="el-dropdown-link">-->
    <!--      <el-dropdown @command="handleCommandsx">-->
    <!--        <span>-->
    <!--          {{ sort }}-->
    <!--        </span>-->
    <!--        <el-dropdown-menu slot="dropdown">-->
    <!--          <el-dropdown-item v-for="item in dropdownNum" :key="item.command"-->
    <!--            :command="{ command: item.command, item: item }">{{ item.text }}</el-dropdown-item>-->
    <!--        </el-dropdown-menu>-->
    <!--      </el-dropdown>-->
    <!--      <div class="dropdownsx">-->
    <!--        <i @click="commandSort(1)" :class="sortIndex==1?'el-icon1':''" class="el-icon-arrow-up el-icon&#45;&#45;right"></i>-->
    <!--        <i @click="commandSort(2)" :class="sortIndex==2?'el-icon2':''" class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
    <!--      </div>-->
    <!--    </div>-->


  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { exportShopsSettled } from "@/api/storeApi/storeApi";
// import GoodCategory from "./GoodCategory.vue"; // 编辑商品分类
// import GoodList from "../GoodsList.vue";
// import {
//   GoodCategroyType,
//   GoodDetailInfo,
// } from "@/views/integral/pointsGoods/goodType";
// import {
//   GoodDetailInfo,
// } from "../../goodType";
// import ListApart from "../../ListApart.vue";
import { ApiSpecArea } from "../../marketType";

@Component({
  components: {
    // GoodCategory,
  },
})
export default class SetClassify extends Vue {
  name = "SetClassify";

  @Prop()
  isItem!: boolean;

  @Prop()
  goodIds!: number[];

  @Prop()
  showGetList!: GoodDetailInfo[];

  idList: string[] = [];

  regionList: Array<Partial<ApiSpecArea>> = [];

  popVisible = false;
  sort = '排序'
  sortIndex = ''
  changeId = "108"

  dropdownNum = [
    {
      text: "核销时间",
      command: "verifyTimeSort",
      disabled: false,
    }
  ];
  dropdownItemList = [
    {
      text: "商品码",
      command: "3",
      disabled: false,
    },
    {
      text: "删除",
      command: "4",
      disabled: false,
    },
  ];

  /**
   * 获取选择商品id
   */
  showGetId() {
    const list = (this.$parent.$refs.goodsList as GoodList).tableCheckedItem;
    this.idList = [];
    list.forEach(item => {
      this.idList.push(String(item.id));
    });
  }

  /**
   * 发布积分方案
   */
  handleClick() {
    this.$router.push({
      name: "AddCertificate"
    });
    // @click="publishGoods"
    // this.$emit("click", e);
  }


  /**
   * 点击的下拉项
   */
  handleCommand(val: any) {
    let type = false;
    if (val.item.modeName) {
      type = true;
    }
    this.$emit("command", val.command, type);
  }
  handleCommandsx(val: any) {
    this.sort = val.item.text
    this.sortcommand = val.item.command
    this.sortIndex = ''
    // console.log('222222', val);

  }
  commandSort(val: any) {
    if (this.sortcommand) {
      this.sortIndex = val
      this.$emit("commandsx", val, this.sortcommand);
    } else {
      this.$message.error("请先选择排序");
    }
  }

  /**
   * 导出数据
   */
  exportData() {
    this.$confirm('最多导出10000条数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 获取搜索参数
      const searchParams = Object.assign(
        this.$parent.pagingList.searchType,
        this.$parent.searchType
      );

      // 删除请求参数中的空值
      const params = {};
      for (const key in searchParams) {
        if (searchParams[key] !== '' && searchParams[key] !== null && searchParams[key] !== undefined) {
          params[key] = searchParams[key];
        }
      }

      exportShopsSettled(params).then((res) => {
        var blob = new Blob([res.data], {
          type: "application/x-msdownload;charset=UTF-8",
        });
        // 创建一个blob的对象链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        // 把获得的blob的对象链接赋值给新创建的这个 a 链接
        let now = new Date();
        let timestamp = now.getTime();
        link.setAttribute('download', '商家申请_' + timestamp + '.xls'); // 设置下载文件名
        document.body.appendChild(link);

        // 触发下载
        link.click();
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        this.$message.success('导出成功');
      }).catch((err) => {
        this.$message.error("导出失败: " + err);
      });
    }).catch(() => {
      // 用户取消导出
    });
  }
}
</script>

<style lang="scss">
@import "@/assets/styles/mixins/mixins.scss";

@include b(setClassify) {
  display: flex;
  line-height: 30px;
  // border: 1px solid #dcdfe6;
  overflow: hidden;
  margin-left: 20px;
  border-radius: 50px;
  background-color: #409EFF;
  color: #ffffff;
  border: 1px solid #409EFF;
  position: relative;

  @include e(title) {
    text-align: center;
    padding: 0 20px;
    cursor: pointer;
  }

  @include e(icon) {
    width: 40px;
    text-align: center;
    cursor: pointer;
    vertical-align: middle;
    color: #ffffff;
    font-weight: bold;
  }
}

.all {
  display: flex;

  // justify-content: space-between;
  .el-dropdown-link {
    height: 32px;
    width: 130px;
    line-height: 20px;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 1);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    text-align: left;
    border: 1px solid rgba(187, 187, 187, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    margin-left: 17px;

    .el-dropdown {
      width: 80px;

      span {
        cursor: pointer;

      }
    }

    .dropdownsx {
      display: flex;
      flex-direction: column;

      .el-icon1 {
        color: #409EFF;
      }

      .el-icon2 {
        color: #409EFF;
      }
    }
  }

}

.setClassify__icon::after {
  color: #ffffff;
  content: "|";
  position: absolute;
  left: -5px;
  bottom: 1px;
}

.commandClass {
  height: 90px;
  overflow: overlay;
}

.commandClass::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0);
}
</style>
