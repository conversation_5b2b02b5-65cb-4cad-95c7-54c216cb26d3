<template>
	<div style="margin-top: 20px;background: red;">
		<m-table :data.sync="settledList"  :checked-item.sync="tableCheckedItem" slot="content"
			class="imgView">
			 
			<m-table-column prop="shopName" align="center" label="商家名称" :showsSlection="true" width="180">
				<template v-slot="{ row }">
					<div class="goodList">
						{{ row.shopName }}
					</div>
				</template>
			</m-table-column>
			<m-table-column prop="contacts" label="联系人" width="180">
				<template v-slot="{ row }">
					<span>{{ row.contacts }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="phone" label="联系人电话" width="180">
				<template v-slot="{ row }">
					<span>{{ row.phone }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="useableTimes" label="申请时间" width="90">
				<template v-slot="{ row }">
					<span>{{ row.applyTime }}</span>
				</template>
			</m-table-column>
			<!-- 审批状态：0.临时保存；100.待审核；101.审核通过；200.驳回 -->
			<m-table-column prop="useableTimes" label="状态" width="140">
				<template v-slot="{ row }">
					<span v-if="row.approvalStatus==0" style="color:#ffc910 ;">临时保存</span>
					<span v-else-if="row.approvalStatus==100" style="color:#ff0000 ;">未审核</span>
					<span v-if="row.approvalStatus==101" style="color:#23c910 ;">已审核</span>
					<span v-if="row.approvalStatus==200">驳回</span>
				</template>
			</m-table-column>
			<m-table-column prop="projectStatus" label="操作" width="90">
				<template v-slot="{ row }">
					<el-button size="mini" @click="informationDetails(row)" type="primary" round>详情</el-button>
					<!-- <el-button size="mini" @click="informationDetails(row,'reject')" type="primary" round>驳回详情</el-button> -->

				</template>
			</m-table-column>
			
			
		</m-table>
	</div>
</template>

<script lang="ts">
	import { Vue, Component, Watch, Prop } from "vue-property-decorator";
	import { SearchKeyType } from "./searchType";
	import SetDrop from "./goodsComp/SetDrop.vue";
	import { ApiSkuType, GoodDetailInfo } from "../goodType";
	import { getShopsSettled } from "@/api/storeApi/storeApi"
	// import SetDrop from "@/views/customer/common/SetDrop.vue";
	@Component({
		components: {
			SetDrop
		}
	})
	export default class pagingList extends Vue {
		@Prop({})
		changeId! : string;

		@Watch("changeId")
		getSaleMode() {
			this.searchType.approvalStatus = this.changeId;
			console.log('bbbbbbbb', this.searchType, this.changeId);

			this.getPageList();
		}
		searchType = {
			current: 1,
			size: 10
		} as SearchKeyType;
		settledList = []
		total = 0;
		tableCheckedItem = [];
		get itemDropList() {
		  return (row: GoodDetailInfo) => {
		    return [
		    {
		        text: "删除",
		        command: "delete",
		        show:true,
		        disabled: false
		      },
		      {
		        text: "停用",
		        command: "deactivate",
		        show: true,
		        disabled: false
		      },
		    {
		        text: "编辑",
		        command: "edit",
		        show:true,
		        disabled: false
		      },
		      {
		        text: "审核",
		        command: "examine",
		        show: true,
		        disabled: false
		      }
		    ];
		  };
		}
		
		mounted() {
			console.log('dddddddd',this.$route);
			if(this.$route.query.listStatus){
				if(this.$route.query.listStatus=='-1'){
					this.searchType.approvalStatus=''
				}else{
					this.searchType.approvalStatus = this.$route.query.listStatus as string

				}
				this.$emit('approvalStatus',this.$route.query.listStatus)
				

			}else{
				this.searchType.approvalStatus = this.changeId;

			}
			// console.log("进入到goodlist",this.searchType.approvalStatus);
					//  加载搜索缓存
			var cache = JSON.parse(
				sessionStorage.getItem("cache_entryListLog_search_form") || "{}"
			);
			console.log('获取商品列表查询参数11', this.searchType, cache);
			this.searchType = Object.assign(this.searchType, cache) as SearchKeyType;

			this.getPageList();
		}
		getPageList() {
			// 删除请求链接里面的空值
			for (const key in this.searchType) {
			     if (!this.searchType[key]) {
			         this.$delete(this.searchType, key)
			     }
			 }
			getShopsSettled(this.searchType).then((res) => {
				this.settledList = res.data.list;
				this.total = res.data.total;
				this.$emit("getShowProList", this.settledList);
				console.log('eeeeeeeee获取商品列表e',this.total, res.data.list);
			}).catch((err) => {
				this.$message.error(err)
			})
		}
		/**
		 * 获取下拉框
		 */
		getDropdown(val: string | number, row: GoodDetailInfo) {
		  // if (Number(val) > 9) {
		    console.log('获取下拉框',val,',',row);
		    if(val=='edit'){
		       this.getGoedit(row.id)
		    }
		    // if(val=='deactivate'){
		    //   this.getDeactivateIntegral(row.id)
		    // }
		    // if(val=='detail'){
		    //   this.pointsTab(row)
		    // }
		
		}
		/**
		 * 详情页
		 */
		informationDetails(item:any) {
		  this.$router.push({
		    name: "DetailsStore",
		    params: {
		      item:JSON.stringify(item),
			  status:this.searchType.approvalStatus||'-1'
		    },
			query:{
				item:JSON.stringify(item),
				status:this.searchType.approvalStatus||'-1'
			}
		  });
		}
		
	}
</script>

<style lang="scss" scoped>
  
  .mouseEnter {
    // background-color: red;
    border: 1px solid #ecf6ff;
  }
  
  .mouseEnter:hover {
    // background-color: green;
    border: 1px solid #d7e0e8;
  }
  
  .pop--button {
    display: flex;
    justify-content: flex-end;
    margin-right: 10px;
  }
  
  .goodList {
    width: 200px;
    // display: flex;
    // // justify-content: center;
    // text-align: center;
    padding-right: 20px;
    overflow: hidden;
    
    
  
   
  }
  
  .upDown {
    display: flex;
    align-items: center;
    justify-content: center;
  
    &__goodUp {
      display: flex;
      width: 50px;
      height: 20px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      color: white;
      margin-right: 10px;
    }
  
    &__goodDown {
      margin-left: 10px;
      color: #2d8cf0;
      cursor: pointer;
    }
  }
  
  .commandClass {
    height: 150px;
    overflow: overlay;
  }
  
  .commandClass::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .commandClass::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(0, 0, 0, 0);
  }
  
  .commandClass::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 0;
    background: rgba(0, 0, 0, 0);
  }
  
  .center {
    display: flex;
    justify-content: center;
	font-size: 30px;
	font-weight: 700;
  }
  
  .digTitle {
    font-size: 17px;
    font-weight: bold;
  }
  </style>
  