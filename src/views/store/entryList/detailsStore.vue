<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-01 15:47:16
 2025-7-18
-->
<template>
	<div class="goodForm">
		<keep-alive>
			<el-form :model="formModel" ref="formModel" label-width="100px">

				<div class="baseMsg">
					<el-form-item label="商家名称" prop="shopName">
						<div class="content">{{ formModel.shopName }}</div>
					</el-form-item>
					<el-form-item label="统一社会信用代码" prop="shopCode" label-width="126px">
						<div class="content1">{{ formModel.shopCode }}</div>
					</el-form-item>
					<el-form-item label="联系人" prop="contacts">
						<div class="content">{{ formModel.contacts }}</div>
					</el-form-item>
					<el-form-item label="联系人电话" prop="phone">
						<div class="content">{{ formModel.phone }}</div>
					</el-form-item>
					<el-form-item label="商户地区" prop="ticketName">
						<div class="content">{{ formModel.provinceName }}{{ formModel.cityName }}{{ formModel.areaName }}
						</div>
					</el-form-item>
					<el-form-item label="详细地址" prop="address">
						<div class="content">{{ formModel.address }}</div>
					</el-form-item>
					<el-form-item label="门牌号" prop="houseNumber">
						<div class="content">{{ formModel.houseNumber }}</div>
					</el-form-item>
					<el-form-item class="business__license" label="营业执照" prop="businessLicense">
						<div style="display: flex;justify-content: flex-start;">
							<div v-if="formModel.businessLicense && formModel.businessLicense != '暂无营业执照'"
								class="demo-image__preview">
								<el-image :src="formModel.businessLicense" :preview-src-list="businessLicense">
								</el-image>
								<span style="font-size: 12px;color: #ccc;margin-left: 10px;">点击图片可预览</span>
								<!-- <el-button type="primary" :preview-src-list="srcList">预览</el-button> -->
							</div>
							<div v-else class="noImg"></div>
						</div>
					</el-form-item>
					
					<el-form-item label="法人身份证" prop="ticketName">
						<div class="flex__corporate">
							<div class="corporate">
								<div class="ID__card">
									<el-image v-if="formModel.cardIdUp" :src="formModel.cardIdUp" :preview-src-list="srcList">
									</el-image>
									<div v-else></div>
								</div>
								<div>正面</div>
							</div>
							<div class="corporate">
								<div class="ID__card">
									<el-image v-if="formModel.cardIdDown" :src="formModel.cardIdDown" :preview-src-list="srcList">
									</el-image>
									<span v-if="formModel.cardIdDown" style="font-size: 12px;color: #ccc;">点击图片可预览</span>
									<div v-else></div>
									<!-- <el-button type="primary" :preview-src-list="srcList">预览</el-button> -->
								</div>
								<div>反面</div>
							</div>
						</div>

					</el-form-item>

					<el-form-item  label="门店图片" prop="storeEnvironment">
						<div style="display: flex;justify-content: flex-start;">
							<div v-if="storeEnvironment.length" class="demo-image__preview">
								<el-image :src="item" :preview-src-list="storeEnvironment" v-for="(item, i) in storeEnvironment" :key="i" style="width: 100px;height: 100px;margin-right: 10px;">
								</el-image>
								<span style="font-size: 12px;color: #ccc;margin-left: 10px;">点击图片可预览</span>
								<!-- <el-button type="primary" :preview-src-list="srcList">预览</el-button> -->
							</div>
							<div v-else class="noImg"></div>
						</div>
					</el-form-item>

					<el-form-item v-if="previousStatus == '200'" label="驳回原因" prop="ticketName">
						<div class="reject">
							{{ formModel.approvalReason ? formModel.approvalReason : '' }}
						</div>
					</el-form-item>

					<el-form-item style="margin-top: 50px;text-align: center;width: 550px;">
						<el-button @click="editSecUnit">取 消</el-button>

						<el-button v-if="previousStatus == '100' && (isSupper || toExamineButton)" type="danger"
							@click="dangerReject()">驳回</el-button>
						<el-button v-if="previousStatus == '100' && (isSupper || toExamineButton)" type="primary"
							@click="submitForm(formModel)">审核</el-button>
						<!-- <el-button v-if="previousStatus=='200'" type="primary"
							@click="submitForm(formModel)">审核</el-button> -->
					</el-form-item>
				</div>
			</el-form>



		</keep-alive>
		<!-- 审核  是否通过 -->
		<el-dialog :visible.sync="passThrough" width="30%" :before-close="throughClose">
			<div class="dialogPass">
				<div>
					审核通过，商家信息立即添加，是否操作？
				</div>
				<el-button type="success" @click="primaryThrough">确 定</el-button>
			</div>

		</el-dialog>
		<el-dialog :visible.sync="dangerShow" width="30%" :before-close="handleClose">
			<div class="dialogWhe">
				驳回原因
			</div>
			<el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请输入内容" v-model="rejection">
			</el-input>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="primaryReject">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { AddSubmitFormType } from "./newGoodType";
import DateUtil from "@/store/modules/date";
import { approveSettled } from "@/api/storeApi/storeApi"
// import tonghuiCertificate from '../components/'
@Component({
	components: {
		// tonghuiCertificate
	}
})
export default class NewGoodFormOpints extends Vue {
	@Prop({})
	from!: string;
	color1 = '#409EFF'
	////////////////////
	// 存放数据的对象
	formModel = {} as Partial<AddSubmitFormType>;
	commodityCode = "";
	createTime = ''
	showRegion = false;
	shopTicketList = []
	fileList = []
	businessLicense: Array<string> = []
	options = [{
		value: '1',
		label: '全部'
	}, {
		value: '100',
		label: '未审'
	}, {
		value: '200',
		label: '已审'
	}];
	approvalType = {}
	// url = ''
	url = 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
	srcList: Array<string> = []
	// fileList = [{ name: 'food.jpeg', url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100' }, { name: 'food2.jpeg', url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100' }]
	previousStatus: string = ''
	dangerShow: boolean = false
	passThrough: boolean = false
	rejection = ''
	status: number | string = ''

	menuName = "商家申请";

	toExamineButtonCode = "entryList.toExamine";

	toExamineButton = false;

	buttonList = [];
	storeEnvironment = [];

	isSupper = 0;

	mounted() {
		let val = this.$route.query.item
		let items = JSON.parse(val)
		this.status = this.$route.query.status as string
		if (items) {
			this.formModel = items as Partial<AddSubmitFormType>;
			console.log('wwwwwwwww11', this.formModel, items)
			this.previousStatus = items.approvalStatus as string
			this.businessLicense[0] = items.businessLicense
			this.srcList[0] = items.cardIdUp
			this.srcList[1] = items.cardIdDown
			this.storeEnvironment = items.storeEnvironment.split(',')
		}
		// if (this.$route.query.Status) {
		// 	console.log('wwwwwwwww11',this.previousStatus);
		// 	this.previousStatus = this.$route.query.Status as string
		// }
		console.log('wwwwwwwww', this.$route);
		// this.getIdTicket();

		this.buttonAuth();

	}

	buttonAuth() {

		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var toExamineButtonData = buttonList.find(e => e == this.toExamineButtonCode);

		if (toExamineButtonData != null && toExamineButtonData != undefined) {
			this.toExamineButton = true;
		}

	}
	/**
	 * 选择商品属性
	 */
	selectTemAttsList(temAttsId: number) {
		console.log("selectTemAttsList", temAttsId);
	}
	// 选择日期
	chooseStartTime(data: any) {
		this.formModel.startTime = data ? this.dateConversion(data) : "";

	}
	chooseEndTime(data: any) {
		this.formModel.endTime = data ? this.dateConversion(data) : "";

	}
	/**
	 * 开始时间
	 * @param data 
	 */
	chooseTime(data: any) {

		this.formModel.displayStartTime = data ? this.dateConversion(data) : "";
	}
	/**
	 * 结束时间
	 * @param data 
	 */
	chooseTimeEad(data: any) {
		this.formModel.displayEndTime = data ? this.dateConversion(data) : "";

		console.log('时间', this.dateConversion(data));

	}

	dateConversion(value: Date) {
		const date = new DateUtil("").getYMDHMSs(value);
		console.log('时间', date);

		return date;
	}
	/**取消审核判断 */
	throughClose() {
		this.passThrough = false
	}

	/**确定审核通过 */
	primaryThrough() {
		this.passThrough = false
		this.approvalType.approvalStatus = '101'
		this.approvePrice(this.approvalType)

	}
	/**点击打款按钮 */
	successPayment(id: string) {
		this.$confirm('点击打款按钮' + id)
		// this.approvalType.id = id
		// this.approvalType.approvalStatus = '101'
		// this.approvePrice(this.approvalType)
	}

	/*** 关闭驳回原因输入 */
	handleClose(done: any) {
		// this.dangerShow = true
		done();
		this.$message('已关闭驳回原因输入');
		// this.$confirm('确认关闭？')
		//     .then(_ => {
		//         done();
		//     })
		//     .catch(_ => { });
	}
	/**
	 * 点击列表中的驳回操作
	 */
	dangerReject() {
		this.dangerShow = true
		// this.approvalType.id = id

	}
	/**确定驳回 */
	primaryReject() {
		this.dangerShow = false
		if (!this.rejection) {
			this.$confirm('驳回原因不能为空')
			return
		}

		this.approvalType.approvalStatus = '200'
		this.approvalType.approvalReason = this.rejection
		this.approvePrice(this.approvalType)
		this.this.rejection = ''
		this.$delete(this.approvalType, 'approvalReason')


	}
	approvePrice(val: any) {
		val.id = this.formModel.id
		approveSettled(val).then((res) => {
			console.log('rrrr', res);
			this.$message.success(res.data)
			// this.status
			this.goEntryList()

		}).catch((err) => {
			this.$message.error(err)
		})
	}
	goEntryList() {
		this.$router.push({
			name: "EntryList",
			params: {
				'listStatus': this.status
			},
			query: {
				listStatus: this.status
			},
		});
	}



	// 信息表单
	submitForm(formName: string) {
		this.passThrough = true
		console.log('11111111111111', formName);

	}
	/**
* 确定退出发布积分商品页面
*/

	editSecUnit() {
		console.log('未保存的信息将不会保留');
		// this.$router.go(-1)
		this.goEntryList()
	}
	//////////////////////////////////////

	/**
	 * 获取商品详情
	 */
	async getIdTicket() {
		const goodId = this.$route.query.id || this.$route.params.id;
		if (!goodId) {
			return;
		}
		const { data } = await getShopsSettled({ 'id': goodId });
		this.formModel = data

		console.log('获取商品详情', data);

	}

}
</script>

<style lang="scss">
@import "@/assets/styles/goods/index.scss";

.el-input__inner {
	text-align: center;
}

.business__license {

	height: 220px;

	.demo-image__preview {

		// width: 560px;
		// text-align: center;
		.el-image {
			width: 550px;
			height: 220px;
		}
	}


}

.noImg {
	width: 550px;
	height: 220px;
	border: 1px solid rgb(187, 187, 187);
}

.flex__corporate {
	display: flex;
	justify-content: flex-start;

	.corporate {
		display: flex;
		flex-direction: column;
		align-items: center;

		.ID__card {
			.el-image {
				width: 270px;
				margin-right: 10px;
				height: 220px;
			}

			div {
				width: 270px;
				margin-right: 10px;
				height: 220px;
				border: 1px solid rgb(187, 187, 187);

			}
		}
	}
}

.content {
	width: 550px;
	border: 1px solid rgb(187, 187, 187);
	height: 32px;
	text-align: center;
	border-radius: 5px;
	color: rgb(132, 126, 126);
}

.content1 {
	width: 524px;
	border: 1px solid rgb(187, 187, 187);
	height: 32px;
	text-align: center;
	border-radius: 5px;
	color: rgb(132, 126, 126);
}

.dialogPass {
	color: #010101;
	display: flex;
	justify-self: center;
	align-items: center;
	flex-direction: column;

	.el-button {
		margin-top: 20px;
		background: rgb(9, 240, 22);
		color: #ffffff;
		width: 102px;
		height: 29px;
		font-size: 18px;
		outline: none;
		border: none;
		text-align: center;
		line-height: 14px;
	}
}

.dialogWhe {
	color: #040404;
	font-size: 20px;
	margin-bottom: 10px;
	text-align: center;
	padding-bottom: 10px;
	border-bottom: 1px solid rgb(187, 187, 187);
}

// .upload {
// 	display: flex;
// 	align-items: center;
// 	flex-direction: column;
// 	margin-left: 10px;

.upload-demo {
	margin-top: 160px;
	margin-left: -85px;
}

// }
.reject {
	width: 550px;
	height: 100px;
	padding-left: 10px;
	color: #ff0000;
	border: 1px solid rgb(187, 187, 187);
}

.useableTimes__ci {
	margin-left: 10px;
}

.goodForm::-webkit-scrollbar {
	display: none;
}

.w-e-text-container {
	height: 532px !important;
	/*!important是重点，因为原div是行内样式设置的高度300px*/
}
</style>