<template>
	<div class="borderBox">
		<div class="box__top">
			<div class="topname">分类名称</div>
			<div class="topnum">商家数量</div>
			<div class="topnum">分类图片</div>
			<div class="top">操作</div>
			<!-- <el-row>
				<el-col :span="7">
				</el-col>
				<el-col :span="6">
				</el-col>
				<el-col :span="3">
				</el-col>
				<el-col :span="8">
				</el-col>
			</el-row> -->
		</div>
		<m-drag-list :data.sync="list" child-key="shopsCategoryVos" :defaultOpen="showList" @onMove="onMove">
			<!-- <template><div>qwerwq</div></template> -->

			<template #item="{ itemData }">
				<div class="borderBox__first">
					<!-- <div>{{ itemData.name }}11</div> -->
					<div class="borderBox__child--left">
						{{ itemData.name }}
						<div style="text-align:center;width:120px">
							{{ itemData.productNumber || '' }}
						</div>
					</div>

					<div class="borderBox__first--deal">
						<span style="color:#2D8CF0" @click.stop="modifyList(1, itemData)" v-if="isSupper || addButton">新增二级分类</span>
						<span style="color:#2D8CF0" @click.stop="modifyList(2, itemData)" v-if="isSupper || editButton">编辑</span>
						<span style="color:#FA6465" @click.stop="btnDelClass(itemData)" v-if="isSupper || deleteButton">删除</span>

					</div>
				</div>
			</template>
			<template #child="{ childData }">
				<div class="borderBox__child" @onMove="onMove">
					<div class="borderBox__child--left">
						{{ childData.name }}
						<div style="text-align:center;width:92px">
							{{ childData.productNumber }}
						</div>
					</div>
					<div style="width:200px;" v-if="childData.pic != null && childData.pic != ''">
						<el-image :src="childData.pic" style="width: 40px; height: 40px">
						</el-image>
					</div>
					<div class="borderBox__child--right">
						<div style="color:#2D8CF0" @click="editClassTwo(childData)" v-if="isSupper || editButton">
							编辑
						</div>
						<div style="color:#FA6465" @click="delClassTwo(childData)" v-if="isSupper || deleteButton">
							删除
						</div>
					</div>
				</div>
			</template>
		</m-drag-list>
		<div class="emptyLine" v-if="hasList">
			暂无数据~
		</div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Watch } from "vue-property-decorator";
import ShowClass from './ShowClass.vue'
import { GoodCategroyType } from "./goodType";
@Component
export default class ShowSecond extends Vue {
	@Watch("navigationListCom", { deep: true })
	handleNavigationListCom() {
		this.list = JSON.parse(JSON.stringify(this.navigationListCom));
		this.hasList = this.list.length === 0 ? true : false;
		this.list.forEach((item, index) => {
			console.log('1111111', item);
			if (item.shopsCategoryVos.length > 0) {
				this.showList.push(index);
			}
		});
		console.log('aqdqw', this.list, this.showList);
	}



	get navigationListCom() {
		const parentHtml = this.$parent as ShowClass
		return parentHtml.navigationListCom;
	}

	/** 处理数组 */
	list: GoodCategroyType[] = [];

	/** 展开数组 */
	showList: number[] = [];

	/** 是否有数据 */
	hasList = false;


	menuName = "商家分类";

	addButtonCode = "classification.add";

	deleteButtonCode = "classification.delete";

	editButtonCode = "classification.edit";

	addButton = false;

	deleteButton = false;

	editButton = false;

	buttonList = [];

	isSupper = 0;


	mounted() {

		this.buttonAuth();

	}

	buttonAuth() {

		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter((i: any) => i.menuName == this.menuName)

		let buttonList = [] as any;

		authMenuButtonVos.forEach((element: any) => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var addButtonData = buttonList.find((e: any) => e == this.addButtonCode);

		if (addButtonData != null && addButtonData != undefined) {
			this.addButton = true;
		}

		var deleteButtonData = buttonList.find((e: any) => e == this.deleteButtonCode);

		if (deleteButtonData != null && deleteButtonData != undefined) {
			this.deleteButton = true;
		}


		var editButtonData = buttonList.find((e: any) => e == this.editButtonCode);

		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}
	}
	onMove(childData: any) {
		// console.log('dddddd111111d',childData);

		setTimeout(() => {
			this.$emit("navigationListComChange", this.list, childData);
		}, 500);
	}

	/**
	 * 一级编辑
	 */
	modifyList(type: number, item: GoodCategroyType) {
		this.$emit("modifyList", type, item);
	}

	/**
	 * 一级删除
	 */
	btnDelClass(item: GoodCategroyType) {
		this.$emit("btnDelClass", item);
	}

	/**
	 * 二级编辑
	 */
	editClassTwo(item: GoodCategroyType) {
		this.$emit("editClassTwo", item);
	}

	/**
	 * 二级删除
	 */
	delClassTwo(item: GoodCategroyType) {
		this.$emit("delClassTwo", item);
	}
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixins/mixins.scss";

@include b(borderBox) {

	// min-height: 700px;
	@include e(first) {
		background-color: #f2f2f6;
		// padding: 0px 10px;
		display: flex;
		justify-content: space-between;

		@include m(deal) {
			margin-right: 10px;
			display: flex;
			justify-content: space-between;
			width: 200px;
			cursor: pointer;
		}
	}

	@include e(child) {
		padding: 15px 10px 15px 20px;
		border-bottom: 1px solid #f2f2f2;
		display: flex;
		justify-content: space-between;

		@include m(left) {
			display: flex;
			justify-content: space-between;
			width: 580px;
			padding-left: 10px;
		}

		@include m(right) {
			display: flex;
			justify-content: space-between;
			width: 88px;
			cursor: pointer;
		}
	}

	/deep/ .el-icon-arrow-right:before {
		content: "\e791";
	}

	/deep/ .el-collapse-item__content {
		padding-bottom: 0;
	}

	/deep/ .el-collapse-item__header {
		background-color: #f2f2f6;
		padding-left: 10px;
	}
}

.borderBox__child:hover {
	background-color: #f5f5f5;
	cursor: move;
}

.emptyLine {
	width: 100%;
	height: 80px;
	background-color: white;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	justify-content: center;
	font-size: 14px;
	color: #b3b3b3;
	border-bottom: 1px solid #ebeef5;
	border-top: 1px solid #ebeef5;
}

.box__top {
	width: 100%;
	margin-bottom: 10px;
	// width: 980px;
	height: 50px;
	background: rgb(234, 234, 234);
	line-height: 50px;
	display: flex;
	align-content: center;
	justify-content: space-around;

	.topname {
		margin-left: 30px;
	}

	.topnum {
		margin-left: 5px;
	}

	.top {
		margin-left: 220px;
	}
}
</style>
