<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 14:08:55
 2025.5.28有改动的页面
-->
<template>
	<div>
		<!-- <div class="order__control--top">
			<div class="control--l">
				<el-button-group class="fix" v-if="controlOption">
					<el-button plain @click="emitFun('batchSend')" type="primary"
						v-if="(isSupper || deliverGoodsBatchButton)">批量发货</el-button>
					<el-button plain @click="emitFun('deliverMessage')" type="primary"
						v-if="mainFlag && (isSupper || deliveryBatchSendMessageButton)">发货提醒</el-button>
					
				</el-button-group>
			</div>
			<div class="control--r">
				<slot></slot>
			</div>
		</div> -->

		<template>
			<el-table ref="multipleTable" :data="data" border tooltip-effect="dark" style="width: 100%" height="670"
				@selection-change="handleSelectionChange">
				<!-- <el-table-column type="selection" width="55" fixed="left">
				</el-table-column> -->
				<el-table-column label="预约人" min-width="140" fixed="left" prop="userName">
					
				</el-table-column>
				<el-table-column label="预约号码" min-width="140" prop="mobile">
				
				</el-table-column>
			
				<el-table-column label="预约项目" min-width="180" prop="serviceName">
					
				</el-table-column>
				<el-table-column label="预约人数" min-width="100" prop="reservationNumber">					
				</el-table-column>
				<el-table-column label="预约时间" min-width="300" prop="reservationTimeStart">
					<template slot-scope="scope">
						<div>
							{{ scope.row.reservationTimeStart}} 至
							{{ scope.row.reservationTimeEnd}}
						</div>
					</template>
			</el-table-column>

			<el-table-column label="职员" min-width="180" prop="employeeName">
					
				</el-table-column>
			
				<el-table-column label="状态" min-width="120" prop="mobile">
					<template slot-scope="scope">
						<div v-if="scope.row.status == 101">
							待进行
						</div>
						<div v-if="scope.row.status == 104">
							待评价
						</div>
						<div v-if="scope.row.status == 105">
							已完成
						</div>
						<div v-if="scope.row.status == 300">
							超时取消
						</div>
						<div v-if="scope.row.status == 301">
							用户取消
						</div>
					</template>
				</el-table-column>
				<el-table-column label="备注信息" min-width="220" prop="content">					
				</el-table-column>

				
				<el-table-column fixed="right" label="操作" width="160">
					<template slot-scope="scope">

						<el-button-group class="fix">
								<el-button plain @click="emitFun('detail', scope.row)" type="primary">查看详情</el-button> 


								<el-button plain class="dropdown__fix more" type="primary">
									<el-dropdown size="mini" trigger="hover">
										<span class="dropdown__fix--more">...</span>
										<el-dropdown-menu slot="dropdown">
											<el-dropdown-item v-if="scope.row.status == 101"  @click.native="emitFun('ok', scope.row)">完成</el-dropdown-item>
										</el-dropdown-menu>
									</el-dropdown>
								</el-button>
							
							</el-button-group>
							
					</template>
				</el-table-column>
			</el-table>

		</template>

		<div class="order__control--bottom fixed" style="width: 100%">			

			<el-pagination small layout="total,  prev, pager, next,  sizes" :current-page.sync="form.current"
				:size.sync="form.size" :page-size.sync="form.size" :page-sizes="[10, 20, 50, 100]" :total.sync="form.total">
			</el-pagination>
		</div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import {
	DeliveryOrderList,
	DeliveryOrderQueryType,
	DeliveryToolOptions,
} from "../orderType";
import {
	getDeliverOrderStatusName,
	getDeliveryTypeName,
	isClose,
	strFilter,
} from "../common/order";
import { getAfterStatusName, getAfterName } from "../common/afterSale";

@Component({ filters: { strFilter } })
export default class OderTable extends Vue {
	/** 表格数据 */
	@Prop({ type: Array })
	data!: any[];

	/** 表格内查询按钮 */
	@Prop()
	controlOption!: any[];

	/** 查询条件 */
	@Prop()
	query!: any;

	/** 父级已选条码 */
	@Prop()
	checkedItem!: Array<{ orderId: string }>;

	/** 本地form 主要用户页码切换 */
	get form() {
		return this.query;
	}

	set form(v) {
		this.$emit("update:query", v);
	}

	/** 已选表格选项 */
	get tableCheckedItem() {
		return this.checkedItem || [];
	}

	set tableCheckedItem(v) {
		this.$emit("update:checked-item", v);
	}

	/** 表头 */
	columns = [
		{
			label: "商品",
			width: 270,
			coustomStyle: "text-align: left;",
		},
		{
			label: "客户",
			width: 220,
			coustomStyle: "text-align: left;",
		},
		{
			label: "交易额",
			width: 154,
		},
		{
			label: "订单状态",
			width: 115,
		},
		{
			label: "操作",
			width: 142,
		},
	];



	multipleSelection: [];

	menuName = "快递订单";
	buttonList = [];
	isSupper = 0;
	mainFlag = 0;

	editButtonCode = "delivery.edit";
	editButton = false;

	closeButtonCode = "delivery.close";
	closeButton = false;

	paymentButtonCode = "delivery.payment";
	paymentButton = false;

	notIssuedButtonCode = "delivery.notIssued";
	notIssuedButton = false;

	issuedButtonCode = "delivery.issued";
	issuedButton = false;

	toExamineButtonCode = "delivery.toExamine";
	toExamineButton = false;

	deliverGoodsButtonCode = "delivery.deliver.goods";
	deliverGoodsButton = false;

	deliverGoodsBatchButtonCode = "delivery.deliver.batch.goods";
	deliverGoodsBatchButton = false;

	deliveryBatchSendMessageButtonCode = "delivery.batch.send.message";
	deliveryBatchSendMessageButton = false;

	mounted() {
		this.buttonAuth();
	}

	buttonAuth() {
		this.mainFlag = this.$STORE.userStore.userInfo.shopInfoVo.mainFlag
		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter((i: any) => i.menuName == this.menuName)


		let buttonList = [] as any;

		authMenuButtonVos.forEach((element: any) => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var editButtonData = buttonList.find((e: any) => e == this.editButtonCode);

		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}

		var closeButtonData = buttonList.find((e: any) => e == this.closeButtonCode);

		if (closeButtonData != null && closeButtonData != undefined) {
			this.closeButton = true;
		}


		var paymentButtonData = buttonList.find((e: any) => e == this.paymentButtonCode);

		if (paymentButtonData != null && paymentButtonData != undefined) {
			this.paymentButton = true;
		}

		var notIssuedButtonData = buttonList.find((e: any) => e == this.notIssuedButtonCode);

		if (notIssuedButtonData != null && notIssuedButtonData != undefined) {
			this.notIssuedButton = true;
		}

		var issuedButtonData = buttonList.find((e: any) => e == this.issuedButtonCode);

		if (issuedButtonData != null && issuedButtonData != undefined) {
			this.issuedButton = true;
		}

		var toExamineButtonData = buttonList.find((e: any) => e == this.toExamineButtonCode);

		if (toExamineButtonData != null && toExamineButtonData != undefined) {
			this.toExamineButton = true;
		}
		var deliverGoodsButtonData = buttonList.find((e: any) => e == this.deliverGoodsButtonCode);

		if (deliverGoodsButtonData != null && deliverGoodsButtonData != undefined) {
			this.deliverGoodsButton = true;
		}

		var deliverGoodsBatchButtonData = buttonList.find((e: any) => e == this.deliverGoodsBatchButtonCode);

		if (deliverGoodsBatchButtonData != null && deliverGoodsBatchButtonData != undefined) {
			this.deliverGoodsBatchButton = true;
		}
		var deliveryBatchSendMessageButtonData = buttonList.find((e: any) => e == this.deliveryBatchSendMessageButtonCode);

		if (deliveryBatchSendMessageButtonData != null && deliveryBatchSendMessageButtonData != undefined) {
			this.deliveryBatchSendMessageButton = true;
		}
	}

	/** 触发父级方法 */
	emitFun(name: string, data: DeliveryOrderList, status?: boolean) {
		// 阻止未选中元素的批量操作
		if (!data && !this.checkedItem.length && name != 'deliverMessage') {
			return this.$message.info("请先选择条目");
		}
		this.$emit("table-function", name, data, status);
	}

	/** 物流按钮显示隐藏 */
	logisticsBtnVisible(item: DeliveryOrderList) {
		const other = ["WAIT_FOR_PAY", "WAIT_FOR_SEND", "COMPLETE"];
		return (
			!isClose(item.status) &&
			!other.includes(item.status) &&
			item.deliveryType === "LOGISTICS"
		);
	}


	/**
	* 多选
	*/
	handleSelectionChange(val: any) {
		this.multipleSelection = []
		val.forEach((item: any) => {
			this.multipleSelection.push(item.orderId);
		});
		this.$emit("update:checked-item", val);
		// console.log("fdf=",this.multipleSelection);
		// console.log("val=",val);
		
	}

	getOrderStatusName = getDeliverOrderStatusName;

	getDeliveryTypeName = getDeliveryTypeName;

	isClose = isClose;

	getAfterStatusName = getAfterStatusName;

	getAfterName = getAfterName;
}
</script>

<style lang="scss">
.header__tag {
	border-radius: 0;
	margin-right: 10px;
}

.fixed {
	@include flex(space-between);
	position: fixed;
	bottom: 10px;
	width: 990px !important;
	z-index: 10;
	background: #fff;
	padding: 10px 0;
}
</style>