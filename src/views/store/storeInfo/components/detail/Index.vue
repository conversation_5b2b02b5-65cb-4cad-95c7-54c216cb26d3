<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-12 15:05:52
 2025.5.28有改动的页面
-->
<template>
  <div>
    <el-dialog :visible.sync="visible" class="detail__modal">
      <el-row>
        <el-col :span="12" class="form__col"><b>门店名称：</b>{{ detail.shopName }}</el-col>
        <el-col :span="12" class="form__col"><b>预约人：</b>{{ detail.userName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="12" class="form__col"><b>预约号码：</b>{{ detail.mobile }}</el-col>
        <el-col :span="12" class="form__col"><b>预约人数：</b>{{ detail.reservationNumber }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="12" class="form__col"><b>预约时间：</b>{{ detail.reservationTimeStart }} 至 {{ detail.reservationTimeEnd }}</el-col>
        <el-col :span="12" class="form__col"><b>服务名称：</b>{{ detail.serviceName }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="12" class="form__col"><b>职员名称：</b>{{ detail.employeeName }}</el-col>
        <el-col :span="12" class="form__col"><b>预约内容：</b>{{ detail.content }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="12" class="form__col">
          <b>状态：</b>
          <span v-if="detail.status == 101">正常</span>
          <span v-else-if="detail.status == 104">待评价</span>
          <span v-else-if="detail.status == 105">已完成</span>
          <span v-else-if="detail.status == 300">超时取消</span>
          <span v-else-if="detail.status == 301">用户取消</span>
          <span v-else>{{ detail.status }}</span>
        </el-col>
      </el-row>
      <!-- <el-row v-if="detail.evaluateInfo">
        <el-col :span="12" class="form__col"><b>评价用户昵称：</b>{{ detail.evaluateInfo.userName }}</el-col>
        <el-col :span="12" class="form__col"><b>评分：</b>{{ detail.evaluateInfo.rate }}</el-col>
      </el-row>
      <el-row v-if="detail.evaluateInfo && detail.evaluateInfo.comment">
        <el-col :span="12" class="form__col"><b>评价内容：</b>{{ detail.evaluateInfo.comment }}</el-col>
      </el-row> -->
    </el-dialog>




  </div>

</template>

<script lang="ts">
import _ from "lodash";
import { Vue, Component, Prop } from "vue-property-decorator";
import { getPackageDetail, verifyGoods, pcGetCode, verifyPackageGoodsCode } from "@/api/order"

import PageManage from "@/components/PageManage.vue";
import {
  DeliveryOrderList,
  ApiOrderDetail,
  ApiAfterListType,
} from "../../orderType";
import {
  getAddProductPackageVo,
} from "@/api/good/goods";
@Component({
  components: {
    PageManage,
  },
})
export default class OrderDetailModal extends Vue {
  /** 订单详情 */
  @Prop({
    default() {
      return {};
    },
  })
  detail!: any;

  /** 售后详情 */
  @Prop({
    default() {
      return {};
    },
  })
  afterInfo?: any;

  /** 是否需要tab */
  @Prop({
    default: true,
  })
  needTabs?: boolean;

  /**
   * 当前组件是否在快递订单页
   */
  @Prop({
    default: false,
  })
  isDelivery?: boolean;

  /** 当前是否所处售后工单 */
  @Prop({
    default: false,
  })
  isAfter?: boolean;

  @Prop()
  value!: boolean;

  @Prop({
    default: "1",
  })
  type!: string;

  @Prop({
    default: false,
  })
  isGroup?: boolean;


  get deliveryVisible() {
    const detail = this.detail;
    // && detail.orderDelivery.deliverySn  物流单号
    console.log("物流单号-", detail);
    // orderDeliveryList
    return detail && detail.orderDeliveryList.length && detail.type != "TICKET";
  }

  /** 模态框显示隐藏 */
  get visible() {
    return this.value;
  }

  set visible(v) {
    this.$emit("input", v);
  }

  reset(data: DeliveryOrderList | ApiAfterListType) {
    this.$emit("reset", data);
  }

  packageDetails = []  //权益包详情数据

  // 权益包详情弹窗
  dialogPackageDetailsVisible = false

  //核销选择商品弹窗
  dialogVerifyGoodsVisible = false

  miniAccountPackageGoodsId = ""
  verifyCode = ""
  searchType = {
    productName: "",
  }
  goodList = [];
  current = 1;
  size = 10;
  total = 0;

  searchGoods() {
    this.current = 1;
    this.size = 10;
    this.getGoodsList();
  }
  getGoodsList() {
    let params = this.searchType
    params.current = this.current
    params.size = this.size
    getAddProductPackageVo(params).then((res) => {
      this.goodList = res.data.list
      this.total = res.data.total
      this.dialogVerifyGoodsVisible = true
    }).catch((err) => {
      this.$message.error(err)
    })
  }
  openPackageDetails() {
    //显示权益包详情弹窗
    this.dialogPackageDetailsVisible = true;
    //关闭订单详情
    this.$emit("closeDetail");
    this.getPackageDetail()
  }

  //beforeClose弹窗关闭前的操作
  beforeClose() {
    this.getPackageDetail();
    this.visible = true
  }

  /**
* @method handleSizeChange
* @description 每页 条
*/
  handleSizeChange(val: number) {
    this.size = val;
    this.getGoodsList()
  }
  /**
* @method handleCurrentChange
* @description 当前页
*/
  handleCurrentChange(val: number) {
    this.current = val;
    this.getGoodsList()
    // this.getPageTicket();
  }

  //获取权益包详情
  getPackageDetail() {
    let params = {}
    //获取本地存储数据
    let localData = JSON.parse(localStorage.getItem('orderDetailInfo') || '{}')
    params.orderId = localData.orderId
    params.packageId = localData.productId
    //调用接口
    getPackageDetail(params).then(res => {
      this.packageDetails = res.data
    }).catch(err => {
      console.log(err);
      this.$message.error({
        message: '获取权益包详情失败',
        type: 'error'
      });
    })
  }

  // //核销商品
  // verifyPackageDetail(item) {
  //   let params = {}
  //   //获取要核销的商品id
  //   params.miniAccountPackageGoodsId = item.id
  //   let debouncedFunction = _.debounce(this.sendVerifyGoods(params), 1000)
  //   debouncedFunction()
  // }

  // //发送请求核销
  // sendVerifyGoods(params) {
  //   console.log("sendVerifyGoods", params);

  //   //调用接口
  //   verifyGoods(params).then(res => {
  //     this.$message.success({
  //       message: '核销成功',
  //       type: 'info'
  //     });
  //     this.getPackageDetail()
  //   }).catch(err => {
  //     console.log(err);
  //     this.$message.error({
  //       message: err,
  //       type: 'error'
  //     });
  //   })
  // }

  // 核销商品
  verifyPackageDetail(item) {
    //   let params = {
    //     miniAccountPackageGoodsId: item.id  // 获取要核销的商品id
    // };
    //   this.debounceSendVerifyGoods(params);
    this.miniAccountPackageGoodsId = item.id
    this.searchType.productName = ""
    this.getVerifyCode();
    this.searchGoods()
  }
  getVerifyCode() {
    let params = {};
    params.miniAccountPackageGoodsId = this.miniAccountPackageGoodsId
    pcGetCode(params)
      .then(res => {
        this.verifyCode = res.data.verifyCode
      })
      .catch(err => {
        console.log(err);
        this.$message.error({
          message: err,
          type: 'error'
        });
      });
  }

  verifyChooseGoods(row) {
    let params = {
      verifyCode: this.verifyCode, // 获取要核销的商品id
      verifyGoodsId: row.productId,
      verifySkuId: row.skuId,
    };
    this.debounceSendVerifyGoods(params);
    this.dialogVerifyGoodsVisible = false
  }
  // 防抖后发送请求核销
  debounceSendVerifyGoods = _.debounce(params => {
    // 调用接口
    verifyPackageGoodsCode(params)
      .then(res => {
        this.$message.success({
          message: '核销成功',
          type: 'info'
        });
        this.dialogVerifyGoodsVisible = false
        this.getPackageDetail();
      })
      .catch(err => {
        console.log(err);
        this.$message.error({
          message: err,
          type: 'error'
        });
      });
  }, 1000);

}
</script>

<style lang="scss">
.detail__modal {
  .el-dialog__body {
    padding: 10px 20px 30px;
  }
}

.form__col {
  font-size: 18px;
  font-weight: 500;
  line-height: 2.2;
  b {
    font-size: 18px;
    font-weight: 700;
    margin-right: 4px;
  }
}

.package_table {
  width: 100%;
}

.package_table thead {
  padding: 20px 10px;
  border: 1px solid #d8eaf9;
  border-bottom: none;
  display: block;
  border-radius: 10px 10px 0 0;
}

.package_table thead>div {
  text-align: center
}

.package_table tbody {
  padding: 20px 10px;
  border: 1px solid #d8eaf9;
  display: block;
}

.package_table tbody>div {
  text-align: center;
}
</style>
