<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 17:16:33
  2025.5.30有改动的页面
  代发货搜索
-->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="90px">

      <el-row>
        <el-col :span="10">
          <el-form-item label="预约号码">
            <el-input v-model="form.mobile" placeholder="请输入预约号码"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="预约人">
            <el-input v-model="form.userName" placeholder="请输入预约人"></el-input>
          </el-form-item>
        </el-col>

      </el-row>

      <el-row>
        <el-col :span="10">
          <el-form-item label="预约项目">
            <el-input v-model="form.serviceName" placeholder="请输入预约项目"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="10">
          <el-form-item label="职员">
            <el-input v-model="form.employeeName" placeholder="请输入职员"></el-input>
          </el-form-item>
        </el-col>

      </el-row>

      <el-row>				
				<el-col :span="10">
					<el-form-item label="预约时间"> 
						<el-date-picker v-model="form.date" type="daterange" range-separator="-" start-placeholder="开始时间"  value-format="yyyy-MM-dd"
							end-placeholder="结束时间" style="width: 100%;" @change="chooseTimes">
						</el-date-picker>
					</el-form-item>
				</el-col>
			</el-row>
      
      <el-form-item>
        <el-button type="primary" @click="emitSeach">搜索</el-button>
      </el-form-item>
    </el-form>
  
  </m-card>
</template>

<script lang="ts">
/* eslint-disable indent */
import { Vue, Component, Watch } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import DateUtil from "@/store/modules/date";

/** 订单顶部查询表单 */
@Component({
  components: {
    PageManage
  }
})
export default class OrderFrom extends Vue {
  form =  {
    date: '',
    startTime: '',
    endTime: '',

    current: 1,
    orderStatus: "-1",
    size: 20,
    total: 0,

    userName: '',
    mobile: '',
    reservationNumber: '',
    reservationTimeStart: '',
    reservationTimeEnd: '',
    employeeName: '',
    serviceName: '',
  };;

  
  mounted() {  

  }

  chooseTimes(data : any) {
			this.form.startTime = data ? this.dateConversion(data[0]) : "";
			this.form.endTime = data ? this.dateConversion(data[1]) : "";
			// console.log('时间', data[0], this.dateConversion(data), this.searchType);
        }

		dateConversion(value : Date) {
			// const date = new DateUtil("").getYMDHMSs(value);
			const date = new DateUtil("").getYMD(value);
			return date;
		}

  /** 触发父级查询 */
  emitSeach() {
    console.log(this.form);
    this.$emit("input-set", this.form);
  }


}
</script>

<style lang="scss" scoped>
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}
</style>
