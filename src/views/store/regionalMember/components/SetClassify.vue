<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 13:47:50
 2025-7-16
-->
<template>
  <!-- 设置分类 -->
  <div class="all">
    <div class="setClassify">
      <el-button type="primary" @click="handExport" round>规则说明</el-button>
    </div>
    <!-- <div class="el-dropdown-link">
      <el-dropdown @command="handleCommandsx">
        <span>
          {{ sort }}
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="item in dropdownNum" :key="item.command"
            :command="{ command: item.command, item: item }">{{ item.text }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="dropdownsx">
        <i @click="commandSort(1)" :class="sortIndex == 1 ? 'el-icon1' : ''"
          class="el-icon-arrow-up el-icon--right"></i>
        <i @click="commandSort(2)" :class="sortIndex == 2 ? 'el-icon2' : ''"
          class="el-icon-arrow-down el-icon--right"></i>
      </div>
    </div> -->

  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
@Component({

})
export default class SetClassify extends Vue {
  name = "SetClassify";

  sort = '申请时间'
  sortIndex: number | string = '2'
  sortcommand = 'saleIntegralSort'
  dropdownNum = [
    {
      text: "销售积分",
      command: "saleIntegralSort",
      disabled: false,
    },
    {
      text: "普通积分",
      command: "integralSort",
      disabled: false,
    },
    {
      text: "总积分",
      command: "allIntegralSort",
      disabled: false,
    }
  ];
  mounted() {

  }
  handExport() {
    this.$emit('handExport', true)
  }
  handleCommandsx(val: any) {
    this.sort = val.item.text
    this.sortcommand = val.item.command
    this.sortIndex = ''
  }
  commandSort(val: any) {
    if (this.sortcommand) {
      this.sortIndex = val
      this.$emit("commandsx", val, this.sortcommand);
    } else {
      this.$message.error("请先选择排序");
    }
  }
}
</script>

<style lang="scss">
@import "@/assets/styles/mixins/mixins.scss";

@include b(setClassify) {
  display: flex;
  line-height: 30px;
  // border: 1px solid #dcdfe6;
  overflow: hidden;
  margin-left: 20px;
  border-radius: 50px;
  background-color: #409EFF;
  color: #ffffff;
  border: 1px solid #409EFF;
  position: relative;

  @include e(title) {
    text-align: center;
    padding: 0 20px;
    cursor: pointer;
  }

  @include e(icon) {
    width: 40px;
    text-align: center;
    cursor: pointer;
    vertical-align: middle;
    color: #ffffff;
    font-weight: bold;
  }
}

.all {
  display: flex;

  .el-dropdown-link {
    height: 32px;
    width: 100px;
    line-height: 20px;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 1);
    color: rgba(16, 16, 16, 1);
    font-size: 14px;
    text-align: left;
    border: 1px solid rgba(187, 187, 187, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    margin-left: 17px;

    .el-dropdown {
      width: 80px;

      span {
        cursor: pointer;

      }
    }

    .dropdownsx {
      display: flex;
      flex-direction: column;

      .el-icon1 {
        color: #409EFF;
      }

      .el-icon2 {
        color: #409EFF;
      }
    }
  }

}

.setClassify__icon::after {
  color: #ffffff;
  content: "|";
  position: absolute;
  left: -5px;
  bottom: 1px;
}

.commandClass {
  height: 90px;
  overflow: overlay;
}

.commandClass::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: rgba(0, 0, 0, 0);
}
</style>
