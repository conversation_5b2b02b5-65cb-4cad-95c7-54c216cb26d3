<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 17:16:33
  2025.5.30有改动的页面
  代发货搜索
-->
<template>
  <m-card class="form" hide-text="展开搜索条件" show-text="收起搜索条件" :needToggle="true">
    <el-form ref="form" :model="form" label-width="170px">

      <el-row>
        <el-col :span="7">
          <el-form-item label="区域会员名称">
            <el-input v-model="form.nikeName" placeholder="请输入区域会员名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="申请代理区域名称">
            <el-input v-model="form.applyAgentRegionName" placeholder="请输入申请代理区域名称"></el-input>
          </el-form-item>
        </el-col>
        

        <!-- <el-col :span="10">
          <el-form-item label="区域会员号码">
            <el-input v-model="form.phone" placeholder="请输入区域会员号码"></el-input>
          </el-form-item>
        </el-col> -->

      </el-row>

      <el-row>
        <el-col :span="7">
          <el-form-item label="申请升级的会员等级">
            <el-input v-model="form.memberLevel" placeholder="请输入申请升级的会员等级"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="审核状态">
            <el-select v-model="form.auditStatus" placeholder="请选择审核状态" style="width: 100%" size="small">
              <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="10">
          <el-form-item label="申请时间" >
            <el-date-picker
              v-model="form.applyTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 100%;"
              @change="chooseTimes"
            />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="10">
          <el-form-item label="状态">
            <el-select v-model="form.auditStatus" placeholder="请选择" style="width: 100%" size="small">
              <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->

        <!-- <el-col :span="10">
          <el-form-item label="审核状态">
            <el-select v-model="form.approvalStatus" placeholder="请选择" style="width: 100%" size="small">
              <el-option v-for="item in approvalStatusList" :key="item.value" :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
       
        <!-- <el-col :span="10">
          <el-form-item label="申请时间">
            <el-date-picker v-model="form.date" type="daterange" range-separator="-" start-placeholder="开始时间"
              end-placeholder="结束时间" style="width: 100%;" @change="chooseTimes">
            </el-date-picker>
          </el-form-item>
        </el-col> -->

      </el-row>

      <!-- <el-row>
        <el-col :span="10">
          <el-form-item label="有效期">
            <el-date-picker v-model="form.date" type="daterange" range-separator="-" start-placeholder="开始时间"
              end-placeholder="结束时间" style="width: 100%;" @change="chooseTimes">
            </el-date-picker>
          </el-form-item>
        </el-col>


      </el-row> -->

      
      <el-form-item>
        <el-button type="primary" @click="emitSeach">搜索</el-button>
      </el-form-item>
    </el-form>

  </m-card>
</template>

<script lang="ts">
/* eslint-disable indent */
import { Vue, Component, Watch } from "vue-property-decorator";
import PageManage from "@/components/PageManage.vue";
import DateUtil from "@/store/modules/date";

/** 订单顶部查询表单 */
@Component({
  components: {
    PageManage
  }
})
export default class OrderFrom extends Vue {
  form = {
    date: '',
    createStartTime: '',
    createEndTime: '',

    current: 1,
    size: 20,
    total: 0,
    phone: '',
    nikeName: '',
    mainPrizeName:'',
    productName: '',
    prizeName: '',
    prizeType:"",
    levelName: '',
    verifyNickName:"",
    applyTime: '',
    applyBeginTime: '',
    applyEndTime: '',
    auditStatus: '',
    applyAgentRegionName: '',
    memberLevel: '',
  };
  statusList=[
    {
      value: "",
      label: "全部"
    },
    {
      value: "100",
      label: "待审核"
    },
    {
      value: "101",
      label: "审核通过"
    },
    {
      value: "200",
      label: "驳回"
    },

  ]

  mounted() {

  }

  // chooseTimes(data: any) {
  //   this.form.startTime = data ? this.dateConversion(data[0]) : "";
  //   this.form.endTime = data ? this.dateConversion(data[1]) : "";
  //   // console.log('时间', data[0], this.dateConversion(data), this.searchType);
  // }

  /**
    * 选择有效期
    * @param data 
    */
  chooseTimes(data: any) {
    this.form.applyBeginTime = data ? this.dateConversion(data[0]) : "";
    this.form.applyEndTime = data ? this.dateConversion(data[1])  : "";
    console.log("this.searchType", this.form);

  }
  dateConversion(value: Date) {
    const date = new DateUtil("").getYMDs(value);
    return date;
  }
  // dateConversion(value: Date) {
  //   // const date = new DateUtil("").getYMDHMSs(value);
  //   const date = new DateUtil("").getYMD(value);
  //   return date;
  // }

  /** 触发父级查询 */
  emitSeach() {
    console.log(this.form);
    this.$emit("input-set", this.form);

    // this.$emit("update:query", this.form);
  }


}
</script>

<style lang="scss" scoped>
@include b(form) {
  transform-origin: left top;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease 0s;

  @include e(btn) {
    width: 100%;
    position: absolute;
    bottom: 0;
    text-align: center;
    padding-bottom: 20px;

    span {
      cursor: pointer;
    }
  }
}
</style>
