<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-08-25 14:08:55
 2025.5.28有改动的页面
-->
<template>
	<div>
		<!-- <div class="order__control--top">
			<div class="control--l">
				<el-button-group class="fix" >
					<el-button plain @click="emitFun('addLuckyDraw')" type="primary"
						>新增抽奖活动</el-button>
					<el-button plain @click="emitFun('copyLuckyDraw')" type="primary"
						>复制抽奖方案</el-button>
					
				</el-button-group>
			</div>
		</div> -->

		<template>
			<el-table ref="multipleTable" :data="data" border tooltip-effect="dark" style="width: 100%" height="670"
				@selection-change="handleSelectionChange">
				<el-table-column type="selection" width="55" fixed="left">
				</el-table-column>
				<el-table-column label="区域会员名称" min-width="160" fixed="left" prop="nikeName">

				</el-table-column>
				<el-table-column label="区域会员号码" min-width="160" prop="phone">

				</el-table-column>
				<el-table-column label="区域类型" min-width="140" prop="phone">
					<template slot-scope="{row}">
						<div v-if="row.regionType == 1">
							区/县
						</div>
						<div v-else-if="row.regionType == 2">
							市级
						</div>
						<div v-else-if="row.regionType == 3">
							省级
						</div>
					</template>
				</el-table-column>
				<!-- <el-table-column label="申请时间" min-width="160" prop="auditTime">
				</el-table-column> -->
				<el-table-column label="申请代理区域名称" min-width="160" prop="applyAgentRegionName">
				</el-table-column>
				<el-table-column label="申请升级的会员等级" min-width="160" prop="memberLevel">
				</el-table-column>

				<el-table-column label="审核状态" min-width="120" prop="mobile">
					<template slot-scope="{row}">
						<el-tag type="warning" v-if="row.auditStatus == 100">待审核</el-tag>
						<el-tag type="success" v-else-if="row.auditStatus == 101">审核通过</el-tag>
						<el-tag type="danger" v-else-if="row.auditStatus == 200">驳回</el-tag>
					</template>
				</el-table-column>

				<el-table-column label="审核人" min-width="160" prop="auditPlatformUserName">
				</el-table-column>

				<el-table-column label="审核时间" min-width="180" prop="auditTime">
				</el-table-column>
				<el-table-column label="审核意见" min-width="300" prop="auditReason">
				</el-table-column>

				<el-table-column fixed="right" label="操作" width="160">
					<template slot-scope="scope">

						<el-button-group class="fix">
							<!-- <el-button plain @click="emitFun('detail', scope.row)" type="primary">详情</el-button> -->
							<el-button plain  v-if="scope.row.auditStatus == 100" @click="emitFun('review', scope.row)" type="primary">审核</el-button>


							<!-- <el-button plain class="dropdown__fix more" type="primary">
								<el-dropdown size="mini" trigger="hover">
									<span class="dropdown__fix--more">...</span>
									<el-dropdown-menu slot="dropdown">
										<el-dropdown-item v-if="scope.row.auditStatus == 100" @click.native="emitFun('review', scope.row)">审核</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown>
							</el-button> -->

						</el-button-group>

					</template>
				</el-table-column>
			</el-table>

		</template>

		<div class="order__control--bottom fixed" style="width: 100%">

			<el-pagination small layout="total,  prev, pager, next,  sizes" :current-page.sync="form.current"
				:size.sync="form.size" :page-size.sync="form.size" :page-sizes="[10, 20, 50, 100]" :total.sync="form.total"
				@current-change="handleCurrentChange" @size-change="handleSizeChange">
			</el-pagination>
		</div>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";

import {
	isClose,
	strFilter,
} from "../common/order";

@Component({ filters: { strFilter } })
export default class OderTable extends Vue {
	/** 表格数据 */
	@Prop({ type: Array })
	data!: any[];

	/** 表格内查询按钮 */
	@Prop()
	controlOption!: any[];

	/** 查询条件 */
	@Prop()
	query!: any;

	/** 父级已选条码 */
	@Prop()
	checkedItem!: Array<{ orderId: string }>;

	/** 本地form 主要用户页码切换 */
	get form() {
		return this.query;
	}

	// set form(v) {
	// 	this.$emit("input-set", v);
	// }


	/** 已选表格选项 */
	get tableCheckedItem() {
		return this.checkedItem || [];
	}

	set tableCheckedItem(v) {
		this.$emit("update:checked-item", v);
	}


	multipleSelection: [];



	/**
	 * @method handleSizeChange
	 * @description 每页 条
	 */
	handleSizeChange(val: number) {
		this.form.size = val;
		this.$emit("input-set", this.form);
	}

	/**
	 * @method handleCurrentChange
	 * @description 当前页
	 */
	handleCurrentChange(val: number) {
		this.form.current = val;
		console.log('当前页', val);
		this.$emit("input-set", this.form);
	}



	/** 触发父级方法 */
	emitFun(name: string, data?: any, status?: boolean) {
		// 阻止未选中元素的批量操作
		// if (!data && !this.checkedItem.length && name != 'deliverMessage') {
		// 	return this.$message.info("请先选择条目");
		// }
		this.$emit("table-function", name, data, status);
	}

	/** 物流按钮显示隐藏 */
	logisticsBtnVisible(item: DeliveryOrderList) {
		const other = ["WAIT_FOR_PAY", "WAIT_FOR_SEND", "COMPLETE"];
		return (
			!isClose(item.status) &&
			!other.includes(item.status) &&
			item.deliveryType === "LOGISTICS"
		);
	}


	/**
	* 多选
	*/
	handleSelectionChange(val: any) {
		this.multipleSelection = []
		val.forEach((item: any) => {
			this.multipleSelection.push(item.orderId);
		});
		this.$emit("update:checked-item", val);
		// console.log("fdf=",this.multipleSelection);
		// console.log("val=",val);

	}

}
</script>

<style lang="scss">
.header__tag {
	border-radius: 0;
	margin-right: 10px;
}

.fixed {
	@include flex(space-between);
	position: fixed;
	bottom: 10px;
	width: 990px !important;
	z-index: 10;
	background: #fff;
	padding: 10px 0;
}
</style>