<!--
 * @description: 抽离开源版本
 * @Author: chuyinlong
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 10:45:18
 2025-7-18
-->
<template>
  <div>
    <m-container class="order" :pagination-visible="false" :current.sync="query.current" :size.sync="query.size"
      :total.sync="query.total">
      <MarketingFrom v-model="query" slot="form" @input-set="inputSet" :query.sync="query" />


      <template slot="content">
        <!-- <SetClassify @commandsx="commandValsx" @handExport="handExport" ref="setClass" style="margin-left: 20px"
          :is-item="false" :is-value="false">设置分类</SetClassify> -->

        <!-- <el-tabs v-model="query.status2" @tab-click="handleTabClick">
          <el-tab-pane label="所有申请" name="0"></el-tab-pane>
          <el-tab-pane label="待审核" name="100"> </el-tab-pane>
          <el-tab-pane label="审核通过" name="101"> </el-tab-pane>
          <el-tab-pane label="驳回" name="200"> </el-tab-pane>
        </el-tabs> -->

        <MarketingTable @table-function="tableFunction" :data="dataList" :query.sync="query" @input-set="inputSet"
          :checked-item.sync="checkedItem" :controlOption="getControlOption(query.orderStatus)" />
      </template>
    </m-container>


    <OrderDetailModal v-model="detailVisible" :detail="orderDetail" :need-tabs="true" :is-delivery="true"
      :type="detailType" @reset="handleSeeDetail" @closeDetail="closeDetail" />

    <el-dialog title="审核区域会员" :visible.sync="approvalFlag" width="30%">
      <el-form :model="approvalForm" :rules="rules" ref="approvalForm" label-width="100px">
        <el-form-item label="审核状态" prop="auditStatus">
          <el-select v-model="approvalForm.auditStatus" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核原因" prop="auditReason">
          <el-input type="textarea" :rows="2" placeholder="请输入审核原因" v-model="approvalForm.auditReason"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="approvalFlag = false;">取 消</el-button>
        <el-button type="primary" @click="approvalHandler('approvalForm')">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="规则说明" :visible.sync="ruleDescriptionFlag" width="40%">

      <el-form :model="ruleDescriptionForm" ref="ruleDescriptionForm" label-width="100px">
        <el-form-item label="规则信息" prop="descriptionName">
          <RichEditor :text="ruleDescriptionForm.descriptionName" ref="wEditor"></RichEditor>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="ruleDescriptionFlag = false;">取 消</el-button>
        <el-button type="primary" @click="clickRuleDescription('ruleDescriptionForm')">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Ref } from "vue-property-decorator";
import MarketingFrom from "./components/MarketingFrom.vue";
import MarketingTable from "./components/MarketingTable.vue";
import OrderDetailModal from "./components/detail/Index.vue";
import RichEditor from "@/components/RichEditor.vue";
import SetClassify from "./components/SetClassify.vue";
import {
  DeliveryState,
  DeliveryOrderList,
} from "./orderType";

import { filterEmpty } from "./common/order";

import {
  close,
} from "@/api/order";

import { getDetail } from "@/api/marketing/index"


import { getApplyRegionMemberPage, getApplyRegionMemberAudit } from "@/api/storeApi/storeApi"

Component.registerHooks(["beforeRouteEnter", "beforeRouteUpdate"]);

@Component({
  components: {
    MarketingFrom,
    MarketingTable,
    OrderDetailModal,
    RichEditor,
    SetClassify,
  },
})
export default class DeliveryOrder extends Vue {
  @Ref()
  readonly wEditor: HTMLFormElement;

  checkedItem = null

  detailVisible = false;

  detailType = "1";

  created() {

    //---------------------------
    this.getApplyRegionMemberPage(this.query);
  }



  //-----------------------------------------------
  query: any = {
    date: '',
    startTime: '',
    endTime: '',
    auditStatus: '',
    current: 1,
    size: 20,
    total: 0,
    userPhone: '',
    nikeName: '',
  };

  dataList = [];

  orderDetail = {};

  options = [
    {
      value: '101',
      label: '审核通过'
    },
    {
      value: '200',
      label: '驳回'
    }
  ]

  approvalForm = {
    id: '',
    auditStatus: '',
    auditReason: '',
  }

  approvalFlag = false;
  ruleDescriptionFlag = false;

  descriptionName = ""
  ruleDescriptionForm = {
    descriptionName: ''
  }
  rulesRuleDescription = {
    descriptionName: [
      { required: true, message: '请输入规则说明', trigger: 'blur' }
    ]
  }
  rules = {

    auditStatus: [
      { required: true, message: '请选择审核状态', trigger: 'change' }
    ],
    auditReason: [
      { required: true, message: '请输入审核原因', trigger: 'blur' }
    ]
  }
  commandValsx(val: string, sortcommand: string) {
    console.log(val, sortcommand);

  }
  handExport() {
    this.ruleDescriptionFlag = true;
  }
  changeValue(val: any) {
    console.log("sss", val);
  }
  getDetailHtml() {
    return this.wEditor.getHtml();
    // return (this.$refs.ue as any).getUEContent();
  }
  clickRuleDescription(formName: any) {
    this.ruleDescriptionForm.descriptionName = this.getDetailHtml()
    if (!this.ruleDescriptionForm.descriptionName) {
      this.$message.error('请输入规则说明');
      return;
    }
    this.ruleDescriptionFlag = false;

    console.log("this.ruleDescriptionForm.descriptionName=", this.ruleDescriptionForm.descriptionName);
  }

  approvalHandler(formName: any) {
    this.$refs[formName].validate((valid) => {
      if (valid) {
        console.log("approvalForm=", this.approvalForm);
       
        
        getApplyRegionMemberAudit(this.approvalForm).then(res => {
          this.$message.success("区域会员审核完成")
          this.approvalFlag = false;
          this.query.current = 1;
          this.getApplyRegionMemberPage(this.query);
        }).catch(err => {
          this.$message.error(err);
        })
      } else {
        return false;
      }
    });
  }

  /** 监听query变化 */
  // @Watch("query", { deep: true })
  // handleQueryChange(v: any) {
  //   this.getCurShopPrizeList(filterEmpty({ ...v }));
  //   console.log('监听query变化', v)
  // }

 
  getApplyRegionMemberPage(param: any) {
    getApplyRegionMemberPage(param).then(res => {
      console.log("res111111=", res.data.list);
      this.dataList = res.data.list;
      this.query.total = res.data.total;
    }).catch(err => {
      this.$message.error(err)
    })
  }

  handleTabClick({ name: orderStatus }: { name: string }) {
    if (orderStatus != '0') {
      Object.assign(this.query, {
        auditStatus: orderStatus,
        current: 1,
      });
    } else {
      Object.assign(this.query, {
        auditStatus: null,
        current: 1,
      });
    }
    this.getApplyRegionMemberPage(this.query);
  }

  closeDetail() {
    console.log("closeDetail=");
  }
  inputSet(form: any) {
    console.log("inputSet=", form);
    // this.query = { }
    this.getApplyRegionMemberPage(filterEmpty({ ...form }));
  }

  getDetail(row: any) {
    getDetail({ id: row.id }).then(res => {
      console.log("res111111=", res.data);
      this.orderDetail = res.data;
      this.detailVisible = true;
    }).catch(err => {
      this.$message.error(err)
    })
  }
  //-----------------------------------------------

  getControlOption(orderStatus: any) {
    console.log("handleTabClick=");
  }

  /**
   * 监听table传来的事件
   * @param {name} 事件名 remark | close | detail
   */
  tableFunction(name: string, data: DeliveryOrderList, isLogistics: boolean) {
    console.log("name", name);
    switch (name) {
      case "remark":
        return this.triggerRemark(data);
      case "detail":
        return this.getDetail(data);
      case "addLuckyDraw":
        return this.addLuckyDraw(data);
      case "copyLuckyDraw":
        return this.copyLuckyDraw(data);
      case "review":
        return this.review(data);

    }
  }

  addLuckyDraw(data: any) {
    this.$router.push("/marketing/marketingList/addLuckyDraw")
  }
  copyLuckyDraw(data: any) {

  }
  review(data: any) {
    this.approvalForm.id = data.id;
    this.approvalFlag = true;
  }

  /**
   * 查看详情
   */
  async handleSeeDetail(orderData: DeliveryOrderList, isLogistics: boolean) {
    console.log("orderData", orderData);
    console.log("isLogistics", isLogistics);

    //
  }

  /**
   * 关闭
   * @param {orderData} 订单数据 如果没有参数为批量操作
   */
  handleClose(orderData?: any) {
    this.$confirm("确定关闭订单？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        const orderIds = orderData
          ? [orderData.orderId]
          : [...this.selectedIds];
        close(orderIds)
          .then(() => {
            this.getOrders(Object.assign({}, this.query, this.$route.query));
            this.$message.success("关闭成功");
          })
          .catch((err) => {
            this.$message.warning(err || "关闭失败");
          });
      })
      .catch(() => {
        //
      });
  }


}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/order/order.scss";
</style>
