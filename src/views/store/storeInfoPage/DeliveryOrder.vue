<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 10:45:18
-->
<template>
  <div>
    <m-container class="order" :pagination-visible="false" :current.sync="query.current" :size.sync="query.size"
      :total.sync="query.total">
      <DeliveryOrderFrom v-model="query" slot="form" @input-set="inputSet" />

      <template slot="content">
        <!-- <el-tabs v-model="query.status" @tab-click="handleTabClick">
          <el-tab-pane label="所有预约" name="1"></el-tab-pane>
          <el-tab-pane label="待进行" name="0"> </el-tab-pane>
          <el-tab-pane label="已完成" name="105"> </el-tab-pane>
          <el-tab-pane label="已失效" name="3"> </el-tab-pane>
        </el-tabs> -->

        <DeliveryOrderTable @table-function="tableFunction" :data="dataList" :query.sync="query"
          :checked-item.sync="checkedItem" :controlOption="getControlOption(query.orderStatus)" />
      </template>
    </m-container>
    

    <OrderDetailModal v-model="detailVisible" :detail="orderDetail" :need-tabs="true" :is-delivery="true"
      :type="detailType" @reset="handleSeeDetail" @closeDetail="closeDetail" />

  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch } from "vue-property-decorator";
import DeliveryOrderFrom from "./components/DeliveryOrderFrom.vue";
import DeliveryOrderTable from "./components/DeliveryOrderTable.vue";
import OrderDetailModal from "./components/detail/Index.vue";
import {
  DeliveryState,
  DeliveryOrderList,
} from "./orderType";

import {  filterEmpty } from "./common/order";

import {
  close,
} from "@/api/order";

import { getEvaluatePage, getOrderDetail, } from "@/api/storeApi/storeApi"

Component.registerHooks(["beforeRouteEnter", "beforeRouteUpdate"]);

@Component({
  components: {
    DeliveryOrderFrom,
    DeliveryOrderTable,
    OrderDetailModal,
  },
})
export default class DeliveryOrder extends Vue   {


  checkedItem = null

  detailVisible = false;

  detailType = "1";

  created() {

    //---------------------------
    this.getEvaluatePage(this.query);
  }



  //-----------------------------------------------
  query: any = {
    current: 1,
    size: 20,
    total: 0,

    userName: '',
    successScore: '',
    rate: '',
  };

  dataList = [];

  orderDetail = {};

  /** 监听query变化 */
  // @Watch("query", { deep: true })
  // handleQueryChange(v: any) {
  //   this.getEvaluatePage(filterEmpty({ ...v }));
  //   console.log('监听query变化', v)
  // }

  getEvaluatePage(param: any) {
    getEvaluatePage(param).then(res => {
      console.log("res111111=", res.data.list);
      this.dataList = res.data.list;
      this.query.total = res.data.total;
    }).catch(err => {
      this.$message.error(err)
    })
  }

  handleTabClick(){
    console.log("handleTabClick=");
  }

  inputSet(form : any){
    console.log("inputSet=",form);
    this.getEvaluatePage(filterEmpty({ ...form }));
  }

  getOrderDetail(row : any){
    getOrderDetail({ id : row.id}).then(res => {
      console.log("res111111=", res.data);
      this.orderDetail = res.data;
      this.detailVisible = true;
    }).catch(err => {
      this.$message.error(err)
    })
  }
  //-----------------------------------------------

  getControlOption(orderStatus: any){
    console.log("handleTabClick=");
  }

  /**
   * 监听table传来的事件
   * @param {name} 事件名 remark | close | detail
   */
  tableFunction(name: string, data: DeliveryOrderList, isLogistics: boolean) {
    console.log("name", name);
    switch (name) {
      case "remark":
        return this.triggerRemark(data);
      case "detail":
        return this.getOrderDetail(data);

    }
  }



  /**
   * 查看详情
   */
  async handleSeeDetail(orderData: DeliveryOrderList, isLogistics: boolean) {
    console.log("orderData", orderData);
    console.log("isLogistics", isLogistics);
    
    //
  }

  /**
   * 关闭
   * @param {orderData} 订单数据 如果没有参数为批量操作
   */
  handleClose(orderData?: any) {
    this.$confirm("确定关闭订单？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        const orderIds = orderData
          ? [orderData.orderId]
          : [...this.selectedIds];
        close(orderIds)
          .then(() => {
            this.getOrders(Object.assign({}, this.query, this.$route.query));
            this.$message.success("关闭成功");
          })
          .catch((err) => {
            this.$message.warning(err || "关闭失败");
          });
      })
      .catch(() => {
        //
      });
  }


}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/order/order.scss";
</style>
