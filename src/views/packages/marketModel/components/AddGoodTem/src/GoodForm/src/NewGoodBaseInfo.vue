<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-02 13:20:00
-->
<script src="newGoodBaseInfoType.ts"></script>
<template>
  <div class="pageRoll">
    <el-form :model="formModel" :rules="formRules" ref="formRef" label-width="100px">
      <div class="navLine">基本信息</div>
      <div class="baseMsg">
        <el-form-item label="权益包名称" prop="name">
          <el-input v-model="formModel.name" style="width: 550px" maxlength="30" placeholder="请输入权益包名称"></el-input>
        </el-form-item>
        <el-form-item label="权益包编码" prop="goodsCode">
          <el-input v-if="formModel.goodsCode != null" v-model="formModel.goodsCode" style="width: 550px"
            maxlength="30"></el-input>
          <el-input v-else v-model="commodityCode" style="width: 550px" maxlength="30"></el-input>
        </el-form-item>
        <!-- <el-form-item label="权益包条码" prop="barCode">
          <el-input v-model="formModel.barCode" style="width: 550px" maxlength="30" placeholder="请输入权益包条码"></el-input>
        </el-form-item> -->
        <!-- <el-form-item label="卖点描述">
          <el-input v-model="formModel.saleDescribe" style="width: 550px" maxlength="60"
            placeholder="请输入60字以内卖点描述"></el-input>
        </el-form-item> -->
        <!-- <el-form-item label="销售专区" prop="saleName" v-if="showSale">
          <el-select v-model="formModel.saleName" style="width: 550px" placeholder="请选择销售专区" @change="selectMode">
            <el-option v-for="item in saleApartList" :key="item.id" :label="item.modeName"
              :value="item.modeName"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="展示分类" prop="showCategoryIds">
          <el-select v-model="formModel.showCategoryIds" style="width: 550px" multiple placeholder="请选择分类"
            :popper-append-to-body="false">
            <el-option-group v-for="group in temShowList" :key="group.showCategoryId" :label="group.name">
              <el-option v-for="item in group.showCategoryVos" :key="item.showCategoryId" :label="item.name"
                :value="item.showCategoryId"></el-option>
            </el-option-group>
          </el-select>
          <el-button type="text" class="ml-20" @click="jumpClass(1)">前往设置</el-button>
        </el-form-item>
        <el-form-item label="展示时间" prop="packageShowTime">
          <el-date-picker v-model="formModel.packageShowTime" type="daterange" range-separator="至"
            start-placeholder="开始日期" value-format="yyyy-MM-dd" end-placeholder="结束日期"
            @change="handlePackageShowDateChange">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="使用期限" prop="packageTime">
          <el-date-picker v-model="formModel.packageTime" type="daterange" range-separator="至" start-placeholder="开始日期"
            value-format="yyyy-MM-dd" end-placeholder="结束日期" @change="handlePackageDateChange">
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="供应商">
          <el-select
            v-model="formModel.providerId"
            style="width: 550px"
            placeholder="请选择供应商"
            :popper-append-to-body="false"
          >
            <el-option
              v-for="item in temSupList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
          <el-button type="text" class="ml-20" @click="jumpClass(3)"
            >前往设置</el-button
          >
        </el-form-item> -->
        <!-- <el-form-item label="基本单位">
          <el-select v-model="formModel.unitId" style="width: 550px" placeholder="请选择基本单位"
            :popper-append-to-body="false" @change="setUnit">
            <el-option v-for="item in goodUnitList" :key="item.id" :label="item.unit" :value="item.id"></el-option>
          </el-select>
          <el-button type="text" class="ml-20" @click="jumpClass(6)">前往设置</el-button>
          <el-button type="text" class="ml-20" @click="handleMeasureUnitButtonClick">辅助单位</el-button>
        </el-form-item> -->
        <!-- <el-form-item label="权益包属性">
          <el-select v-model="formModel.attributeId" placeholder="请选择属性模板" style="width: 550px"
            @change="selectTemAttsList" :popper-append-to-body="false" :clearable="true">
            <el-option v-for="item in temAttsList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <el-button type="text" class="ml-20" @click="jumpClass(4)">前往设置</el-button>
          <div class="valueName">
            <div class="flex between">
              <div>属性名称</div>
              <div>属性值</div>
              <div>操作</div>
            </div>
            <div class="valueName__noList" v-if="!showAttributes">
              暂无数据~
            </div>
            <div class="valueName__List" v-for="(item, index) in formModel.productAttributes" :key="index" v-else>
              <el-input v-model="item.name" style="width: 230px" maxlength="10"></el-input>
              <el-input v-model="item.value" style="width: 230px" maxlength="100"></el-input>
              <div @click="formModel.productAttributes.splice(index, 1)" style="width: 30px">
                删除
              </div>
            </div>
          </div>
        </el-form-item>
        <el-button type="primary" plain style="margin-left: 100px; margin-bottom: 20px" size="small"
          @click="addTemAtts">+ 添加属性</el-button> -->
        <el-form-item label="权益包商品" prop="packageProducts">
          <div class="valueName" style="width: 850px;">
            <el-table :key="tableAllKey" :data="formModel.packageProducts" ref="inventorySumTable" style="width: 100%"
              :span-method="objectSpanMethod" show-summary :summary-method="getSummaries">
              <el-table-column prop="mutexGoodId" label="" width="50">
                <template slot-scope="scope">
                  <span></span>
                </template>
              </el-table-column>
              <el-table-column prop="option" label="操作" width="140">
                <template slot-scope="scope">
                  <el-button type="text" style="color: red;" @click="deletePackageProducts(scope.$index)">删除</el-button>
                  <el-button type="text" @click="addSameGoods(scope.row.mutexGoodId)">添加互斥商品</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="productName" label="商品名称" width="160" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="specs" label="商品规格" width="160" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="useNumber" label="数量" width="80">
                <template slot-scope="scope">
                  <el-input :key="tablekey" v-model="scope.row.useNumber" size="small"
                    @input="getAmount(scope.$index)"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="单价" width="80" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="costPrice" label="成本价" width="80" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-input :key="tablekey" v-model="scope.row.costPrice" size="small"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="adjustmentPrice" label="调后价格" width="80" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-input :key="tablekey" v-model="scope.row.adjustmentPrice" size="small"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="金额" width="80" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="notTerm" label="无期限" width="80" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-checkbox v-model="scope.row.notTerm"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="useDays" label="使用天数" width="80" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-input :key="tablekey" v-model="scope.row.useDays" size="small"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="daysRate" label="单次频率天数" width="80" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-input :key="tablekey" v-model="scope.row.daysRate" size="small"></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <el-button type="primary" plain style="margin-left: 100px; margin-bottom: 20px" size="small"
          @click="addTemPackage">+
          添加商品</el-button>
        <el-form-item label="优惠券赠送" prop="couponIds">
          <el-select v-model="formModel.couponIds" style="width: 550px" multiple placeholder="请选择优惠券赠送"
            :popper-append-to-body="false" filterable>
            <el-option v-for="item in couponList" :key="item.id" :label="item.couponName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权益包大图">
          <div class="product" style="display: flex">
            <el-upload class="productImg__uploader product__productImg" :auto-upload="false"
              :on-change="addUploadBigProductImg" action :show-file-list="false" style="width: 250px; height: 120px">
              <img v-if="widePic" :src="widePic" class="product__productImg" style="width: 250px; height: 120px" />
              <i class="el-icon-plus product__productImg--uploader--icon" v-if="!widePic"
                style="width: 250px; line-height: 100px"></i>
              <div class="product__productImg--uploader--add" v-if="!widePic" style="top: 70px; left: 100px">
                添加图片
              </div>
            </el-upload>
          </div>
          <div class="text-info fs-12" style="color: rgba(69, 64, 60, 0.6)">
            尺寸建议750x350（长方形模式）像素以上，大小1M以下
          </div>
        </el-form-item>
        <el-form-item label="权益包视频">
          <div class="productVideo__uploader">
            <el-upload class="productImg__uploader product__productVideo" :auto-upload="false"
              :on-change="uploadProductVideo" action :show-file-list="false">
              <video v-if="formModel.videoUrl !== ''" controls :src="formModel.videoUrl"
                style="width: 100%; height: auto"></video>
              <i v-else class="el-icon-plus product__productVideo--uploader--icon"></i>
            </el-upload>
            <el-upload style="margin-left: 20px" :auto-upload="false" action :on-change="uploadProductVideo"
              :show-file-list="false">
              <el-button type="text">替换</el-button>
            </el-upload>
            <el-button type="text" style="margin-left: 20px" class="fc-fe5f4b" @click="deleteVideoUrl">删除</el-button>
          </div>
          <div class="text-info fs-12" style="color: rgba(69, 64, 60, 0.6)">
            请上传5M以内的视图视频（该功能免费试用）
          </div>
        </el-form-item>
        <el-form-item label="权益包主图" prop="pic">
          <div class="product" style="display: flex">
            <draggable v-model="productImgList">
              <div v-for="(item, index) in productImgList" :key="index" style="position: relative; margin-right: 20px"
                class="product__productImg" draggable="true">
                <el-upload class="productImg__uploader product__productImg" :auto-upload="false"
                  :on-change="uploadProductImg" action :show-file-list="false">
                  <img v-if="item.img" :src="item.img" @click="productImgIndex = index" class="product__productImg" />
                </el-upload>
                <div v-if="index === 0" class="productImg-text">封面图</div>
                <i class="el-icon-circle-close productImg-icon" v-if="item.img" @click="cancelImg(item, index)"></i>
              </div>
            </draggable>
            <el-upload class="productImg__uploader product__productImg" :auto-upload="false"
              :on-change="addUploadProductImg" action :show-file-list="false" v-if="productImgList.length < 6">
              <i class="el-icon-plus product__productImg--uploader--icon"></i>
              <div class="product__productImg--uploader--add">添加图片</div>
            </el-upload>
          </div>
          <div class="text-info fs-12" style="color: rgba(69, 64, 60, 0.6)">
            尺寸建议750x750（正方形模式）像素以上，大小1M以下，最多6张
            (可拖拽图片调整顺序 )
          </div>
        </el-form-item>
      </div>
      <div class="navLine">物流设置</div>
      <div class="sendMsg">
        <el-form-item label="配送方式" prop="distributionMode">
          <el-radio v-model="formModel.distributionMode" :label="1" @change="getLogModel">快递配送</el-radio>
        </el-form-item>
        <el-form-item label="运费设置" prop="freightTemplateId" v-if="formModel.distributionMode === 1">
          <el-select v-model="formModel.freightTemplateId" placeholder="请选择运费模板" @change="selectLogModel"
            :popper-append-to-body="false">
            <el-option v-for="item in logModel" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <el-button type="text" class="ml-20" @click="jumpClass(5)">前往设置</el-button>
          <LogModel :templateId="formModel.freightTemplateId" ref="logRef" @getModelOption="getModelOption"></LogModel>
        </el-form-item>
      </div>
      <div class="navLine" style="background: #deecf0;">佣金设置</div>
      <div class="sendMsg">
        <el-form-item label="分销佣金类型">
          <el-radio-group :disabled="ruleShow != 102" v-model="formModel.ruleType">
            <el-radio :label="100">固定金额</el-radio>
            <el-radio :label="101">百分比</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row>
          <el-col :span="2" :align="'right'">
            <div>分销佣金 </div>
          </el-col>
          <el-col :span="12">
            <div class="DistributionCommission">
              <div class="commission__top">
                <span class="span1">1级分销佣金</span>
                <input v-model="formModel.parentReceive" :disabled="ruleShow != 102" class="distribution__commission" />
                <span class="span2">{{ formModel.ruleType == '100' ? '元' : '%' }}</span>
              </div>
              <div class="commission__top">
                <span class="span1">2级分销佣金</span>
                <input v-model="formModel.aboveParentReceive" :disabled="ruleShow != 102"
                  class="distribution__commission" />
                <span class="span2">{{ formModel.ruleType == '100' ? '元' : '%' }} </span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!--    <el-dialog :visible.sync="showRegion" width="400px" :before-close="cancel">
      <span slot="title">
        <div class="dialogTitle">{{ addFlag ? "添加" : "编辑" }}计量单位</div>
      </span>
      <div class="dialogName">计量单位名称</div>
      <div v-for="(item, index) in addList" :key="index" class="dialogList">
        <div class="dialogList__item">
          <el-input v-model="item.modeName" style="width: 200px"></el-input>
          <el-button
              type="text"
              @click="deleteRegion(item, index)"
              v-if="index !== 0"
          >删除</el-button
          >
        </div>
      </div>
      <div>
        <el-button type="text" @click="textAdd" v-if="addFlag">添加</el-button>
      </div>
      <span slot="footer" class="dialog&#45;&#45;footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="sureDeal">确 定</el-button>
      </span>
    </el-dialog>-->

    <el-dialog :visible.sync="showRegion" width="400px" :before-close="cancel">
      <span slot="title">
        <div class="dialogTitle">设置辅助单位</div>
      </span>
      <div class="dialogList">
        <div class="dialogList__item">
          <el-form :model="formModel" :rules="formRules" ref="formRef" label-width="100px">
            <el-form-item label="最小单位">
              <el-select v-model="formModel.unitId" style="width: 200px" placeholder="请选择最小单位"
                :popper-append-to-body="false" width="400px" @change="setUnit">
                <el-option v-for="item in goodUnitList" :key="item.id" :label="item.unit" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="辅助单位">
              <el-select v-model="secUnitItem.secUnitId" style="width: 200px" placeholder="请选择辅助单位"
                :popper-append-to-body="false" width="400px" @change="setSecUnit">
                <el-option v-for="item in goodUnitList" :key="item.id" :label="item.unit" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="换算比例">
              <el-input v-model="secUnitItem.unitd" style="width: 100px" maxlength="30" placeholder="换算比例"></el-input>
              <span class="miniUnitSpan">{{ formModel.unit }}</span>
              <span> ≈ 1 </span>
              <span>{{ secUnit }}</span>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog--footer">
        <el-button @click="editSecUnit(false)">取 消</el-button>
        <el-button type="primary" @click="showRegion = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" highlight-current-row width="60%" :before-close="handleClose">
      <el-form ref="form" :model="searchType" label-width="90px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="商品名称">
              <el-input v-model="searchType.productName" placeholder="请输入商品名称" style="width: 200px;"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="展示分类">
              <el-select v-model="searchType.showCategoryId" style="width: 200px" placeholder="请选择分类"
                :popper-append-to-body="false">
                <el-option label="全部" :value="''" />
                <el-option-group v-for="group in temAllShowList" :key="group.showCategoryId" :label="group.name">
                  <el-option v-for="item in group.showCategoryVos" :key="item.showCategoryId" :label="item.name"
                    :value="item.showCategoryId"></el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品状态">
              <el-select v-model="searchType.status" placeholder="请选择状态" style="width: 150px" clearable>
                <el-option label="全部" :value="''" />
                <el-option v-for="tag in statusList" :key="tag.value" :label="tag.key" :value="tag.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button @click="searchGoods" type="primary" size="mini" round style="margin-top: 2px;">搜索</el-button>
          </el-col>
        </el-row>
        <div style="border: 1px solid #ccc;width: 100%;margin: 20px 0;"></div>
      </el-form>
      <el-table ref="multipleTable" :data="goodList" tooltip-effect="dark" style="width: 100%" :row-key="getRowKeys"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" :reserve-selection="true">
        </el-table-column>
        <el-table-column label="序号" type="index">
        </el-table-column>
        <el-table-column width="20">
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="originalPrice" label="商品指导价" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="price" label="商品实售价" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="specs" label="商品规格" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="categoryName" label="商品分类" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="status" label="商品状态" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.status == '0'">下架</span>
            <span v-if="scope.row.status == '1'">上架</span>
          </template>
        </el-table-column>
      </el-table>
      <PageManage :pageSize="size" :pageNum="current" :total="total" @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange" style="margin-top: 0px">
      </PageManage>
      <span slot="footer" class="dialog-footer">
        <!-- <el-button @click="toggleSelection([selectionList[0],tableData[0]])">切换第二、第三行的选中状态</el-button> -->
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="primaryButton">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref, Prop, Watch } from "vue-property-decorator";
import draggable from "vuedraggable";
import LogModel from "@/views/logistics/common/LogModel.vue";
import AddGood from "@/views/goods/marketModel/AddGood.vue";
import NewGoodForm from "./NewGoodForm.vue";
import {
  GoodDetailInfo,
  GoodCategroyType,
  GoodSupplierType,
  GoodUnit,
  GoodAttributeTempType,
  FreightTempType,
  MeasureUnit,
  ProductSecUnitType
} from "@/views/goods/marketModel/goodType";
import { ApiSpecArea } from "@/views/goods/marketModel/marketType";
import { AddSubmitFormType } from "./newGoodType";
import {
  AnyMixinGoodDetailInfo,
  NewGoodBaseState
} from "./newGoodBaseInfoType";
import { upLoad } from "@/api/index";

import {  
  getShopCouponVoList
} from "@/api/certificateApi/certificateApi"
import {
  getAllCategory,
  getAllAttsList,
  getLogistics,
  getAllRegionList,
  getAllSupList,
  getGoodUnitAll,
  GoodsCode,
  PackageGoodsCode,
  getAddProductPackageVo,
  queryShowCategoryListAll
} from "@/api/good/goods";
import storage from "@/libs/storage";
import { readFile, advanceGetImage } from "@/store/modules/voidImg";
import { ElForm } from "element-ui/types/form";
import { ElUploadInternalFileDetail } from "element-ui/types/upload";
import { getCommissionRule } from "@/api/withdrawalApi/withdrawalApi";
import PageManage from "@/components/PageManage.vue";
import Big from 'big.js';
/**
 * 编辑基本信息
 */
@Component({
  components: {
    draggable,
    LogModel,
    PageManage
  }
})
export default class NewGoodBaseInfo extends Vue implements NewGoodBaseState {
  @Prop({
    type: Object,
    default() {
      return null;
    }
  })
  readonly goodDetail!: GoodDetailInfo;

  @Ref()
  readonly logRef!: LogModel;
  // 存放数据的对象
  formModel = {} as Partial<AddSubmitFormType>;

  widePic = "";
  showAttributes = false;

  showPackages = false;
	statusList = [
		{ key: '上架', value: '1' },
		{ key: '下架', value: '0' },
	];
  /** 基本信息验证 */
  formRules = {
    name: [{ required: true, message: "请输入权益包名称", trigger: "blur" }],
    saleName: [{ required: true, message: "请选择销售专区", trigger: "blur" }],
    packageShowTime: [{ required: true, message: "请选择展示时间", trigger: "blur" }],
    packageTime: [{ required: true, message: "请选择使用期限", trigger: "blur" }],
    showCategoryIds: [
      { required: true, message: "请选择分类", trigger: "blur" }
    ],
    packageProducts:[
      { required: true, message: "请选择商品", trigger: "blur" }
    ],
    pic: [
      {
        required: true,
        validator: (
          _rule: any,
          value: boolean,
          callback: (arg?: Error) => void
        ) => {
          if (!value) {
            callback(new Error("请上传权益包主图"));
          } else {
            callback();
          }
        },
        trigger: "change"
      }
    ],
    distributionMode: [
      {
        required: true,
        message: "请选择配送方式",
        trigger: "change"
      }
    ],
    freightTemplateId: [
      {
        required: true,
        message: "请选择物流模板",
        trigger: "change"
      }
    ]
  };

  saleApartList: Array<ApiSpecArea> = [];

  temShowList: Array<GoodCategroyType> = [];

  couponList = []  

  temAllShowList: Array<GoodCategroyType> = [];

  temSupList: Array<GoodSupplierType> = [];

  goodUnitList: Array<GoodUnit> = [];

  temAttsList: Array<GoodAttributeTempType> = [];

  logModel: Array<FreightTempType> = [];

  productImgList: Array<{ img: string }> = [];

  productImgIndex = 0;

  mutexGoodId = "";

  secUnitItem: Partial<ProductSecUnitType> = {
    id: "",
    secUnitId: "",
    unitd: 0,
    secUnitd: 0
  };

  secUnit = "";
  tableAllKey = 0;
  tablekey = false;
  commodityCode = "";
  dialogVisible = false;
  goodList = [];
  current = 1;
  size = 10;
  total = 0;
  searchType = {
    productName:"",
    showCategoryId:"",
    status:"",
  }
  selectedArray  = []
  showSale = false;


  saleMode: string | number | null = null;

  showRegion = false;

  addFlag = true;

  deleteType = false;
  ruleShow=null
  addList: Array<Partial<ApiSpecArea>> = [
    {
      modeName: "",
      id: null
    }
  ];

  tempMeasureUnitList: Array<MeasureUnit> = [];

  @Ref()
  readonly formRef!: ElForm;

  @Watch("formModel.productAttributes")
  getShowAttributes() {
    this.showAttributes = this.formModel.productAttributes
      ? this.formModel.productAttributes.length > 0
        ? true
        : false
      : false;
  }

  @Watch("formModel.packageProducts")
  getShowPackages() {
    this.showPackages = this.formModel.packageProducts
      ? this.formModel.packageProducts.length > 0
        ? true
        : false
      : false;
  }
  

  /**
   * 还原表单数据(初始化数据)
   */
  @Watch("goodDetail")
  setFormModel(nVal: AnyMixinGoodDetailInfo) {
    if (nVal) {
      const parentHtml = this.$parent as NewGoodForm;
      getAllRegionList({})
        .then((res) => {
          this.saleApartList = res.data;
        })
        .then(async () => {
          const formModel = this.goodDetail
            ? (JSON.parse(
                JSON.stringify(this.goodDetail)
              ) as AnyMixinGoodDetailInfo)
            : (storage.get("formModel") as AnyMixinGoodDetailInfo);
          for (const key of Object.keys(formModel)) {
            const value = nVal[key];
            if (value) {
              formModel[key] = value;
            }
          }
          // formModel.ruleType=100
          // formModel.aboveParentReceive=100
          // formModel.parentReceive=100
          if (formModel.productSecUnits.length === 0) {
            formModel.productSecUnits.push(this.secUnitItem);
          }
          if (
            formModel.productSecUnits &&
            formModel.productSecUnits.length === 1
          ) {
            if (formModel.productSecUnits[0].secUnitId !== "") {
              this.setSecUnit();
              const secUnitItemData = formModel.productSecUnits[0];
              this.secUnitItem = secUnitItemData;
            }
          } else {

          }
          formModel.showCategoryIds = this.getShowCategoryIds(
            nVal.productShowCategorys
          );
          formModel.freightTemplateId =
            formModel.freightTemplateId === "0"
              ? "0"
              : formModel.freightTemplateId;
          formModel.videoUrl = formModel.videoUrl ? formModel.videoUrl : "";
          this.widePic = formModel.widePic;
          this.showSale = parentHtml.from === "csv" ? true : false;
          if (parentHtml.from === "csv") {
            formModel.distributionMode = 1;
            formModel.freightTemplateId = "0";
          }
          if (this.showSale) {
            formModel.saleName = this.saleApartList[0].modeName;
          }
          this.productImgList = this.getProductImgList(nVal.albumPics);
          this.formModel = formModel;
          //展示时间
          let packageShowStartTime = this.formModel.packageShowStartTime
          let packageShowEndTime = this.formModel.packageShowEndTime  
          let packageShowTime = [];
          packageShowTime.push(packageShowStartTime);
          packageShowTime.push(packageShowEndTime);
          this.$set(this.formModel,'packageShowTime',packageShowTime);
          //使用期限
          let packageStartTime = this.formModel.packageStartTime
          let packageEndTime = this.formModel.packageEndTime
          let packageTime = [];
          packageTime.push(packageStartTime);
          packageTime.push(packageEndTime);
          this.$set(this.formModel,'packageTime',packageTime);

          if (this.showSale) {
            const dropItem = this.saleApartList.filter((item) => {
              return item.modeName === this.formModel.saleName;
            })[0];
            if (dropItem) {
              this.saleMode = dropItem.id;
              this.formModel.saleMode = dropItem.id;
              this.getTemShowList(); // 展示分类
            }
          }
        });
    }
  }

  @Watch("formModel.saleName")
  getDropItem() {
    const dropItem = this.saleApartList.filter((item) => {
      return item.modeName === this.formModel.saleName;
    })[0];
    if (dropItem) {
      this.saleMode = dropItem.id;
      this.formModel.saleMode = dropItem.id;
      this.getTemShowList(); // 展示分类
    }
  }
  getAmount(index){
    this.tablekey = !this.tablekey
    let packageProducts = this.formModel.packageProducts
    packageProducts[index].amount = packageProducts[index].price * packageProducts[index].useNumber
    this.formModel.packageProducts = packageProducts
    this.tableAllKey =this.tableAllKey + 1
  }
  /**
   * 点击下拉销售区域
   */
  selectMode() {
    this.temShowList = [];
    this.formModel.showCategoryIds = [];
  }

  mounted() {
    const parentHtml = this.$parent.$parent.$parent.$parent.$parent as AddGood;
    this.saleMode = parentHtml.saleMode || parentHtml.saleMode[0];
    storage.delete("allFoorm");
    this.getTemShowList(); // 展示分类
    this.getAllCategoryList();
    this.getTemSupList(); // 供应商模板
    this.getGoodUnit(); // 基本单位
    this.getTemAttsList(); // 属性模板
    this.getLogis(); //  物流模板
    this.GoodsCode();
    this.getCouponList();
    this.getRule()//“佣金设置”
    if (storage.get("formModel")) {
      this.formModel = JSON.parse(JSON.stringify(storage.get("formModel")));
      // this.formModel.ruleType=100
      this.widePic = this.formModel.widePic ? this.formModel.widePic : "";
      this.formModel.videoUrl = this.formModel.videoUrl
        ? this.formModel.videoUrl
        : "";
      this.productImgList = this.getProductImgList(
        storage.get("formModel").albumPics
      );
      if (this.formModel.saleName) {
        this.showSale = true;
        this.setFormModel(storage.get("formModel"));
      }
      if (this.formModel.skuStocks && this.formModel.skuStocks.length === 1) {
        this.formModel.originalPrice =
          this.formModel.skuStocks[0].originalPrice;
        this.formModel.price = this.formModel.skuStocks[0].price;
        this.formModel.stock = Number(this.formModel.skuStocks[0].stock);
      }

      if (storage.get("formModel").id) {
        this.formModel.showCategoryIds = this.getShowCategoryIds(
          storage.get("formModel").productShowCategorys
        );
      }
    } else {
      this.getModel();
    }
  }
  handlePackageShowDateChange(value){
    let packageShowTime = this.formModel.packageShowTime;
    this.formModel.packageShowStartTime = packageShowTime[0];
    this.formModel.packageShowEndTime = packageShowTime[1];
  }
  handlePackageDateChange(value){
    let packageTime = this.formModel.packageTime;
    this.formModel.packageStartTime = packageTime[0];
    this.formModel.packageEndTime = packageTime[1];
  }
  getSummaries(param) {
    const { columns, data } = param;
    const sums = [];
    sums[0]='合计'
    columns.forEach((column, index) => {
      let property = column.property
      if(property=='useNumber'||property=='amount'){
        let sumData = 0
        data.forEach(e => {
          sumData = Big(sumData).plus(e[property]).toFixed(2)
        });
        sums[index] = sumData
      }
    });
    return sums;
  }
  getRowKeys(row) {
    return row.productId + '-' +row.skuId //唯一性
  }
    /**
  * @method handleSizeChange
  * @description 每页 条
  */
  handleSizeChange(val: number) {
    this.size = val;
    this.getGoodsList()
  }
    /**
 * @method handleCurrentChange
 * @description 当前页
 */
 handleCurrentChange(val: number) {
    this.current = val;
    this.getGoodsList()
    // this.getPageTicket();
  }

  handleSelectionChange(val) {  
    this.selectedArray = val
  }
  handleClose(done: any) {
    this.mutexGoodId = "";
    this.selectedArray = [];
    this.$refs.multipleTable.clearSelection();
    done();
  }
  close(){
    this.mutexGoodId = "";
    this.selectedArray = []
    this.dialogVisible = false;
    this.$refs.multipleTable.clearSelection();
  }
  generateId() {
    // 生成唯一ID
    return  Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
  }
  objectSpanMethod(obj) {
    var prop = obj.column.property;
    var row = obj.row;   
    var rowIndex =  obj.rowIndex;
    var columnIndex = obj.columnIndex;
    if (columnIndex === 0) {
      if (rowIndex === 0 || row.mutexGoodId != this.formModel.packageProducts[rowIndex - 1].mutexGoodId) {
        var rowspan = 0;
        this.formModel.packageProducts.forEach((element) => {
          if (element.mutexGoodId === row.mutexGoodId) {
              rowspan++;
          }
        });
        return [rowspan, 1];
      }else{
        return [0, 0];
      }
    }
  }
  primaryButton() {
    this.dialogVisible = false;
    let choosePackageProducts = this.formModel.packageProducts
    this.selectedArray.forEach(row => {
      if(this.mutexGoodId!=null&&this.mutexGoodId!=undefined&&this.mutexGoodId!=""){
        row.mutexGoodId = this.mutexGoodId
      }else{
        let id = this.generateId();
        row.mutexGoodId = id
      }
      const result = choosePackageProducts.find((element) => {
          return element.productId == row.productId&&element.skuId == row.skuId;
      });
      if(result!=null&&result!=undefined&&result!=""){
        row.startTime = result.startTime
        row.endTime = result.endTime
        row.useNumber = result.useNumber
        row.amount = row.useNumber * row.price
      }else{
        row.useNumber = 1
        row.amount = row.useNumber * row.price
      }
    });
    this.mutexGoodId = "";
    this.formModel.packageProducts = this.formModel.packageProducts.concat(this.selectedArray)

    this.dataSort();

    this.selectedArray = [];
    this.$refs.multipleTable.clearSelection();
  }
  dataSort(){
    this.formModel.packageProducts.sort(function(a, b){
        let mutexGoodIdA = a.mutexGoodId.toUpperCase();
        let mutexGoodIdB = b.mutexGoodId.toUpperCase();
        if(mutexGoodIdA<mutexGoodIdB){
            return -1;
        }
        if(mutexGoodIdA>mutexGoodIdB){
            return 1;
        }
      } 
    );
  }
  deletePackageProducts(index){
    this.formModel.packageProducts.splice(index, 1);
    this.dataSort();
  }
  addSameGoods(mutexGoodId){
    this.mutexGoodId = mutexGoodId;
    this.addTemPackage()
  }
  /**
   * 添加权益包商品
   */
   addTemPackage(){
    this.current = 1;
    this.size = 10;
    this.searchType = {
      productName:"",
      showCategoryId:"",
      status:"1",
    }
    this.getGoodsList();
    setTimeout(() => {
      let rows = this.formModel.packageProducts
      let goodList = this.goodList;
      this.$refs.multipleTable.clearSelection();
      if (rows) {
          rows.forEach(row => {
              const result = goodList.find((element) => {
                  return element.productId == row.productId&&element.skuId == row.skuId;
              });
          });
      } else {
          this.$refs.multipleTable.clearSelection();
      }
    }, 100);
  }
  searchGoods(){
    this.current = 1;
    this.size = 10;
    this.getGoodsList();
  }
  getGoodsList(){
    let params = this.searchType
    params.current = this.current
    params.size = this.size
    let dataList = this.formModel.packageProducts;
    let skuIds = [];
    if(dataList!=null&&dataList.length>0){
      dataList.forEach(element => {
        skuIds.push(element.skuId);
      });
    }
    params.skuIds = skuIds
    getAddProductPackageVo(params).then((res) => {
      this.goodList = res.data.list
      this.total = res.data.total
      this.dialogVisible = true
    }).catch((err) => {
      this.$message.error(err)
    })
  }




	getRule(){
		getCommissionRule({}).then((res)=>{
			if(res.data==null){
				return
			}
      this.ruleShow=res.data.ruleType
		}).catch((err)=>{
			this.$message(err)
		})
	}
  getModel() {

    if (!this.goodDetail) {
      const parentHtml = this.$parent as NewGoodForm;
      this.formModel = parentHtml.goodFormModel;
      this.formModel.saleMode = String(this.saleMode);
      this.formModel.freightTemplateId = "0";
      if (!this.formModel.productSecUnits) {
        this.formModel.productSecUnits = [];
        this.formModel.productSecUnits.push(this.secUnitItem);
      }
    }
  }

  /**
   * 获取表单数据
   */
  async getFormModel() {
    try {
      this.setAlbumPics();
      await this.validate();
      // this.setSortingCategoryName();
      this.setProviderName();
      this.setUnit();
      this.setSecUnit();
      const formModel = this.formModel;
      const productShowCategorys = this.getProductShowCategorys();
      this.formModel.freightTemplateId = this.logRef.templateId;
      return {
        ...formModel,
        productShowCategorys
      };
    } catch (error) {
      return Promise.reject(error);
    }
  }

  /**
   * 对整个表单进行校验
   */
  validate() {
    return this.formRef.validate();
  }

  /**
   * 移除表单项的校验结果
   */
  clearValidate() {
    this.formRef.clearValidate();
  }

  /**
   * 获取展示分类
   */
  async getTemShowList() {
    const param = {
      saleMode: this.saleMode
    };
    const { data } = await getAllCategory(param);
    console.log("getTemShowList",data);
    this.temShowList = JSON.parse(JSON.stringify(data)) || [];
  }

  async getCouponList(){
    const param = {}
    const { data } = await getShopCouponVoList(param);
    this.couponList = JSON.parse(JSON.stringify(data)) || [];
    console.log(this.couponList);
  }

  async getAllCategoryList() {
    const param = {
      
    };
    const { data } = await queryShowCategoryListAll(param);
    this.temAllShowList = JSON.parse(JSON.stringify(data)) || [];
  }


  /**
   * 获取供应商
   */
  async getTemSupList() {
    const { data } = await getAllSupList({});
    this.temSupList = data || [];
  }

  /**
   * 获取权益包基本单位
   */
  async getGoodUnit() {
    const { data } = await getGoodUnitAll({});
    this.goodUnitList = data;
  }

  /**
   * 设置供应商名称
   */
  setProviderName() {
    const providerId = this.formModel.providerId;
    const currentTempSup = this.temSupList.find(
      (item) => item.id === providerId
    );
    this.formModel.providerName = currentTempSup ? currentTempSup.name : "";
  }

  /**
   * 设置基本单位
   */
  setUnit() {
    const unitId = this.formModel.unitId;
    const unit = this.goodUnitList.find((item) => item.id === unitId);
    this.formModel.unit = unit ? unit.unit : "";
  }

  /**
   * 设置辅助单位
   */
  setSecUnit() {
    if (
      this.formModel.productSecUnits &&
      this.formModel.productSecUnits.length === 1
    ) {
      if (this.formModel.productSecUnits[0].secUnitId !== "") {
        const secUnitId = this.formModel.productSecUnits[0].secUnitId;
        const secUnit = this.goodUnitList.find((item) => item.id === secUnitId);
        this.secUnit = secUnit ? secUnit.unit : "";
      }
    }
  }

  /**
   * 获取展示分类
   */
  getProductShowCategorys() {
    const vals = this.formModel.showCategoryIds as (string | number)[];
    const productShowCategorys: Omit<
      GoodCategroyType,
      "saleMode" | "showCategoryVos" | "sort"
    >[] = [];
    this.temShowList.forEach((item) => {
      const productShowCategorySeconds = item.showCategoryVos.filter(
        (child) => {
          return vals.includes(child.showCategoryId);
        }
      );
      if (productShowCategorySeconds.length) {
        const parentItem = {
          id: item.id,
          name: item.name,
          parentId: item.parentId,
          showCategoryId: item.showCategoryId,
          productShowCategorySeconds: productShowCategorySeconds.map(
            (seconds) => {
              return { ...seconds };
            }
          )
        };
        productShowCategorys.push(parentItem);
      }
    });
    return productShowCategorys;
  }

  /**
   * 还原展示分类id
   */
  getShowCategoryIds(
    showCategorys: Omit<
      GoodCategroyType,
      "saleMode" | "showCategoryVos" | "sort"
    >[] = []
  ) {
    let showCategoryId: string[] = [];
    showCategorys.forEach((item) => {
      if (item.productShowCategorySeconds) {
        const ids = item.productShowCategorySeconds.map((child) =>
          String(child.showCategoryId)
        );
        showCategoryId = showCategoryId.concat(ids);
      }
    });
    return showCategoryId;
  }

  /**
   * 获取权益包属性
   */
  async getTemAttsList() {
    const { data } = await getAllAttsList({});
    this.temAttsList = data || [];
  }

  /**
   * 取消辅助单位修改编辑
   */
  editSecUnit(state: boolean) {
    this.$confirm(
      `确定要退出${
        state ? "新增" : "编辑"
      }辅助单位页面?退出后，未保存的信息将不会保留!`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    ).then(() => {
      if (
        this.formModel.productSecUnits &&
        this.formModel.productSecUnits.length === 1
      ) {
        this.secUnitItem = this.formModel.productSecUnits[0];
      } else {
        this.secUnitItem = {
          id: "",
          secUnitId: "",
          unitd: 0,
          secUnitd: 0
        };
      }
      this.showRegion = false;
    });
  }

  /**
   * 选择权益包属性
   */
  selectTemAttsList(temAttsId: number) {
    const currenttemAtts = this.temAttsList.find(
      (item) => item.id === temAttsId
    );
    if (currenttemAtts) {
      this.formModel.attribute = currenttemAtts.name;
      this.formModel.productAttributes = currenttemAtts.attributeTemplates.map(
        (item) => {
          return {
            ...item,
            value: item.content
          };
        }
      );
    } else {
      this.formModel.attribute = undefined;
      this.formModel.productAttributes = [];
    }
  }

  /**
   * 添加权益包属性
   */
  addTemAtts() {
    const obj = {
      content: "",
      name: "",
      id: "",
      parentId: "",
      value: ""
    };
    if (this.formModel.productAttributes) {
      this.formModel.productAttributes.push(obj);
    }
  }


  /**
   * 跳转分类/供货商/属性模板
   */
  async jumpClass(type: number) {
    let location: any;
    const parentHtml = this.$parent as NewGoodForm;
    const outerParentHtml = this.$parent.$parent.$parent.$parent
      .$parent as AddGood;
    this.setAlbumPics();
    if (parentHtml.from === "csv" && this.formModel.productShowCategorys) {
      this.formModel.productShowCategorys = this.getProductShowCategorys();
      storage.set("formModel", JSON.parse(JSON.stringify(this.formModel)));
      storage.set("allFoorm", JSON.parse(JSON.stringify(this.formModel)));
    } else {
      if (!this.goodDetail) {
        parentHtml.allFoorm = Object.assign(
          parentHtml.allFoorm,
          this.formModel
        );
        storage.set(
          "formModel",
          JSON.parse(JSON.stringify(parentHtml.allFoorm))
        );
        storage.set(
          "allFoorm",
          JSON.parse(JSON.stringify(parentHtml.allFoorm))
        );
      } else {
        storage.set("formModel", JSON.parse(JSON.stringify(this.goodDetail)));
        storage.set("allFoorm", JSON.parse(JSON.stringify(this.goodDetail)));
      }
    }
    outerParentHtml.saveFlag = true;
    switch (type) {
      case 1:
        location = {
          name: "class",
          query: {
            options: "1",
            saleMode: this.saleMode || this.formModel.saleMode
          },
          params: {
            options: "1",
            saleMode: this.saleMode || this.formModel.saleMode
          }
        };
        break;
      case 2:
        location = {
          name: "class",
          query: {
            options: "2",
            saleMode: this.saleMode || this.formModel.saleMode
          },
          params: {
            options: "2",
            saleMode: this.saleMode || this.formModel.saleMode
          }
        };
        break;
      case 3:
        location = {
          name: "sup",
          query: { options: "sup" },
          params: { options: "sup" }
        };
        break;
      case 4:
        location = {
          name: "Attribute",
          query: { options: "pick" },
          params: { options: "sup" }
        };
        break;
      case 5:
        location = {
          name: "logistics",
          query: { options: "log" },
          params: { options: "sup" }
        };
        break;
      case 6:
        location = {
          name: "measureUnit",
          query: { options: "sup" },
          params: { options: "sup" }
        };
        break;
    }
    this.$router.push(location);
  }

  /**
   * @method uploadProductImg
   * @description 修改权益包主图
   */
  async uploadProductImg(file: ElUploadInternalFileDetail) {
    const whiteList = ["image/jpeg", "image/jpg", "image/png"];

    const isLt1M = file.size < 1 * 1024 * 1024;
    if (!whiteList.includes(file.raw.type)) {
      this.$message.error("上传文件只能是 JPG或PNG 格式!");
      return;
    }
    if (!isLt1M) {
      this.$message.error("上传文件大小不能超过 1MB!");
      return;
    }

    const res = await upLoad({
      file: file.raw
    });
    this.productImgList[this.productImgIndex].img = res.data;
    this.formRef.clearValidate("pic");
  }

  /**
   * 上传图片
   */
  async addUploadProductImg(file: ElUploadInternalFileDetail) {
    const whiteList = ["image/jpeg", "image/jpg", "image/png"];

    const isLt1M = file.size < 1 * 1024 * 1024;
    if (!whiteList.includes(file.raw.type)) {
      this.$message.error("上传文件只能是 JPG或PNG 格式!");
      return;
    }
    if (!isLt1M) {
      this.$message.error("上传文件大小不能超过 1MB!");
      return;
    }

    const res = await upLoad({
      file: file.raw
    });
    this.productImgList.push({
      img: res.data
    });
  }

  /**
   * 上传大图片
   */
  async addUploadBigProductImg(file: ElUploadInternalFileDetail) {
    const whiteList = ["image/jpeg", "image/jpg", "image/png"];

    const isLt1M = file.size < 1 * 1024 * 1024;
    if (!whiteList.includes(file.raw.type)) {
      this.$message.error("上传文件只能是 JPG或PNG 格式!");
      return;
    }
    if (!isLt1M) {
      this.$message.error("上传文件大小不能超过 1MB!");
      return;
    }

    const imgFile = await readFile(file.raw);
    const newImg = await advanceGetImage(imgFile);
    if (newImg.image.width !== 750 || newImg.image.height !== 350) {
      this.$message.error("宽屏图片长宽应为750 * 350");
      return;
    }

    const res = await upLoad({
      file: file.raw
    });
    this.widePic = res.data;
    this.formModel.widePic = res.data;
  }

  /**
   * 删除以选中图片
   */
  cancelImg(_item: any, index: number) {
    this.productImgList.splice(index, 1);
    if (this.productImgList.length == 0) {
      this.formModel.pic = "";
    }
  }

  /**
   *  设置权益包图片
   */
  setAlbumPics() {
    const productImgList = this.productImgList;
    if (productImgList.length > 0) {
      this.formModel.pic = productImgList[0].img;
      this.formModel.albumPics = productImgList
        .filter((item) => item.img !== "")
        .map((item) => item.img)
        .join(",");
    }
  }

  /**
   * 还原权益包图片
   */
  getProductImgList(albumPics: string) {
    if (albumPics) {
      const imgList = albumPics.split(",").map((item) => {
        return { img: item };
      });
      return imgList;
    } else {
      return [];
    }
  }

  /**
   * 上传视频
   */
  async uploadProductVideo(file: ElUploadInternalFileDetail) {
    const whiteList = ["video/mp4"];
    const isLt = file.size < 5 * 1024 * 1024;

    if (!whiteList.includes(file.raw.type)) {
      this.$message.error("上传视频只能是 mp4 格式!");
      return;
    }

    if (!isLt) {
      this.$message.error("上传视频大小不超过5M!");
      return;
    }

    const res = await upLoad({
      file: file.raw
    });
    this.formModel.videoUrl = res.data;
  }

  /**
   * 删除视频
   */
  deleteVideoUrl() {
    this.formModel.videoUrl = "";
  }

  /**
   * 获取物流模板
   */
  async getLogis() {
    const res = await getLogistics({});
    res.data.unshift({
      id: "0",
      name: "商家包邮"
    });
    this.logModel = res.data;
  }

  /**
   * 切换配送方式
   */
  getLogModel(e: number) {
    const parentHtml = this.$parent as NewGoodForm;
    parentHtml.isWeight = false;
    this.$emit("changeLogisticsId");
    if (e === 1) {
      this.formModel.freightTemplateId = "0";
    }
  }

  /**
   * 选择物流模板
   */
  selectLogModel(e: string) {
    const parentHtml = this.$parent as NewGoodForm;
    this.formModel.freightTemplateId = e;
    parentHtml.logisticsId = e;
    parentHtml.isWeight = e === "0" ? false : true;
  }

  /**
   * 获取模板信息
   */
  getModelOption(modelOption: FreightTempType) {
    const parentHtml = this.$parent as NewGoodForm;
    parentHtml.isWeight =
      Number(modelOption.valuationModel) === 1 ? false : true;
  }

  handleMeasureUnitButtonClick() {
    this.showRegion = true;
    this.addFlag = true;
  }

  /**
   * 删除计量单位
   */
  deleteRegion(_item: any, index: number) {
    this.addList.splice(index, 1);
  }

  /** 新增计量单位 */
  textAdd() {
    this.addList.push({
      modeName: ""
    });
  }

  cancel() {
    this.showRegion = false;
    this.deleteType = false;
    this.addList = [
      {
        modeName: ""
      }
    ];
  }

  //权益包编码

  async GoodsCode() {
    const { data } = await PackageGoodsCode({});
    // 获取后端传递过来的权益包编码，放到对象的属性中
    this.formModel.goodsCode = data;
  }


  /**
   * 确认操作
   */
  sureDeal() {
    /*const list = this.addList[0];
    if (this.click) {
      return;
    }
    if (!list.id) {
      this.addRegionList();
      return;
    }
    this.click = true;
    editRegionList(list)
      .then(res => {
        this.$message.success("设置成功");
        this.showRegion = false;
        this.getInit();
        console.log(res);
        this.click = false;
      })
      .catch(err => {
        this.$message.error(err || "网络错误");
        this.click = false;
      });*/
  }
}
</script>

<style lang="scss" scoped>
.DistributionCommission {
  margin-left: 5px;

  .commission__top {
    display: flex;
    border: 1px solid #a0b3d6;
    margin-bottom: 2px;

  }

  span {
    display: inline-block;
    text-align: center;
    background: #bbbbbb;
    line-height: 20px;
    color: rgba(33, 31, 31, 1);

  }

  .span1 {
    width: 200px;

  }

  .span2 {
    width: 50px;
  }

  .distribution__commission {

    width: 295px;
    height: 20px;
    line-height: 20px;
    padding: 3px;
    font-size: 12px;
    outline: none;
    text-align: center;
    border: none;
    // overflow-x: hidden;
    // overflow-y: auto;
  }
}

@include b(regionTop) {
  width: 100%;
  height: 50px;
  background-color: #f2f2f2;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 10px 30px;
  color: #666666;
  font-size: 12px;

  @include e(lineWidth) {
    width: 100%;
    display: flex;
  }
}

@include b(regionAdd) {
  margin: 20px 0px;
  display: flex;
  justify-content: space-between;

  @include e(tip) {
    color: #666666;
    font-size: 14px;
  }
}

.dialogTitle {
  display: flex;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
}

.dialogName {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  padding-left: 20px;
  background-color: #e9f3fb;
  font-size: 14px;
  font-weight: bold;
  margin-top: -20px;
}

@include b(dialogList) {
  margin-top: 20px;

  @include e(item) {
    display: flex;
    margin-bottom: 20px;
    justify-content: space-between;
    width: 300px;
  }
}

.miniUnitSpan {
  margin-left: 10px;
}
</style>
