<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:10
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-14 10:15:03
-->
<template>
	<!-- 商品列表 -->
	<div style="margin-top: 20px">
		<m-table :data.sync="goodList" :selection="true" :checked-item.sync="tableCheckedItem" slot="content"
			class="imgView">
			<m-table-column prop="userName" label="权益包名称" :showsSlection="true" width="380">
				<template v-slot="{ row }">
					<div class="goodList">
						<div style="width: 70px; height: 70px">
							<img :src="row.pic" alt />
						</div>
						<div class="goodList__msg" @click="currentGoodClick(row)">
							<GoodNameEdit :good-name="row.name" @change="updateGoodName" />
							<GoodPriceEdit :sku-stocks="row.skuStocks" @change-price="updateGoodPrice" />
							<!--<div class="goodList__msg&#45;&#45;apply">
                供应商:{{ row.providerName }}
              </div>-->
						</div>
					</div>
				</template>
			</m-table-column>
			<!--      <m-table-column prop="userName" label="库存">
        <template v-slot="{ row }">
          <span class="stockWarn center">
            {{ row.stock }}
            <StockWarn :stock-item="row"></StockWarn>
          </span>
        </template>
      </m-table-column> -->
			<!-- <m-table-column prop="userName" label="库存上限">
			  <template v-slot="{ row }">
			    <span class="stockWarn center">
			      {{ row.skuStocks[0].upperLimit }}
			      <StockWarn :stock-item="row"></StockWarn>
			    </span>
			  </template>
			</m-table-column>
			<m-table-column prop="userName" label="库存下限">
			  <template v-slot="{ row }">
			    <span class="stockWarn center">
			      {{ row.skuStocks[0].lowerLimit }}
			      <StockWarn :stock-item="row"></StockWarn>
			    </span>
			  </template>
			</m-table-column> -->
			<m-table-column prop="userName" label="创建时间">
				<template v-slot="{ row }">
					<span>{{ row.createTime }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="userName" label="使用期限">
				<template v-slot="{ row }">
					<span>{{ row.packageStartTime }}至{{ row.packageEndTime }}</span>
				</template>
			</m-table-column>
			<m-table-column prop="userName" label="状态">
				<template v-slot="{ row }">
					<div class="upDown center">
						<span class="upDown__goodUp" :style="{
							color: row.status === 1 ? '#80C269' : '#F3A07E',
							'background-color': row.status === 1 ? '#F5FAF3' : '#FEF7F4'
						}">{{ row.status === 1 ? "已上架" : "已下架" }}</span>
					</div>
				</template>
			</m-table-column>
			<!-- <m-table-column prop="userName" label="转移专区">
				<template v-slot="{ row }">
					<el-dropdown v-if="isSupper || moveButton" trigger="click" @command="transWay($event, row)"
						placement="bottom-start">
						<span style="display: flex; font-size: 12px">
							{{ currentRegion }}
							<i class="el-icon-arrow-down el-icon--right"></i>
						</span>
						<el-dropdown-menu slot="dropdown" :class="regionList.length > 4 ? 'commandClass' : ''">
							<el-dropdown-item v-for="item in regionList" :key="item.command" :disabled="item.disabled"
								:command="item.command">{{ item.modeName }}</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
					<span v-else style="display: flex; font-size: 12px">
						{{ currentRegion }}
					</span>
				</template>
			</m-table-column> -->
			<m-table-column prop="userName" label="操作">
				<template v-slot="{ row }">
					<div class="center">
						<set-drop v-if="isSupper || editButton" setName="编辑" :dropdownList="itemDropList(row)" @setClick="edit(row)"
							@command="getDropdown($event, row)" />
						<set-drop v-else setName="" :dropdownList="itemDropList(row)" @command="getDropdown($event, row)" />
						<el-dialog title="商品码" :visible.sync="dialogVisible" width="350px">
							<div style="display: flex; justify-content: center">
								<img :src="codeImg" alt style="width: 180px; height: 180px" />
							</div>
							<span slot="footer" class="dialog-footer">
								<el-button @click="dialogVisible = false">取 消</el-button>
								<el-button type="primary" @click="dialogVisible = false">确 定</el-button>
							</span>
						</el-dialog>

						<!-- 多规格弹窗 -->
						<el-dialog :visible.sync="vdialogVisible" width="50%">
							<div slot="title" class="diaTitle" style="font-size: 15px">
								会员设置
							</div>
							<el-radio-group v-model="radio" @change="agreeChange()" style="margin: 0px 0px 25px 0px">
								<el-radio :label="0">不使用会员价</el-radio>
								<el-radio :label="1">固定金额</el-radio>
								<el-radio :label="2">百分比</el-radio>
							</el-radio-group>

							<el-tabs v-model="activeName" v-if="fold">
								<el-tab-pane :label="itemTab.memberTypeName" :name="itemTab.memberTypeName"
									v-for="(itemTab, i) in memberLevelGoodsPriceGroupByMemberTypeVos" :key="i">

									<el-table :data="tablevipItemList" :header-cell-style="{ background: '#F2F4F7', color: '#606266' }"
										size="30" :header-row-style="{ height: '100%' }" v-if="fold">
										<el-table-column label="规格值" width="150" v-if="skuDisplay">
											<template slot-scope="scope">
												{{ scope.row.specs }}
											</template>
										</el-table-column>

										<el-table-column label="实售价" width="200">
											<template slot-scope="scope">
												{{ scope.row.price }}
											</template>
										</el-table-column>
										<el-table-column label="指导价" width="200">
											<template slot-scope="scope">
												{{ scope.row.originalPrice }}
											</template>
										</el-table-column>

										<el-table-column
											:label="meberLive.filter(filterItem => filterItem.id == item.memberLevelId)[0].memberLevel"
											:key="index" v-for="(item, index) in itemTab.memberLevelGoodsPriceList" width="200">
											<template slot-scope="scope">
												<el-input size="mini" v-model="scope.row.memberLevelGoodsPriceGroupByMemberTypeVos[i].memberLevelGoodsPriceList[index].memberLevelPrice
													" @input="
														(e) =>
															(scope.row.memberLevelGoodsPriceGroupByMemberTypeVos[i].memberLevelGoodsPriceList[index].memberLevelPrice = integerFn(e))
													" placeholder="请输入"></el-input>
											</template>
										</el-table-column>

									</el-table>

								</el-tab-pane>
							</el-tabs>


							<div slot="footer" class="dialog-footer">
								<el-button @click="vdialogVisible = false">取 消</el-button>
								<el-button type="primary" @click="confirm(row)">确 定</el-button>
							</div>
						</el-dialog>

						<el-dialog :visible.sync="vdialogVisible2" width="50%">
							<div slot="title" class="diaTitle" style="font-size: 15px">
								会员复购价设置
							</div>
							<el-radio-group v-model="radio2" @change="agreeChange2()" style="margin: 0px 0px 25px 0px">
								<el-radio :label="0">不使用会员复购价</el-radio>
								<el-radio :label="1">固定金额</el-radio>
								<el-radio :label="2">百分比</el-radio>
							</el-radio-group>

							<el-tabs v-model="activeName2" v-if="fold2">
								<el-tab-pane :label="itemTab.memberTypeName" :name="itemTab.memberTypeName"
									v-for="(itemTab, i) in memberLevelGoodsAgainPriceGroupByMemberTypeVos" :key="i">


									<el-table :data="tablevipItemList2" :header-cell-style="{ background: '#F2F4F7', color: '#606266' }"
										size="30" :header-row-style="{ height: '100%' }" v-if="fold2">
										<el-table-column label="规格值" width="150" v-if="skuDisplay2">
											<template slot-scope="scope">
												{{ scope.row.specs }}
											</template>
										</el-table-column>

										<el-table-column label="实售价" width="200">
											<template slot-scope="scope">
												{{ scope.row.price }}
											</template>
										</el-table-column>
										<el-table-column label="指导价" width="200">
											<template slot-scope="scope">
												{{ scope.row.originalPrice }}
											</template>
										</el-table-column>
										<!-- <el-table-column
											:label="meberLive.filter(filterItem => filterItem.id == item.memberLevelId)[0].memberLevel"
											:key="index" v-for="(item, index) in itemTab.memberLevelGoodsAgainPriceList" width="200">
											<template slot-scope="scope">
												<el-input size="mini" v-model="item.memberLevelAgainPrice
													" @input="
														(e) =>
															(item.memberLevelAgainPrice = integerFn(e))
													" placeholder="请输入"></el-input>
											</template>
										</el-table-column> -->

										<el-table-column
											:label="meberLive.filter(filterItem => filterItem.id == item.memberLevelId)[0].memberLevel"
											:key="index" v-for="(item, index) in itemTab.memberLevelGoodsAgainPriceList" width="200">
											<template slot-scope="scope">
												<el-input size="mini" v-model="scope.row.memberLevelGoodsAgainPriceGroupByMemberTypeVos[i].memberLevelGoodsAgainPriceList[index].memberLevelAgainPrice
													" @input="
														(e) =>
															(scope.row.memberLevelGoodsAgainPriceGroupByMemberTypeVos[i].memberLevelGoodsAgainPriceList[index].memberLevelAgainPrice = integerFn(e))
													" placeholder="请输入"></el-input>
											</template>
										</el-table-column>

									</el-table>

								</el-tab-pane>
							</el-tabs>


							<div slot="footer" class="dialog-footer">
								<el-button @click="vdialogVisible2 = false">取 消</el-button>
								<el-button type="primary" @click="confirm2(row)">确 定</el-button>
							</div>
						</el-dialog>

					</div>
				</template>
			</m-table-column>
		</m-table>
	</div>
</template>

<script lang="ts">
import { Vue, Component, Watch, Prop } from "vue-property-decorator";
import SetDrop from "@/views/customer/common/SetDrop.vue";
import GoodNameEdit from "@/components/GoodNameEdit.vue"; // 编辑商品名称
import GoodPriceEdit from "@/components/GoodPriceEdit.vue"; // 编辑商品价格
import StockWarn from "./goodsComp/StockWarn.vue"; // 库存警告
import { GoodListState } from "./goodListType";
import { SearchKeyType } from "./searchType";
import {
	getProList,
	GoodUpDown,
	updateGood,
	GoodDel,
	updateGoodsApart,
	getImgCode,
	getAllRegionList,
	getMemberPriceList,
	getMemberLevelPriceAdd,
	getMemberLevelPriceList,
	getMemberLevelList,
	getMemberLevelAdd,
	getMemberLevelAgainAdd,
	getManageAllRegionList
} from "@/api/good/goods";
import { Loading } from "element-ui";
import { ApiSkuType, GoodDetailInfo } from "../goodType";
import { ApiSpecArea } from "../marketType";
import { PickApiSkuType } from "@/components/componentType/goodPriceEditType";
@Component({
	components: {
		GoodNameEdit,
		GoodPriceEdit,
		StockWarn,
		SetDrop
	}
})
export default class GoodsList extends Vue implements GoodListState {
	@Prop({})
	changeId!: string;

	@Watch("changeId")
	getSaleMode() {
		this.searchType = {
			current: 1,
			size: 20,
			saleMode: ""
		};
		this.saleMode = this.changeId;
		this.getProduct();
		this.getApartList();
	}

	@Watch("goodIds")
	onGoodIdsChanged(val: number[]) {
		this.$emit("goodId", val);
	}

	saleMode = "";

	goodList: Array<GoodDetailInfo> = [];

	hasList = false;

	cateFlag = false;

	loading = false;

	checkAll = false;

	isIndeterminate = false;

	goodIds: Array<number> = [];

	tableCheckedItem: Array<GoodDetailInfo> = [];

	tablevipItem: Array<vipcardDetailInfo> = [];

	tablevipItemList: Array<vipcardDetailInfoList> = [];
	tablevipItemList2: Array<vipcardDetailInfoList> = [];

	newTablevipItemList: Array<vipcardDetailInfoList> = [];
	newTablevipItemList2 = [];

	tablevipItemData: Array<vipcardDetailInfoData> = [];

	tablevipItemListAdd: Array<vipcardDetailInfoDataAdd> = [];

	tablevipItemListAdd2 = [];

	currentGood: GoodDetailInfo | null = null;

	menuName = "权益包列表";

	activeName = '';
	activeName2 = '';

	buttonList = [];

	isSupper = 0;

	addButtonCode = "packagesList.add";
	addButton = false;

	deleteButtonCode = "packagesList.delete";
	deleteButton = false;

	editButtonCode = "packagesList.edit";
	editButton = false;

	moveButtonCode = "packagesList.move";
	moveButton = false;

	pickButtonCode = "packagesList.pick";
	pickButton = false;

	banButtonCode = "packagesList.ban";
	banButton = false;

	get itemDropList() {
		return (row: GoodDetailInfo) => {
			return [
				{
					text: "商品码",
					command: "3",
					show: true,
					disabled: false
				},
				{
					text: "删除",
					command: "4",
					show: this.isSupper || this.deleteButton,
					disabled: false
				},
				{
					text: "复制链接",
					command: "5",
					show: true,
					disabled: false
				},
				{
					text: "上架",
					command: "6",
					show: (row.status === 0) && (this.isSupper || this.pickButton),
					disabled: false
				},
				{
					text: "下架",
					command: "7",
					show: (row.status === 1) && (this.isSupper || this.banButton),
					disabled: false
				},
				{
					text: "会员价",
					command: "8",
					show: this.isSupper || this.editButton,
					disabled: false
				},
				{
					text: "复购价",
					command: "9",
					show: this.isSupper || this.editButton,
					disabled: false
				}
			];
		};
	}

	total = 0;

	fold = true;
	fold2 = true;

	skuDisplay = true;
	skuDisplay2 = true;

	meberLive = [];

	searchType = {
		current: 1,
		size: 20,
		saleMode: "",
		productType: 2,
	} as SearchKeyType;

	radio = 0;
	radio2 = 0;


	memberPriceType = "";

	memberAgainPriceType = "";

	dddaata = "";
	dddaata2 = "";

	gridData = [
		{
			realPrice: "达达1号",
			guidePrice: "200"
		}
	];

	form = {
		name: "0",
		region: "0",
		date1: "0"
	};

	// 会员等级复购价格-根据会员类型分组
	memberLevelGoodsAgainPriceGroupByMemberTypeVos: any = [];
	// 会员等级价格-根据会员类型分组
	memberLevelGoodsPriceGroupByMemberTypeVos: any = [];

	dialogVisible = false;

	vdialogVisible = false;
	vdialogVisible2 = false;

	DialogVisible = false;

	codeImg = "";

	regionList: Array<ApiSpecArea> = [];

	currentRegion = "";

	mounted() {
		this.saleMode = this.changeId;

		//  加载搜索缓存
		var cache = JSON.parse(
			localStorage.getItem("cache_goods_search_form") || "{}"
		);
		console.log('cache11111111', cache);
		this.searchType = Object.assign(cache) as SearchKeyType;

		this.getProduct();
		this.getApartList();
		this.getMemberLevelList();
		this.buttonAuth();
	}

	buttonAuth() {
		this.isSupper = this.$STORE.userStore.userInfo.isSupper
		let authMenuButtonVos = this.$STORE.userStore.userInfo.authMenuButtonVos.filter(i => i.menuName == this.menuName)

		let buttonList = [];

		authMenuButtonVos.forEach(element => {
			buttonList.push(element.buttonCode);
		});

		this.buttonList = buttonList

		var addButtonData = buttonList.find(e => e == this.addButtonCode);

		if (addButtonData != null && addButtonData != undefined) {
			this.addButton = true;
		}

		var deleteButtonData = buttonList.find(e => e == this.deleteButtonCode);

		if (deleteButtonData != null && deleteButtonData != undefined) {
			this.deleteButton = true;
		}

		var editButtonData = buttonList.find(e => e == this.editButtonCode);

		if (editButtonData != null && editButtonData != undefined) {
			this.editButton = true;
		}

		var moveButtonData = buttonList.find(e => e == this.moveButtonCode);

		if (moveButtonData != null && moveButtonData != undefined) {
			this.moveButton = true;
		}

		var pickButtonData = buttonList.find(e => e == this.pickButtonCode);

		if (pickButtonData != null && pickButtonData != undefined) {
			this.pickButton = true;
		}

		var banButtonData = buttonList.find(e => e == this.banButtonCode);

		if (banButtonData != null && banButtonData != undefined) {
			this.banButton = true;
		}

	}


	/**
	 * 获取所有专区
	 */
	getApartList() {
		getManageAllRegionList({}).then((res) => {
			res.data.forEach((item) => {
				item.text = `移至${item.modeName}专区`;
				item.show = true;
				item.disabled = false;
				item.command = item.id;
				item.isCustom = false;
				if (item.id === this.saleMode) {
					item.isCustom = true;
					this.currentRegion = item.modeName;
				}
			});

			this.regionList = res.data;
		});
	}

	/**
	 * 获取商品列表
	 */
	async getProduct() {
		const param = this.getProListParams();
		this.loading = true;
		try {
			const res = await getProList(param);
			const goodList = res.data.list;
			this.total = res.data.total;
			this.hasList = res.data.list.length === 0 ? true : false;
			goodList.forEach((item) => {
				item.stock = this.getGoodStock(item.skuStocks);
			});
			this.goodList = goodList;
			this.checkAll = false;
			this.handleCheckAllChange();
		} catch (error) {
			console.log(error);
		}
		this.loading = false;
		this.$emit("getShowProList", this.goodList);
	}

	/**
	 * 只能输入大于0的数（不能以0开头，只能为八位小数，小数点后四位。小数点前后共可以有十二位）
	 * @param {string} value
	 * @returns {string | number} 返回空字符或数字
	 */
	integerFn(value) {
		let reg = /[1-9]|\.$/;
		//点出现的次数
		let spotNumber = 0;
		//点后面的小数
		let spotNum = 0;
		let bool = "float";
		// let reg = /^((?!0)\d{1,8}|100000000)$/;
		let strArray = value.split("");
		let newStrs = "";
		let strs = "";
		for (let i = 0; i < strArray.length; i++) {
			//限制输入的为八位数，小数点后可以为4位数
			strs = strs + strArray[i];
			if (bool == "true" && strs.indexOf(".")[0] > 100000000) {
				//console.log(newStrs,'====rag============111===========',strs)
				return newStrs;
			} else if (strs > 100000000) {
				//console.log(newStrs,'====rag============222222===========',strs)
				return newStrs;
			}
			//当有小数点时，进行判断。用于限制小数点后的数量
			if (reg.test(strArray[i])) {
				if (strArray[i] === ".") {
					spotNumber = spotNumber + 1;
					if (spotNumber > 1) {
						return newStrs;
					}
					bool = "true";
				}
				newStrs += strArray[i];
			} else if (i > 0 && strArray[i] === "0") {
				newStrs += strArray[i];
			}
			//小数点后数量为4位后不给添加
			if (bool == "true") {
				spotNum = spotNum + 1;
				if (spotNum > 4) {
					return newStrs;
				}
			}
		}
		return newStrs;
	}

	/**
	 * 获取商品列表查询参数
	 */
	getProListParams() {
		this.searchType.saleMode = this.saleMode;
		if (!this.searchType.size) {
			this.searchType.size = 20
		}
		this.searchType.productType = 2
		return this.searchType;
	}

	/**
	 * 获取商品库存
	 */
	getGoodStock(skuStocks: ApiSkuType[]): number {
		let stock = 0;
		skuStocks.forEach((v) => {
			stock += Number(v.stock);
		});
		return stock;
	}

	/**
	 * 全选|取消全选 商品
	 */
	handleCheckAllChange() {
		this.goodIds = this.checkAll
			? this.goodList.map((item) => item.id as number)
			: [];
		this.isIndeterminate = false;
	}

	/**
	 * 勾选商品
	 */
	handleGoodIdsChange(goodId: number) {
		const goodIds = this.goodIds;
		const index = goodIds.findIndex((id) => id === goodId);
		index === -1 ? goodIds.push(goodId) : goodIds.splice(index, 1);
		const checkedCount = goodIds.length;
		this.checkAll = checkedCount === this.goodList.length;
		this.isIndeterminate =
			checkedCount > 0 && checkedCount < this.goodList.length;
	}

	/**
	 * 转移专区
	 */
	transWay(val: string | number, row: GoodDetailInfo) {
		const selectItem = this.regionList.filter((item) => item.id === val);
		const modeType = selectItem[0].modeType === "GROUP" ? "GROUP" : 0;
		const id = [];
		id.push(Number(row.id));
		updateGoodsApart(val, modeType, id)
			.then((res) => {
				if (res.code === 200) {
					this.$message.success(`商品已${selectItem[0].text}`);
					this.getProduct();
				}
			})
			.catch((e) => {
				this.$message.error(e || "网络错误");
			});
	}

	/**
	 * 获取下拉框
	 */
	getDropdown(val: string | number, row: GoodDetailInfo) {
		if (Number(val) > 9) {
			this.transWay(val, row);
			return;
		}
		switch (val) {
			case "2":
				const id = [];
				id.push(row.id);
				updateGoodsApart(val, 0, id)
					.then((res) => {
						if (res.code === 200) {
							this.$message.success(`商品移动成功`);
							this.getProduct();
						}
					})
					.catch((e) => {
						this.$message.error(e || "网络错误");
					});
				break;
			case "3":
				this.dealShopCode(Number(row.id));
				break;
			case "4":
				this.delProduct(Number(row.id));
				break;
			case "5":
				if (row.status === 0) {
					this.$message.error("请先上架商品");
					return;
				}
				const path = `subcontract/pages/detail/detail.html` + "?id=" + row.id;
				this.$copyText(path);
				this.$message.success("复制成功");
				break;
			case "6":
			case "7":
				this.changeStatus(row);
				break;
			case "8":
				// this.memberPrice(row.id);
				// this.memberPriceList(row.id,row.memberPriceType)
				this.memberPriceList(row);
				break;
			case "9":
				this.memberAgainPriceList(row);
				break;
		}
	}

	/**
	 * 获取商品码
	 */
	dealShopCode(id: number) {
		const wxaGetwxacode = {
			path: `subcontract/pages/detail/detail?id=${id}`,
			width: 0
		};
		let loadingInstance = Loading.service({ target: ".imgView" });
		getImgCode(wxaGetwxacode)
			.then((res) => {
				this.codeImg = res.data;
				0;
				loadingInstance.close();
				this.dialogVisible = true;
			})
			.catch(() => {
				loadingInstance.close();
			});
	}

	/**
	 * 删除商品
	 */
	async delProduct(ids: string | number | string[]) {
		await this.$confirm("确定要删除选中商品吗？", "提示", {
			confirmButtonText: "确定",
			cancelButtonText: "取消",
			type: "warning"
		});
		try {
			const { code } = await GoodDel(ids, {});
			if (code === 200) {
				this.$message.success("删除成功");
				this.getProduct();
			}
		} catch (error) {
			this.$message.error(error);
		}
	}

	/**
	 *
	 */
	currentGoodClick(val: GoodDetailInfo | null) {
		this.currentGood = val;
	}

	/**
	 * 更新商品名称
	 */
	async updateGoodName(newName: string) {
		const currentGood = this.currentGood as GoodDetailInfo;
		const param = {
			...this.currentGood,
			name: newName
		};
		await this.updateGoodInfo(param);
		currentGood.name = newName;
	}

	/**
	 * 更新商品价格
	 */
	async updateGoodPrice(prices: PickApiSkuType) {
		const currentGood = this.currentGood as GoodDetailInfo;
		const skuStocks = currentGood.skuStocks.map((item) => {
			return {
				...item,
				price: prices[item.id]
			};
		});
		const param = {
			...this.currentGood,
			skuStocks
		};
		await this.updateGoodInfo(param);
		currentGood.skuStocks = skuStocks;
	}

	/**
	 * 更新商品信息
	 */
	async updateGoodInfo(param: Partial<GoodDetailInfo>) {
		try {
			const { code } = await updateGood(param);
			if (code === 200) {
				this.$message.success("修改成功");
				return Promise.resolve(true);
			}
		} catch (error) {
			console.log(error);
		}
		return Promise.reject(false);
	}

	/**
	 * 上下架商品
	 */
	changeStatus(item: GoodDetailInfo) {
		if (item.productShowCategorys.length === 0) {
			this.$message.error(
				`商品${item.status === 0 ? "上架" : "下架"}失败,请重新编辑商品分类信息`
			);
			return;
		}
		const id = [];
		id.push(item.id);
		const productType = 2
		GoodUpDown(item.status === 1 ? 0 : 1, item.saleMode, id, productType).then((res) => {
			if (res.code === 200) {
				this.$message.success(`商品已${item.status === 0 ? "上架" : "下架"}`);
				this.getProduct();
			}
		}).catch((err) => {
			this.$message.error(err);
		});
	}

	//查询会员等级
	getMemberLevelList() {
		getMemberLevelList().then((res) => {
			this.meberLive = res.data;
			console.log("获取会员等级", this.meberLive);
		});
	}
	memberAgainPriceList(row) {
		console.log("memberAgainPriceList", row);
		this.tablevipItemListAdd2 = row;
		this.dddaata2 = row.skuStockMemberPriceVos.length;
		this.radio2 = row.memberAgainPriceType;
		this.memberAgainPriceType = row.memberAgainPriceType;
		if (this.newTablevipItemList2.length == 0) {
			this.newTablevipItemList2 = [...row.skuStockMemberPriceVos];
			console.log(
				"newTablevipItemList2,进来了！！！！",
				this.newTablevipItemList2
			);
		} else {
			//判断这个模态框的商品与之前模态框商品是否一致
			if (this.newTablevipItemList2[0].id == row.skuStockMemberPriceVos[0].id) {
				//判断这个数据在数据库里是否进行修改，如果没有修改则将原来的数据覆盖现在的数据
				if (
					this.newTablevipItemList2[0].memberLevelGoodsAgainPrices[0].updateTime ==
					row.skuStockMemberPriceVos[0].memberLevelGoodsAgainPrices[0].updateTime
				) {
					console.log("================修改时间相等=============");
				} else {
					console.log("==================修改时间不一样=============");
					this.newTablevipItemList2 = [...row.skuStockMemberPriceVos];
				}
			} else {
				this.newTablevipItemList2 = [...row.skuStockMemberPriceVos];
			}
		}
		console.log("this.newTablevipItemList2", this.newTablevipItemList2);
		//给模态框赋值
		this.tablevipItemList2 = JSON.parse(
			JSON.stringify(this.newTablevipItemList2)
		);

		this.memberLevelGoodsAgainPriceGroupByMemberTypeVos = this.tablevipItemList2[0].memberLevelGoodsAgainPriceGroupByMemberTypeVos;
		this.activeName2 = this.memberLevelGoodsAgainPriceGroupByMemberTypeVos[0].memberTypeName;

		console.log("this.newTablevipItemList2,！！！！", this.newTablevipItemList2);
		if (row.memberAgainPriceType == 0) {
			this.fold2 = false;
		} else {
			this.fold2 = true;
		}
		this.vdialogVisible2 = !this.vdialogVisible2;

		if (this.dddaata2 > 1) {
			this.skuDisplay2 = true;
		}
		if (this.dddaata2 == 1) {
			this.skuDisplay2 = false;
		}
	}
	//查询会员价格
	memberPriceList(row: GoodDetailInfo) {
		console.log("this.tablevipItemListAdd = row", row);
		this.tablevipItemListAdd = row;
		//this.tablevipItemList = row.skuStockMemberPriceVos;
		this.dddaata = row.skuStockMemberPriceVos.length;
		this.radio = row.memberPriceType;
		this.memberPriceType = row.memberPriceType;
		//给一个常量赋值，然后用这个常量给模态框赋值。防止修改了但是没有提交，前段还是显示修改的样子
		if (this.newTablevipItemList.length == 0) {
			this.newTablevipItemList = [...row.skuStockMemberPriceVos];
			console.log(
				"newTablevipItemList,进来了！！！！",
				this.newTablevipItemList
			);
		} else {
			//判断这个模态框的商品与之前模态框商品是否一致
			if (this.newTablevipItemList[0].id == row.skuStockMemberPriceVos[0].id) {
				// console.log('werwerwerqweqr===',this.newTablevipItemList[0])
				//判断这个数据在数据库里是否进行修改，如果没有修改则将原来的数据覆盖现在的数据
				if (
					this.newTablevipItemList[0].memberLevelGoodsPrices[0].updateTime ==
					row.skuStockMemberPriceVos[0].memberLevelGoodsPrices[0].updateTime
				) {
					console.log("================修改时间相等=============");
				} else {
					console.log("==================修改时间不一样=============");
					this.newTablevipItemList = [...row.skuStockMemberPriceVos];
				}
			} else {
				this.newTablevipItemList = [...row.skuStockMemberPriceVos];
			}
		}
		// console.log('撒旦范德萨范德萨范德萨发',this.newTablevipItemList)
		// this.tablevipItemList=[...this.newTablevipItemList]
		//给模态框赋值
		this.tablevipItemList = JSON.parse(
			JSON.stringify(this.newTablevipItemList)
		);
		this.memberLevelGoodsPriceGroupByMemberTypeVos = this.tablevipItemList[0].memberLevelGoodsPriceGroupByMemberTypeVos;
		this.activeName = this.memberLevelGoodsPriceGroupByMemberTypeVos[0].memberTypeName;

		// this.tablevipItemList=deepCopy(this.newTablevipItemList)
		console.log("this.newTablevipItemList,！！！！", this.newTablevipItemList);
		// console.log("this.tablevipItemList,！！！！" ,this.tablevipItemList)

		if (row.memberPriceType == 0) {
			this.fold = false;
		} else {
			this.fold = true;
		}
		this.vdialogVisible = !this.vdialogVisible;

		// console.log("this.tablevipItemListAdd====" ,this.tablevipItemListAdd)

		if (this.dddaata > 1) {
			this.skuDisplay = true;
		}
		if (this.dddaata == 1) {
			this.skuDisplay = false;
		}

		// getMemberLevelPriceList(param).then(res => {
		// 	// console.log(res.data[0].memberLevelGoodsPrices)
		// 	// console.log('getMemberLevelPriceList', res.data)
		// 	const returnData =res.data
		// 	console.log('getMemberLevelPriceList',returnData.data)
		// 	if(returnData.code!=200){
		// 		this.$message.error(
		// 		 res.data.data,
		// 		);

		// 	}else{
		// 		this.tablevipItemListAdd = returnData.data
		// 		this.tablevipItemList = returnData.data.skuStockMemberPriceVos;
		// 		// console.log("1111111111222",returnData.data.skuStockMemberPriceVos)
		// 		this.dddaata = returnData.data.skuStockMemberPriceVos.length;
		// 		this.radio = returnData.data.memberPriceType
		// 		console.log(returnData.data.memberPriceType)
		// 	// if(this.dddaata > 1){
		// 		this.vdialogVisible = !this.vdialogVisible;
		// 	// }if(this.dddaata == 1){
		// 	// 	this.DialogVisible = !this.DialogVisible;
		// 	// }
		// 	}
		// });
		// console.log(id)
	}

	/**
	 * 深拷贝的方式
	 */

	// function copyArray(arr){
	//   return JSON.parse(JSON.stringify(arr));
	// }

	// function deepCopy(obj) {
	//   var a = JSON.stringify(obj)
	//   var newobj = JSON.parse(a)
	//   return newobj
	// }

	//会员价格页面按钮切换条件
	agreeChange() {
		let that = this;
		console.log("agreeChange,", that);
		this.memberPriceType = this.radio;
		console.log(this.memberPriceType);
		//判断这个是多规格商品还是单规格商品，如果是单规格商品则没有规格值
		if (this.radio == 0 || this.radio == 12) {
			this.fold = false;
		} else {
			this.fold = true;
		}
		//切换到其他状态，将数据清零
		// for (var j = 0; j < this.tablevipItemList.length; j++) {
		// 	for (
		// 		var i = 0;
		// 		i < this.tablevipItemList[j].memberLevelGoodsPrices.length;
		// 		i++
		// 	) {
		// 		this.tablevipItemList[j].memberLevelGoodsPrices[i].memberLevelPrice =
		// 			"0";
		// 	}
		// }
		for (var j = 0; j < this.tablevipItemList.length; j++) {
			for (
				var i = 0;
				i < this.tablevipItemList[j].memberLevelGoodsPriceGroupByMemberTypeVos.length;
				i++
			) {
				for (
					var k = 0;
					k < this.tablevipItemList[j].memberLevelGoodsPriceGroupByMemberTypeVos[i].memberLevelGoodsPriceList.length;
					k++
				) {
					this.tablevipItemList[j].memberLevelGoodsPriceGroupByMemberTypeVos[i].memberLevelGoodsPriceList[k].memberLevelPrice = '0';
				}
			}
		}
		// console.log("this.newTablevipItemList,11111" ,this.newTablevipItemList)
		// console.log("this.tablevipItemList,22222" ,this.tablevipItemList)
	}

	agreeChange2() {
		let that = this;
		console.log("agreeChange2,", that);
		this.memberAgainPriceType = this.radio2;
		console.log(this.memberAgainPriceType);
		//判断这个是多规格商品还是单规格商品，如果是单规格商品则没有规格值
		if (this.radio2 == 0 || this.radio2 == 12) {
			this.fold2 = false;
		} else {
			this.fold2 = true;
		}

		//切换到其他状态，将数据清零
		for (var j = 0; j < this.tablevipItemList2.length; j++) {
			for (
				var i = 0;
				i < this.tablevipItemList2[j].memberLevelGoodsAgainPriceGroupByMemberTypeVos.length;
				i++
			) {
				for (
					var k = 0;
					k < this.tablevipItemList2[j].memberLevelGoodsAgainPriceGroupByMemberTypeVos[i].memberLevelGoodsAgainPriceList.length;
					k++
				) {
					this.tablevipItemList2[j].memberLevelGoodsAgainPriceGroupByMemberTypeVos[i].memberLevelGoodsAgainPriceList[k].memberLevelAgainPrice = '0';
				}
			}
		}
	}
	// //会员价格对话框的确定事件
	// 	getMemberPriceList(ids: string | number | string[]){
	// 			const goodsId = ids;
	// 			console.log(ids)
	// 		getMemberPriceList(goodsId).then(res => {
	// 			console.log(res)
	// 			// this.tablevipItem = res.data.list;
	// 		});
	// 	}

	priceIncrease(val) { }

	confirm(row: GoodDetailInfo) {
		// console.log(this.memberPriceType)
		// console.log("this.tablevipItemListAdd" ,this.tablevipItemListAdd)
		const param = this.tablevipItemListAdd;
		// console.log("this.tablevipItemListAdd111" ,param)
		// console.log("this.tablevipItemListAdd22222" ,this.tablevipItemList)
		param.memberPriceType = this.radio;
		param.skuStockMemberPriceVos = this.tablevipItemList;
		//判断价格是否为空或者为零
		// if (this.radio != 0) {
		// 	for (var j = 0; j < param.skuStockMemberPriceVos.length; j++) {
		// 		for (
		// 			var i = 0;
		// 			i < param.skuStockMemberPriceVos[j].memberLevelGoodsPrices.length;
		// 			i++
		// 		) {
		// 			if (
		// 				this.tablevipItemList[j].memberLevelGoodsPrices[i]
		// 					.memberLevelPrice == 0 ||
		// 				this.tablevipItemList[j].memberLevelGoodsPrices[i].memberLevelPrice
		// 					.length == 0
		// 			) {
		// 				this.$message.error("价格不能为空或者为0！");
		// 				return;
		// 			}
		// 		}
		// 	}
		// }
		if (this.radio != 0) {
			for (var j = 0; j < param.skuStockMemberPriceVos.length; j++) {
				for (
					var i = 0;
					i < param.skuStockMemberPriceVos[j].memberLevelGoodsPriceGroupByMemberTypeVos.length;
					i++
				) {
					for (
						var k = 0;
						k < param.skuStockMemberPriceVos[j].memberLevelGoodsPriceGroupByMemberTypeVos[i].memberLevelGoodsPriceList.length;
						k++
					) {
						if (
							!this.tablevipItemList[j].memberLevelGoodsPriceGroupByMemberTypeVos[i].memberLevelGoodsPriceList[k].memberLevelPrice ||
							this.tablevipItemList[j].memberLevelGoodsPriceGroupByMemberTypeVos[i].memberLevelGoodsPriceList[k].memberLevelPrice == 0
						) {
							this.$message.error("价格不能为空或者为0！");
							return;
						}

					}
				}
			}
		}
		getMemberLevelAdd(param).then((res) => {
			this.$message({
				message: "提交成功",
				type: "success"
			});
			this.vdialogVisible = false;
			this.getProduct();
		});
		// console.log("12323",this.tablevipItemList)
		// this.$router.go(0)

		// if(this.radio==1 || this.radio==2){
		// 	console.log(this.tablevipItem)
		// if(this.form.name > this.gridData[0].realPrice||this.form.region > this.gridData[0].realPrice||this.form.date1 > this.gridData[0].realPrice){
		// 	this.$message({
		// 	showClose: true,
		// 	message: '会员价格不能高于实售价',
		// 	type: 'error'
		// 	});
		// 	return;
		// }if(this.form.name == '0' || this.form.region == '0' || this.form.date1 == '0'){
		// 	this.$message({
		// 	showClose: true,
		// 	message: '会员优惠不能为0',
		// 	type: 'error'
		// 	});
		// 	return;
		// }if(this.form.name == this.form.region || this.form.region == '0' == this.form.date1 == '0' || this.form.name == '0'== this.form.date1 == '0'){
		// 	this.$message({
		// 	showClose: true,
		// 	message: '会员优惠应为递增',
		// 	type: 'error'
		// 	});
		// 	return;
		// }
		// if(this.radio==1){
		// // getMemberLevelPriceAdd(){
		// // 	const param= {

		// // 	};
		// // }
		// }if(this.radio==2){

		// }
		// }
	}
	confirm2(row) {

		const param = this.tablevipItemListAdd2;
		param.memberAgainPriceType = this.radio2;
		param.skuStockMemberPriceVos = this.tablevipItemList2;
		//判断价格是否为空或者为零

		if (this.radio2 != 0) {
			for (var j = 0; j < param.skuStockMemberPriceVos.length; j++) {
				for (
					var i = 0;
					i < param.skuStockMemberPriceVos[j].memberLevelGoodsAgainPriceGroupByMemberTypeVos.length;
					i++
				) {
					for (
						var k = 0;
						k < param.skuStockMemberPriceVos[j].memberLevelGoodsAgainPriceGroupByMemberTypeVos[i].memberLevelGoodsAgainPriceList.length;
						k++
					) {
						if (
							!this.tablevipItemList2[j].memberLevelGoodsAgainPriceGroupByMemberTypeVos[i].memberLevelGoodsAgainPriceList[k].memberLevelAgainPrice ||
							this.tablevipItemList2[j].memberLevelGoodsAgainPriceGroupByMemberTypeVos[i].memberLevelGoodsAgainPriceList[k].memberLevelAgainPrice == 0
						) {
							this.$message.error("价格不能为空或者为0！");
							return;
						}

					}
				}
			}
		}
		console.log("param", param);
		getMemberLevelAgainAdd(param).then((res) => {
			this.$message({
				message: "提交成功",
				type: "success"
			});
			this.vdialogVisible2 = false;
			this.getProduct();
		});
	}

	/**
	 * 编辑商品
	 */
	edit(item: { id: string; saleMode: string }) {
		this.$router.push({
			name: "AddPackage",
			query: {
				id: item.id,
				saleMode: item.saleMode
			},
			params: {
				id: item.id,
				saleMode: item.saleMode
			}
		});
	}
}
</script>

<style lang="scss" scoped>
.page--top {
	margin-top: 20px;
}

.user__list {
	border-collapse: collapse;

	thead tr td {
		// border-bottom: 1px solid #eee;
		// border-top: 1px solid #eee;
		padding: 10px 5px;
		background-color: #f6f8fa;

		text-align: center;

		&:first-child {
			width: 1%;
		}

		&:nth-child(2) {
			width: 50px;
			text-align: left !important;
		}

		&:last-child {
			width: 90px;
		}
	}

	tbody tr:nth-child(2) td {
		background-color: #ecf6ff;
		height: 40px;
		padding: 5px 5px;
		// border-right: 1px solid #ecf6ff;
		// border-left: 1px solid #ecf6ff;
		font-size: 12px !important;

		.checkItem {
			margin-left: 15px;
		}
	}

	tbody tr:nth-child(3) td {
		// border-bottom: 1px solid #ecf6ff;
		padding: 15px 9px;
		text-align: center;
		height: 75px;
		border-right: 1px solid #ecf6ff;

		&:first-child {
			// border-left: 1px solid #ecf6ff;
		}

		&:nth-child(2) {
			height: 100px;
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			justify-content: center;
		}
	}

	&__good {
		width: 280px;
		display: flex;
		text-align: justify;
		padding-right: 20px;

		img {
			width: 70px;
			height: 70px;
		}

		&--msg {
			margin-left: 10px;
			display: flex;
			flex-wrap: wrap;
			align-content: space-between;

			&--price {
				color: #ff7417;
				margin-top: 10px;
			}
		}
	}

	.stockWarn {
		@include flex(center, center);
	}

	.upDown {
		display: flex;
		align-items: center;
		justify-content: center;

		&--goodUp {
			display: flex;
			width: 50px;
			height: 30px;
			justify-content: center;
			align-items: center;
			border-radius: 50px;
			color: white;
		}

		&--goodDown {
			margin-left: 10px;
			color: #2d8cf0;
			cursor: pointer;
		}
	}
}

.mouseEnter {
	// background-color: red;
	border: 1px solid #ecf6ff;
}

.mouseEnter:hover {
	// background-color: green;
	border: 1px solid #d7e0e8;
}

.pop--button {
	display: flex;
	justify-content: flex-end;
	margin-right: 10px;
}

// .goodList {
// 	// width: 280px;
// 	display: flex;
// 	text-align: justify;
// 	padding-right: 20px;

// 	img {
// 		width: 70px;
// 		height: 70px;
// 	}

// 	&__msg {
// 		margin-left: 10px;
// 		display: flex;
// 		// flex-wrap: wrap;
// 		align-content: space-between;
// 		padding: 8px 0px;

// 		&--apply {
// 			width: 120px;
// 			overflow: hidden;
// 			white-space: nowrap;
// 			text-overflow: ellipsis;
// 		}
// 	}
// }

.goodList {
	// width: 280px;
	display: flex;
	text-align: justify;
	padding-right: 20px;

	img {
		width: 70px;
		height: 70px;
	}

	&__msg {
		margin-left: 10px;
		display: flex;
		// flex-wrap: wrap;
		justify-content: space-between;

		flex-direction: column;
		padding: 8px 0px;

		&--apply {
			width: 120px;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}
}

.upDown {
	display: flex;
	align-items: center;
	justify-content: center;

	&__goodUp {
		display: flex;
		width: 50px;
		height: 20px;
		justify-content: center;
		align-items: center;
		border-radius: 4px;
		color: white;
		margin-right: 10px;
	}

	&__goodDown {
		margin-left: 10px;
		color: #2d8cf0;
		cursor: pointer;
	}
}

.commandClass {
	height: 150px;
	overflow: overlay;
}

.commandClass::-webkit-scrollbar {
	width: 4px;
	height: 4px;
}

.commandClass::-webkit-scrollbar-thumb {
	border-radius: 10px;
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: rgba(0, 0, 0, 0);
}

.commandClass::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 0;
	background: rgba(0, 0, 0, 0);
}

.center {
	display: flex;
	justify-content: center;
}

.digTitle {
	font-size: 17px;
	font-weight: bold;
}
</style>
