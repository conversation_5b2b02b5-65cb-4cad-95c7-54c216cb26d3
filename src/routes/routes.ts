/*
 * @description: 抽离开源版本
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-20 17:29:09
 * @LastEditors: vikingShip
 * @LastEditTime: 2021-09-30 13:18:19
 *  2025.5.30有改动的页面
 */
import MContent from "@/components/MContent.vue";
import MMain from "@/components/MMain.vue";
import store from '@/store/index';

console.log("vuex", store.state.userStore);

const routes = [
  {
    path: "/homePage",
    // 临时比对本地版本用
    version: +new Date(),
    component: MMain,
    code: "homePage",
    meta: {
      icon: "iconjiankonggaikuang_huaban",
      title: "首页",
      islink: 1,
    },
    children: [
      {
        path: "",
        name: "homePage",
        meta: {
          title: "首页",
          isShow: 1,
        },
        show: true,
        component: () => import("@/views/homePage/Index.vue"),
      },
    ],
  },
  {
    path: "/overview",
    // 临时比对本地版本用
    version: +new Date(),
    component: MMain,
    code: "overview",
    meta: {
      icon: "iconjiankonggaikuang_huaban",
      title: "经营概况",
      islink: 1,
    },
    children: [
      {
        path: "",
        name: "OverView",
        meta: {
          title: "经营概况",
          isShow: 1,
        },
        show: true,
        component: () => import("@/views/manage/Index.vue"),
      },
    ],
  },
  {
    path: "",
    component: MMain,
    code: "goods",
    meta: {
      icon: "iconshangpinguanli",
      title: "商品管理",
    },
    children: [
      {
        path: "goods",
        component: MContent,
        code: "goodsList",
        meta: {
          title: "商品列表",
        },
        show: true,
        children: [
          {
            path: "",
            name: "Goods",
            component: () =>
              import(
                "@/views/goods/marketModel/Goods.vue"
              ),
          },
          {
            path: "addGood",
            name: "AddGood",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/marketModel/AddGood.vue"
              ),
            meta: {
              title: "发布商品",
              noPadding: true,
            },
            show: true,
          },
        ],
      },
      {
        path: "goodRegion",
        code: "goodRegion",
        // name: "goodRegion",
        component: MContent,
        meta: {
          title: "设置专区",
        },
        show: true,
        children: [
          {
            path: "",
            name: "goodRegion",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/goodManage/GoodRegion.vue"
              ),
          },
          {
            path: "class",
            name: "class",
            show: true,
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/goodManage/GoodClass.vue"
              ),
            meta: {
              title: "商品分类",
            },
          },
        ],
      },
      {
        path: "attributeTemple",
        name: "Attribute",
        code: "attributeTemple",
        show: true,
        component: () =>
          import(
            /* webpackChunkName: "goods" */ "@/views/goods/goodManage/AttributeTemple.vue"
          ),
        meta: {
          title: "属性模板",
        },
      },
      {
        path: "supplier",
        name: "sup",
        code: "supplier",
        show: true,
        component: () =>
          import(
            /* webpackChunkName: "goods" */ "@/views/goods/goodManage/SupplierManage.vue"
          ),
        meta: {
          title: "供货商",
        },
      },
      {
        path: "measureUnit",
        // name: "measureUnit",
        component: MContent,
        code: "measureUnit",
        meta: {
          title: "基本单位",
        },
        show: true,
        children: [
          {
            path: "",
            name: "measureUnit",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/goodManage/MeasureUnit.vue"
              ),
          },
          {
            path: "class",
            name: "class",
            show: true,
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/goodManage/GoodClass.vue"
              ),
            meta: {
              title: "商品分类",
            },
          },
        ],
      },
      {
        path: "csvImport",
        name: "csvImport",
        code: "csvImport",
        show: true,
        component: MContent,
        meta: {
          title: "素材导入",
        },
        children: [
          {
            path: "",
            name: "csvList",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/csvImport/CsvIndex.vue"
              ),
          },
          {
            path: "editGood",
            name: "editGood",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/csvImport/EditGood.vue"
              ),
            meta: {
              title: "编辑商品",
              noPadding: true,
            },
            show: true,
          },
        ],
      },
    ],
  },
  {
    path: "",
    component: MMain,
    code: "packages",
    meta: {
      icon: "iconshangpinguanli",
      title: "权益包管理",
    },
    children: [
      {
        path: "packages",
        component: MContent,
        code: "packagesList",
        meta: {
          title: "权益包列表",
        },
        show: true,
        children: [
          {
            path: "",
            name: "Packages",
            component: () =>
              import(
                "@/views/packages/marketModel/Goods.vue"
              ),
          },
          {
            path: "addPackage",
            name: "AddPackage",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/packages/marketModel/AddGood.vue"
              ),
            meta: {
              title: "发布权益包",
              noPadding: true,
            },
            show: true,
          },
        ],
      },
      {
        path: "packageDetailList",
        name: "packageDetailList",
        code: "packageDetailList",
        component: () =>
          import(
            "@/views/packages/packageDetailList/packageDetailList.vue"
          ),
        meta: {
          title: "权益包明细",
        },
        show: true,
      },
      {
        path: "accountOrderList",
        name: "accountOrderList",
        code: "accountOrderList",
        component: () =>
          import(
            "@/views/packages/accountOrderList/accountOrderList.vue"
          ),
        meta: {
          title: "销售记录",
        },
        show: true,
      },
      {
        path: "accountGoodsList",
        name: "accountGoodsList",
        code: "accountGoodsList",
        component: () =>
          import(
            "@/views/packages/accountGoodsList/accountGoodsList.vue"
          ),
        meta: {
          title: "销售明细",
        },
        show: true,
      },
      {
        path: "packageGoodsWriteOffList",
        name: "packageGoodsWriteOffList",
        code: "packageGoodsWriteOffList",
        component: () =>
          import(
            "@/views/packages/writeOffList/writeOffList.vue"
          ),
        meta: {
          title: "核销单列表",
        },
        show: true,
      },
      {
        path: "packageGoodsWriteOffDetailList",
        name: "packageGoodsWriteOffDetailList",
        code: "packageGoodsWriteOffDetailList",
        component: () =>
          import(
            "@/views/packages/writeOffDetailList/writeOffDetailList.vue"
          ),
        meta: {
          title: "核销单明细",
        },
        show: true,
      },
      //订单汇总
      {
        path: 'orderCollect',//订单汇总
        name: "orderCollect",
        code: "orderCollect",
        component: () =>
          import(
            "@/views/packages/orderCollect/orderCollect.vue"
          ),
        children: [
          {
            path: "packageStoreCollect/:id",//门店权益包汇总
            name: "packageStoreCollect",

            component: () =>
              import(
                "@/views/packages/orderCollect/components/packageStoreCollect.vue"
              ),
            meta: {
              title: "门店权益包汇总",
            },
            show: true,
          }, {
            path: "packageEmployeeCollect/:empId",//职员权益包汇总
            name: "packageEmployeeCollect",

            component: () =>
              import(
                "@/views/packages/orderCollect/components/packageEmployeeCollect.vue"
              ),
            meta: {
              title: "职员权益包汇总",
            },
            show: true,
          },

        ],
        meta: {
          title: "订单汇总"
        },
        show: true,
      },
      //权益包汇总
      {
        path: 'packageCollect',//权益包汇总
        name: "packageCollect",
        code: "packageCollect",
        component: () =>
          import(
            "@/views/packages/packageCollect/packageCollect.vue"
          ),
        meta: {
          title: "权益包汇总"
        },
        show: true,
      }
    ]
  },
  {
    path: "/order",
    component: MMain,
    code: "order",
    meta: {
      title: "订单管理",
      icon: "icondingdan",
    },
    children: [
      {
        path: "delivery",
        code: "delivery",
        name: "delivery",
        meta: {
          title: "快递订单",
        },
        show: true,
        component: MContent,
        children: [
          {
            meta: {
              title: "",
            },
            path: "",
            component: () =>
              import(
                /* webpackChunkName: "order" */ "@/views/order/DeliveryOrder.vue"
              ),
          },
          {
            path: "send",
            meta: {
              title: "批量发货",
              noPadding: true,
            },
            component: () =>
              import(
                /* webpackChunkName: "order" */ "@/views/order/DeliverySend.vue"
              ),
          },
        ],
      },
      {
        path: "dropShipping",
        code: "dropShipping",
        name: "dropShipping",
        meta: {
          title: "代发货订单",
        },
        show: true,
        component: MContent,
        children: [
          {
            meta: {
              title: "",
            },
            path: "",
            component: () =>
              import(
                /* webpackChunkName: "order" */ "@/views/order/DropShipping.vue"
              ),
          },
          {
            path: "sendSendDropShip",
            meta: {
              title: "批量发货",
              noPadding: true,
            },
            component: () =>
              import(
                /* webpackChunkName: "order" */ "@/views/order/DeliverySendDropShip.vue"
              ),
          },
        ],
      },
      {
        path: "orderDetail",
        code: "orderDetail",
        name: "orderDetail",
        component: () =>
          import(`@/views/order/orderDetails.vue`),
        meta: {
          title: "订单明细",
        },
        show: true,

      },
      {
        path: "evaluation",
        code: "evaluation",
        component: () =>
          import(
            /* webpackChunkName: "order" */ "@/views/order/Evaluation.vue"
          ),
        meta: {
          title: "评价管理",
        },
        show: true,
      },
      {
        path: "afterSale",
        code: "afterSale",
        component: () =>
          import(
            /* webpackChunkName: "order" */ "@/views/order/AfterSaleOrder.vue"
          ),
        meta: {
          title: "售后工单",
        },
        show: true,
      },
    ],
  },

  {
    path: "/warehouse",
    code: "warehouse",
    component: MMain,
    meta: {
      title: "仓库",
      icon: "icondingdan",
    },
    children: [
      {
        path: "warehouse",
        code: "warehouseList",
        component: MContent,
        children: [
          {
            path: "",
            name: "commodityStock",
            component: () =>
              import(
	              /* webpackChunkName: "goods" */ "@/views/warehouse/Index.vue"
              ),
          },
        ],
        meta: {
          title: "仓库管理",
        },
        show: true,
      },

      {
        path: "commodityStock",
        name: "commodityStock",
        code: "commodityStock",
        component: () =>
          import(
	          /* webpackChunkName: "blacklist" */ "@/views/warehouse/commodityStock/stock.vue"
          ),
        meta: {
          title: "商品库存",
        },
        show: true,
      },
      {
        path: "Inventory",
        code: "Inventory",
        component: MContent,
        meta: {
          title: "商品入库",
        },
        show: true,
        children: [
          {
            path: "",
            name: "inventory",
            component: () =>
              import(
			            /* webpackChunkName: "goods" */ "@/views/warehouse/Inventory/details.vue"
              ),
          },
          {
            path: "addReceipt",
            name: "AddReceipt",
            component: () =>
              import(
			            /* webpackChunkName: "goods" */ "@/views/warehouse/Inventory/AddReceipt.vue"
              ),
            meta: {
              title: "新增入库",
            },
          },
          {
            path: "detailed",
            name: "Detailed",
            component: () =>
              import(
						      /* webpackChunkName: "goods" */ "@/views/warehouse/Inventory/detailed.vue"
              ),
            meta: {
              title: "入库明细",
            },
          },
        ],
      },
      {
        path: "warehouseDetails",
        name: "warehouseDetails",
        code: "warehouseDetails",
        meta: {
          title: "入库明细",
        },
        show: true,
        component: () =>
          import(
	          /* webpackChunkName: "blacklist" */ "@/views/warehouse/Inventory/WarehouseDetails.vue"
          ),
      },
      {
        path: "waitShipmentInventory",
        name: "waitShipmentInventory",
        code: "waitShipmentInventory",
        meta: {
          title: "待发货库存",
        },
        show: true,
        component: () =>
          import(
	          /* webpackChunkName: "blacklist" */ "@/views/warehouse/waitShipmentInventory/Index.vue"
          ),
      },
      // {
      //   path: "Inventory",
      //   name: "Inventory",
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "blacklist" */ "@/views/warehouse/Inventory/details.vue"
      //     ),
      //   meta: {
      //     title: "商品入库",
      //   },
      //   show: true,
      // 	children: [
      // 		{
      // 			path: "AddReceipt",
      // 			name: "AddReceipt",
      // 			component: () =>
      // 			  import(
      // 			    /* webpackChunkName: "goods" */ "@/views/warehouse/Inventory/AddReceipt.vue"
      // 			  ),
      // 		},
      // 	],
      // },
    ],
  },


  {
    path: "/customer",
    code: "customer",
    component: MMain,
    meta: {
      title: "客户管理",
      icon: "iconkehu",
    },
    children: [
      {
        path: "list",
        code: "customerList",
        component: MContent,
        children: [
          {
            path: "",
            name: "list",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/customer/list/Index.vue"
              ),
          }, {
            path: "subordinate",
            name: "Subordinate",
            component: () =>
              import(
                "@/views/customer/list/subordinate.vue"
              ),
            meta: {
              title: "客户下级",
            },
          }
        ],
        meta: {
          title: "客户列表",
        },
        show: true,
      },

      {
        path: "blacklist",
        code: "blacklist",
        name: "blacklist",
        component: () =>
          import(
            /* webpackChunkName: "blacklist" */ "@/views/customer/blacklist/Index.vue"
          ),
        meta: {
          title: "黑名单",
        },
        show: true,
      }, {
        path: "miniAccountDetail",
        code: "miniAccountDetail",
        name: "miniAccountDetail",
        component: MContent,
        children: [
          {
            meta: {
              title: "",
            },
            path: "",
            name: "miniAccountDetail",
            component: () =>
              import(
                "@/views/customer/detailList/detailList.vue"
              ),
          }, {
            path: "BatchUpdateIntegral",
            name: "BatchUpdateIntegral",
            component: () =>
              import(
                "@/views/customer/detailList/componnents/BatchUpdateIntegral.vue"
              ),
            meta: {
              title: "批量修改积分",
            },
          },
        ],
        meta: {
          title: "客户明细",
        },
        show: true,
      }, {
        path: "integralRanking",
        code: "integralRanking",
        name: "integralRanking",
        component: () =>
          import("@/views/customer/integralRanking/Index.vue"),
        meta: {
          title: "积分排行",
        },
        show: true,
      },
      {
        path: "memberTypeReport",
        code: "memberTypeReport",
        component: MContent,
        meta: {
          title: "会员信息汇总",
        },
        show: true,
        children: [
          {
            path: "",
            name: "memberTypeReport",
            component: () =>
              import(
                "@/views/customer/memberTypeReport/index.vue"
              ),
          },
          {
            path: "memberLevelReport",
            name: "memberLevelReport",
            component: () =>
              import(
                "@/views/customer/memberLevelReport/index.vue"
              ),
            meta: {
              title: "会员等级汇总",
            },
            show: false,
          },
        ]
      },

      {
        path: "salesReport",
        name: "salesReport",
        code: "salesReport",
        component: () =>
          import(
            "@/views/customer/salesReport/index.vue"
          ),
        meta: {
          title: "销售汇总表",
        },
        show: true,
      },
    ],
  },
  {
    path: '/integral',
    code: 'integral',
    component: MMain,
    meta: {
      title: '积分管理',
      icon: "iconPC-yibeizhu",
      isShow: 0
    },
    children: [
      {
        path: "integral",
        component: MContent,
        code: "integralGoods",
        meta: {
          title: "积分商品",
        },
        show: true,
        children: [
          {
            path: "",
            name: "integral",
            component: () =>
              import("@/views/integral/pointsGoods/Index.vue"),
          },
          {
            path: "addIntegralGoods",
            name: "AddIntegralGoods",
            component: () =>
              import("@/views/integral/pointsGoods/addIntegralGoods.vue"),
            meta: {
              title: "发布积分方案",
              noPadding: true,
            },
            show: true,
          }, {
            path: "pointsGoodsDetailsList",
            name: "PointsGoodsDetailsList",
            component: () =>
              import("@/views/integral/pointsGoods/PointsDetailsList.vue"),
            meta: {
              title: "积分商品明细",
              noPadding: true,
            },
            show: true,
          }, {
            path: "addPointsGoods",
            name: "AddPointsGoods",
            component: () =>
              import("@/views/integral/pointsGoods/addPointsGoods.vue"),
            meta: {
              title: "发布积分商品",
              noPadding: true,
            },
            show: true,
          },
        ],
      }, {
        path: "",
        code: "integralRules",
        component: MContent,
        meta: {
          title: "积分规则",
        },
        show: true,
        children: [
          {
            path: "",
            name: "ruleSettings",
            component: () =>
              import(
                "@/views/integral/ruleSettings/ruleSettings.vue"
              ),
          },
        ],
      },
      {
        path: "integralDet",
        code: "integralDet",
        name: "integralDet",
        component: () =>
          import("@/views/integral/integralDet/Index.vue"),
        meta: {
          title: "积分明细",
        },
        show: true,
      }
    ]

  },
  {
    path: '/reward',
    code: 'reward',
    component: MMain,
    meta: {
      title: '奖励管理',
      icon: "iconqiandai",
      isShow: 0
    },
    children: [
      {
        path: "rewardScheme",
        component: MContent,
        code: "rewardScheme",
        meta: {
          title: "奖励方案",
        },
        show: true,
        children: [
          {
            path: "",
            name: "rewardScheme",
            component: () =>
              import("@/views/reward/rewardScheme/Index.vue"),
          }, {
            path: "addRewardScheme",
            name: "addRewardScheme",
            component: () =>
              import("@/views/reward/rewardScheme/addRewardScheme.vue"),
            meta: {
              title: "发布奖励方案",
              noPadding: true,
            },
            show: true,
          },
        ],
      }, {
        path: "rewardSchemeDet",
        code: "rewardSchemeDet",
        name: "rewardSchemeDet",
        component: () =>
          import("@/views/reward/rewardSchemeDet/Index.vue"),
        meta: {
          title: "奖励明细",
        },
        show: true,
      }, {
        path: "rewardRoyalty",
        code: "rewardRoyalty",
        name: "rewardRoyalty",
        component: () =>
          import("@/views/reward/rewardRoyalty/Index.vue"),
        meta: {
          title: "提成汇总",
        },
        show: true,
      }, {
        path: "rewardRoyaltyDet",
        code: "rewardRoyaltyDet",
        name: "rewardRoyaltyDet",
        component: () =>
          import("@/views/reward/rewardRoyatlyDet/Index.vue"),
        meta: {
          title: "提成明细",
        },
        show: true,
      },

    ]
  },
  {
    path: '/commission',
    code: 'commission',
    component: MMain,
    meta: {
      title: '佣金管理',
      icon: "iconbigeguanlitai-jinrichengjiaoe",
      isShow: 0
    },
    children: [
      {
        path: "list",
        code: 'commissionList',
        component: MContent,
        children: [
          {
            path: "",
            name: "withdrawalList",
            component: () =>
              import("@/views/commission/list/withdrawalList.vue"),
          },
        ],
        meta: {
          title: "提现列表",
        },
        show: true,
      },
      {
        path: "rulesSetup",
        component: MContent,
        code: 'commissionSetting',
        children: [
          {
            path: "",
            name: "RulesSetup",
            component: () =>
              import("@/views/commission/rulesSetup/rulesSetup.vue"),
          },
        ],
        meta: {
          title: "佣金设置",
        },
        show: true,
      },
      {
        path: "commissionDet",
        code: "commissionDet",
        name: "commissionDet",
        component: () =>
          import("@/views/reward/commissionDet/Index.vue"),
        meta: {
          title: "佣金明细",
        },
        show: true,
      },
      {
        path: "goldenBeanChanges",
        code: "goldenBeanChanges",
        name: "goldenBeanChanges",
        component: () =>
          import("@/views/commission/goldenBeanChanges/goldenBeanChanges.vue"),
        meta: {
          title: "金豆列表",
        },
        show: true,
      },
      {
        path: "goldenBeanDetails",
        code: "goldenBeanDetails",
        name: "goldenBeanDetails",
        component: () =>
          import("@/views/commission/goldenBeanDetails/Index.vue"),
        meta: {
          title: "金豆明细",
        },
        show: true,
      },
    ]

  },
  {
    path: "/certificate",
    code: "certificate",
    component: MMain,
    meta: {
      title: "特惠管理",
      icon: "iconweipingjiaxingxing",
      // icon: "icongouwuche",
    },
    children: [
      {
        path: "certificateList",
        code: "certificateList",
        component: MContent,
        children: [
          {
            path: "",
            name: "certificateList",
            component: () =>
              import(
                "@/views/certificate/certificateList/certificateList.vue"
              ),
          }, {
            path: "addCertificate",
            name: "AddCertificate",
            component: () =>
              import(
                "@/views/certificate/certificateList/addCertificate.vue"
              ),
            meta: {
              title: "发布通惠证",
            },
            show: true,
          },
        ],
        meta: {
          title: "通惠证",
        },
        show: true,
      },
      {
        path: "writeOffList",
        name: "WriteOffList",
        code: "WriteOffList",
        component: () =>
          import(
            "@/views/certificate/writeOffList/writeOffList.vue"
          ),
        meta: {
          title: "核销列表",
        },
        show: true,
      },
      {
        path: "couponList",
        code: "couponList",
        component: MContent,
        children: [
          {
            path: "",
            name: "couponList",
            component: () =>
              import(
                "@/views/certificate/couponList/couponList.vue"
              ),
          }, {
            path: "addCoupon",
            name: "addCoupon",
            component: () =>
              import(
                "@/views/certificate/couponList/addCoupon.vue"
              ),
            meta: {
              title: "发布优惠券",
            },
            show: true,
          },
        ],
        meta: {
          title: "优惠券",
        },
        show: true,
      },
      {
        path: "accountCouponList",
        name: "accountCouponList",
        code: "accountCouponList",
        component: () =>
          import(
            "@/views/certificate/accountCouponList/accountCouponList.vue"
          ),
        meta: {
          title: "优惠券明细",
        },
        show: true,
      },
      {
        path: "couponWriteOffList",
        name: "couponWriteOffList",
        code: "couponWriteOffList",
        component: () =>
          import(
            "@/views/certificate/couponWriteOffList/couponWriteOffList.vue"
          ),
        meta: {
          title: "优惠券核销列表",
        },
        show: true,
      },
    ],
  },
  {
    path: '/marketing',
    component: MMain,
    code: "marketing",
    meta: {
      title: '营销管理',
      icon: "el-icon-discount",
      isShow: 0
    },
    children: [
      {
        path: "marketingList",
        code: "marketingList",
        component: MContent,
        children: [
          {
            path: "",
            name: "MarketingList",
            component: () =>
              import("@/views/marketing/MarketingList.vue"),
          },
          {
            path: "addLuckyDraw",
            name: "addLuckyDraw",
            component: () =>
              import(
                "@/views/marketing/components/addLuckyDraw.vue"
              ),
            meta: {
              title: "新增抽奖活动",
            },
            show: true,
          },

        ],
        meta: {
          title: "抽奖活动方案",
        },
        show: true,
      },
      {
        path: "prizeDetail",
        name: "prizeDetail",
        code: "prizeDetail",
        component: () =>
          import("@/views/marketing/prizeDetail/Index.vue"),
        meta: {
          title: "抽奖活动明细",
        },
        show: true,
      },
      {
        path: "prizeSummary",
        name: "prizeSummary",
        code: "prizeSummary",
        component: () =>
          import("@/views/marketing/prizeSummary/Index.vue"),
        meta: {
          title: "抽奖活动汇总",
        },
        show: true,
      },
      {
        path: "memberLottery",
        name: "memberLottery",
        code: "memberLottery",
        component: () =>
          import("@/views/marketing/memberLottery/MemberLottery.vue"),
        meta: {
          title: "会员抽奖列表",
        },
        show: true,
      },
      {
        path: "fullGiftList",
        code: "fullGiftList",
        component: MContent,
        children: [
          {
            path: "",
            name: "FullGiftList",
            component: () =>
              import("@/views/marketing/fullGiftList/FullGiftList.vue"),
          },
          {
            path: "addFullGift",
            name: "addFullGift",
            component: () =>
              import(
                "@/views/marketing/fullGiftList/components/AddFullGift.vue"
              ),
            meta: {
              title: "新增满赠方案",
            },
            show: true,
          },

        ],
        meta: {
          title: "满赠方案",
        },
        show: true,
      },
      {
        path: "fullGiftListDetails",
        name: "fullGiftListDetails",
        component: () =>
          import(
            "@/views/marketing/fullGiftListDetails/FullGiftListDetails.vue"
          ),
        meta: {
          title: "满赠方案明细表",
        },
        show: true,
      },
    ]
  },
  {
    path: '/store',
    component: MMain,
    code: "store",
    meta: {
      title: '商家管理',
      icon: "el-icon-discount",
      isShow: 0
    },
    children: [
      {
        path: "storeList",
        code: "storeList",
        component: MContent,
        children: [
          {
            path: "",
            name: "StoreList",
            component: () =>
              import("@/views/store/storeList/storeList.vue"),
          },
          {
            path: "editStore",
            name: "EditStore",
            component: () =>
              import(
                "@/views/store/storeList/editStore.vue"
              ),
            meta: {
              title: "商家信息编辑",
            },
            show: true,
          },
        ],
        meta: {
          title: "商家信息",
        },
        show: true,
      }, {
        path: "entryList",
        code: "entryList",
        component: MContent,
        children: [{
          path: "",
          name: "EntryList",
          component: () =>
            import("@/views/store/entryList/entryList.vue"),
        },
        {
          path: "detailsStore",
          name: "DetailsStore",
          component: () =>
            import("@/views/store/entryList/detailsStore.vue"),
          meta: {
            title: "商家详情页面",
          },
          show: true,
        },
        ],
        meta: {
          title: "商家申请",
        },
        show: true,
      }, {
        path: "classification",
        code: "classification",
        component: MContent,
        children: [{
          path: "",
          name: "Classification",
          component: () =>
            import("@/views/store/classification/classification.vue"),
        },
        ],
        meta: {
          title: "商家分类",
        },
        show: true,
      },
      {
        path: "storeInfo",
        name: "storeInfo",
        code: "storeInfo",
        component: () =>
          import("@/views/store/storeInfo/DeliveryOrder.vue"),
        meta: {
          title: "预约信息",
        },
        show: true,
      },
      {
        path: "storeInfoPage",
        name: "storeInfoPage",
        code: "storeInfoPage",
        component: () =>
          import("@/views/store/storeInfoPage/DeliveryOrder.vue"),
        meta: {
          title: "预约评论",
        },
        show: true,
      },
      {
        path: "regionalMember",
        name: "regionalMember",
        code: "regionalMember",
        component: () =>
          import("@/views/store/regionalMember/RegionalMember.vue"),
        meta: {
          title: "区域会员",
        },
        show: true,
      },
    ]
  },
  {
    path: "/distribution",
    code: "distribution",
    component: MMain,
    meta: {
      title: "配送方式",
      icon: "icondaifahuo",
    },
    children: [
      {
        path: "logistics",
        code: "logistics",
        component: () =>
          import(
            /* webpackChunkName: "finance" */ "@/views/logistics/logistics/Index.vue"
          ),
        meta: {
          title: "快递配送",
        },
        show: true,
      },
    ],
  },
  {
    path: '/systemManage',
    code: 'systemManage',
    component: MMain,
    meta: {
      title: '系统管理',
      icon: "el-icon-receiving"
    },
    children: [
      {
        path: "menuInfo",
        code: 'menuInfo',
        component: MContent,
        children: [
          {
            path: "",
            name: "menuInfo",
            component: () =>
              import(
                "@/views/systemManage/menuInfo/MenuInfo.vue"
              ),
          }, {
            path: "buttonInfo",
            name: "buttonInfo",
            show: true,
            component: () =>
              import(
              /* webpackChunkName: "goods" */ "@/views/systemManage/menuInfo/buttonInfo/ButtonInfo.vue"
              ),
            meta: {
              title: "按钮权限",
            },
          }
        ],
        meta: {
          title: "菜单管理",
        },
        show: true,
      },
      {
        path: "roleInfo",
        code: 'roleInfo',
        component: MContent,
        children: [
          {
            path: "",
            name: "roleInfo",
            component: () =>
              import(
                "@/views/systemManage/roleInfo/roleInfo.vue"
              ),
          }
        ],
        meta: {
          title: "角色管理",
        },
        show: true,
      },
      {
        path: "page",
        code: 'pageInfo',
        component: MContent,
        children: [
          {
            path: "",
            name: "pageUser",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/platformUser/Index.vue"
              ),
          },
        ],
        meta: {
          title: "用户管理",
        },
        show: true,
      },
      {
        path: "userMenu",
        code: 'userMenu',
        component: MContent,
        children: [
          {
            path: "",
            name: "userMenu",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/systemManage/userMenu/userMenu.vue"
              ),
          },
        ],
        meta: {
          title: "租户菜单",
        },
        show: true,
      },
      {
        path: "department",
        code: 'department',
        component: MContent,
        children: [
          {
            path: "",
            name: "department",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/department/Index.vue"
              ),
          },
        ],
        meta: {
          title: "部门管理",
        },
        show: true,
      },
      {
        path: "employee",
        code: 'employee',
        component: MContent,
        children: [
          {
            path: "",
            name: "employee",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/employee/Index.vue"
              ),
          },
        ],
        meta: {
          title: "职员管理",
        },
        show: true,
      },
      {
        path: "storeFront",
        code: 'storeFront',
        component: MContent,
        children: [
          {
            path: "",
            name: "storeFront",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/storeFront/Index.vue"
              ),
          },
          {
            path: 'subordinateStore',
            name: 'SubordinateStore',
            code: 'subordinateStore',
            component: () =>
              import(
                "@/views/storeFront/subordinateStore/subordinateStore.vue"
              ),
            meta: {
              // title: "下属门店",
            },
            show: false,
          },
        ],
        meta: {
          title: "门店管理",
        },
        show: true,
      },
      {
        path: "positionTemp",
        code: 'positionTemp',
        component: MContent,
        children: [
          {
            path: "",
            name: "position",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/systemManage/position/templateIndex.vue"
              ),
          },
        ],
        meta: {
          title: "商家职位模版",
        },
        show: true,
      },
      {
        path: "position",
        code: 'position',
        component: MContent,
        children: [
          {
            path: "",
            name: "position",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/systemManage/position/Index.vue"
              ),
          },
        ],
        meta: {
          title: "商家职位",
        },
        show: true,
      },
      {
        path: "serviceContentTemplate",
        code: 'serviceContentTemplate',
        component: MContent,
        children: [
          {
            path: "",
            name: "serviceContentTemplate",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/systemManage/service/serviceContentTemplate.vue"
              ),
          },
        ],
        meta: {
          title: "服务项目模板",
        },
        show: true,
      },
      {
        path: "serviceContent",
        code: 'serviceContent',
        component: MContent,
        children: [
          {
            path: "",
            name: "serviceContent",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/systemManage/service/serviceContent.vue"
              ),
          },
        ],
        meta: {
          title: "服务项目",
        },
        show: true,
      },
      {
        path: "announcement",
        code: 'announcement',
        component: MContent,
        children: [
          {
            path: "",
            name: "announcement",
            component: () =>
              import(
                /* webpackChunkName: "systemManage" */ "@/views/systemManage/announcement/index.vue"
              ),
          },
        ],
        meta: {
          title: "系统公告",
        },
        show: true,
      },
      {
        path: "sseTest",
        code: 'sseTest',
        component: MContent,
        children: [
          {
            path: "",
            name: "sseTest",
            component: () =>
              import(
                /* webpackChunkName: "systemManage" */ "@/views/systemManage/sseTest/index.vue"
              ),
          },
        ],
        meta: {
          title: "SSE连接测试",
        },
        show: true,
      }
    ]
  },
  {
    path: "/setting",
    code: "setting",
    component: MMain,
    meta: {
      title: "商城设置",
      icon: "iconshangpinxiangqing-dianpu",
    },
    children: [
      {
        path: "editorPage",
        code: "editorPage",
        redirect: "/editorPage",
        component: () =>
          import(
            /* webpackChunkName: "setting" */ "@/views/decoration/components/EditorPage/src/Editor.vue"
          ),
        meta: {
          title: "装修",
          // isShow: 1,
        },
        show: true,
      },
      {
        path: "",
        name: "setting",
        code: "allSetting",
        component: () =>
          import(/* webpackChunkName: "setting" */ "@/views/setting/Index.vue"),
        meta: {
          title: "通用设置",
        },
        show: true,
      }, {
        path: "shopSetting",
        name: "shopSetting",
        code: "shopSetting",
        component: () =>
          import("@/views/shopSetting/Index.vue"),
        meta: {
          title: "模板设置",
        },
        show: true,
      }, {
        path: "wechatMessageTemplate",
        name: "wechatMessageTemplate",
        code: "wechatMessageTemplate",
        component: () =>
          import(
            "@/views/setting/WechatMessageTemplate.vue"
          ),
        meta: {
          title: "消息模板"
        },
        show: true
      },
      /*{
        path: "externalSystem",
        component: () =>
          import(
            "@/views/setting/ExternalSystem.vue"
            ),
        meta: {
          title: "外部系统信息",
        },
        show: true,
      },
      {
        path: "ExternalSystemDet",
        // name: "ExternalSystemDet",
        // path: "Inventory",
        component: MContent,
        // component: () =>
        //   import(
        //     "@/views/setting/ExternalSystemDet.vue"
        //     ),
        meta: {
          title: "外部系统信息明细",
        },
        show: true,
        children: [
          {
            path: "",
            name: "externalSystemDet",
            component: () =>
              import(
                 "@/views/setting/ExternalSystemDet.vue"
                ),
          },
          {
            path: "fieldRelationship",
            name: "FieldRelationship",
            component: () =>
              import(
                 "@/views/setting/FieldRelationship.vue"
                ),
            meta: {
              title: "字段映射关系",
            },
          },
        ],
      },*/




    ],
  },
  {
    path: "/logistics",
    component: MMain,
    meta: {
      title: "物流管理",
      isShow: 1,
    },
    children: [
      {
        path: "logistics",
        name: "logistics",
        component: () =>
          import(
            /* webpackChunkName: "logistics" */ "@/views/logistics/logistics/Index.vue"
          ),
        meta: {
          title: "快递配送",
        },
        show: true,
      },
    ],
  },
  {
    path: "/market",
    component: MMain,
    meta: {
      title: "商超系统",
      isShow: 1,
    },
    children: [
      {
        path: "goods",
        component: MContent,
        meta: {
          title: "商品列表",
        },
        show: true,
        children: [
          {
            path: "",
            name: "Goods",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/marketModel/Goods.vue"
              ),
          },
          {
            path: "addGood",
            name: "AddGood",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/marketModel/AddGood.vue"
              ),
            meta: {
              title: "发布商品",
            },
          },
        ],
      },
    ],
  },
  {
    path: "/CategoryTem",
    component: MMain,
    meta: {
      title: "商品属性",
      isShow: 1,
    },
    children: [
      {
        path: "/",
        name: "CategoryTem",
        component: () =>
          import(
            /* webpackChunkName: "CategoryTem" */ "@/views/goods/goodManage/components/CategoryTem.vue"
          ),
        meta: {
          title: "商品属性",
        },
        show: true,
      },
    ],
  },
  {
    path: "/business",
    component: MMain,
    meta: {
      title: "商家中心",
      isShow: 1,
    },
    children: [
      {
        path: "/",
        name: "businessCenter",
        component: () =>
          import(
            /* webpackChunkName: "businessCenter" */ "@/views/businessCenter/Index.vue"
          ),
        meta: {
          title: "",
        },
        show: true,
      },
    ],
  },
  {
    path: "/message",
    component: MMain,
    meta: {
      title: "消息中心",
      isShow: 1,
    },
    children: [
      {
        path: "/",
        name: "messageInfo",
        component: () =>
          import(
            /* webpackChunkName: "businessCenter" */ "@/views/messageInfo/Index.vue"
          ),
        meta: {
          title: "",
        },
        show: true,
      },
    ],
  },
  {
    path: "/editorPage",
    name: "editorPage",
    component: () =>
      import(
        /* webpackChunkName: "CategoryTem" */ "@/views/decoration/components/EditorPage/src/Editor.vue"
      ),
    meta: {
      title: "装修",
      isShow: 1,
    },
  },
  {
    path: "/changepass",
    name: "changepass",
    component: () =>
      import(
        /* webpackChunkName: "changepass" */ "@/views/businessCenter/Account/ChangePassword.vue"
      ),
    meta: {
      title: "修改密码",
      isShow: 1,
    },
  },
  {
    path: "/redirect/:type",
    name: "redirect",
    component: () =>
      import(/* webpackChunkName: "CategoryTem" */ "@/views/sign/Redirect.vue"),
    meta: {
      title: "重定向页面",
      isShow: 1,
    },
  },
  {
    path: "/static",
    component: MContent,
    meta: {
      title: "",
      isShow: 1,
    },
    children: [
      {
        path: "protocol",
        name: "protocol",
        component: () =>
          import(
            /* webpackChunkName: "meal" */ "@/views/businessCenter/Static/Protocol.vue"
          ),
        meta: {
          title: "注册协议",
        },
      },
      {
        path: "privacy",
        name: "privacy",
        component: () =>
          import(
            /* webpackChunkName: "meal" */ "@/views/businessCenter/Static/Privacy.vue"
          ),
        meta: {
          title: "隐私政策",
        },
      },
      {
        path: "order",
        name: "order",
        component: () =>
          import(
            /* webpackChunkName: "meal" */ "@/views/businessCenter/Static/Order.vue"
          ),
        meta: {
          title: "订购及服务协议",
        },
      },
      {
        path: "register",
        name: "register",
        component: () =>
          import(
            /* webpackChunkName: "meal" */ "@/views/businessCenter/Static/Register.vue"
          ),
        meta: {
          title: "开户及服务协议",
        },
      },
    ],
  },
  {
    path: "/",
    component: MMain,
    meta: {
      title: "商品管理",
      isShow: 1,
    },
    children: [
      {
        path: "",
        component: MContent,
        meta: {
          title: "商超商品",
        },
        show: true,
        children: [
          {
            path: "",
            name: "index",
            component: () =>
              import(
                /* webpackChunkName: "goods" */ "@/views/goods/marketModel/Goods.vue"
              ),
          },
        ],
      },
    ],
  },
  {
    path: "*",
    name: "404",
    component: () =>
      import(/* webpackChunkName: "404" */ "@/views/sign/404.vue"),
    meta: {
      title: "404",
      isShow: 1,
    },
  },
];

export default routes;
