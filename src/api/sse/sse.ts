import api from "@/libs/request";

/**
 * SSE连接相关API
 */

/**
 * 建立消息通知SSE连接
 * @param clientId 客户端ID
 */
export function connectMessageSSE(clientId: string) {
  // 返回SSE连接配置，包含URL和请求头
  const token = localStorage.getItem('token') || '';
  const baseUrl = process.env.VUE_APP_BASEURL || '';
  const url = `${baseUrl}/order-open/sse/connect?clientId=${clientId}`;

  return {
    url,
    headers: {
      'Token': 'platform:tk:eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.eyJqdGkiOiJmMjU0MTEwM2RjYzQ0ZjQ0OWFhYWY1NmNiMWY1NjUxNSIsInN1YiI6InBsYXRmb3JtIiwiaXNzIjoiYmduaWFvLmNvbSIsImlhdCI6MTc1Mzk0ODMyNywiZXhwIjoxNzUzOTU1NTI3fQ.wfwW2SXJ6fBGMLU3_Q6WDtaaNN4xNLQ3ABfVS3bzW1A',
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    }
  };
}

/**
 * 建立SSE连接（测试用）
 * @param clientId 客户端ID
 */
export function connectSSE(clientId: string) {
  return api.instance({
    url: `/order-open/sse/connect`,
    method: 'get',
    params: { clientId },
    responseType: 'stream',
    headers: {
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    }
  });
}

/**
 * 发送测试订单消息
 * @param orderId 订单ID
 */
export function sendTestOrderMessage(orderId: string) {
  return api.instance({
    url: `/order-open/mini/sse/getOrderPayMsg`,
    method: 'get',
    params: { orderId }
  });
}

/**
 * 获取SSE连接状态
 * @param clientId 客户端ID
 */
export function getSSEStatus(clientId: string) {
  return api.instance({
    url: `/order-open/mini/sse/status`,
    method: 'get',
    params: { clientId }
  });
} 