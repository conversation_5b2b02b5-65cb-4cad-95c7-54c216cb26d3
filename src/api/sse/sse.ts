import api from "@/libs/request";

/**
 * SSE连接相关API
 */

/**
 * 建立消息通知SSE连接
 * @param clientId 客户端ID
 */
export function connectMessageSSE(clientId: string) {
  // 注意：这里不使用api.instance，而是直接创建EventSource
  // 因为需要在组件中直接使用EventSource来监听事件
  const token = localStorage.getItem('token') || '';
  const baseUrl = process.env.VUE_APP_BASEURL || '';
  const url = token
    ? `${baseUrl}/order-open/sse/connect?token=${token}`
    : `${baseUrl}/order-open/sse/connect`;

  return url;
}

/**
 * 建立SSE连接（测试用）
 * @param clientId 客户端ID
 */
export function connectSSE(clientId: string) {
  return api.instance({
    url: `/order-open/sse/connect`,
    method: 'get',
    params: { clientId },
    responseType: 'stream',
    headers: {
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    }
  });
}

/**
 * 发送测试订单消息
 * @param orderId 订单ID
 */
export function sendTestOrderMessage(orderId: string) {
  return api.instance({
    url: `/order-open/mini/sse/getOrderPayMsg`,
    method: 'get',
    params: { orderId }
  });
}

/**
 * 获取SSE连接状态
 * @param clientId 客户端ID
 */
export function getSSEStatus(clientId: string) {
  return api.instance({
    url: `/order-open/mini/sse/status`,
    method: 'get',
    params: { clientId }
  });
} 