import api from "@/libs/request";

/**
 * SSE连接测试相关API
 */

/**
 * 建立SSE连接
 * @param clientId 客户端ID
 */
export function connectSSE(clientId: string) {
  return api.instance({
    url: `/order-open/mini/sse/connect`,
    method: 'get',
    params: { clientId },
    responseType: 'stream',
    headers: {
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    }
  });
}

/**
 * 发送测试订单消息
 * @param orderId 订单ID
 */
export function sendTestOrderMessage(orderId: string) {
  return api.instance({
    url: `/order-open/mini/sse/getOrderPayMsg`,
    method: 'get',
    params: { orderId }
  });
}

/**
 * 获取SSE连接状态
 * @param clientId 客户端ID
 */
export function getSSEStatus(clientId: string) {
  return api.instance({
    url: `/order-open/mini/sse/status`,
    method: 'get',
    params: { clientId }
  });
} 